import { Tables } from './supabase';

export type WorkoutType = 'Cardio' | 'Strength' | 'Flexibility' | 'Balance' | 'HIIT' | 'Push' | 'Pull' | 'Legs' | 'Core' | 'Full Body';
export type DifficultyLevel = 'beginner' | 'intermediate' | 'advanced';

// Enhanced Exercise type that extends the base type from Supabase
export interface Exercise extends Tables<'exercises'> {
  // Additional UI-specific properties can be added here
}

// Enhanced Workout type that extends the base type from Supabase
export interface Workout extends Tables<'workouts'> {
  exercises?: Exercise[];
  selected?: boolean; // Used in the UI for selection state
  exercise_count?: number; // Used in UI for display
}

export interface HIITWorkout extends Workout {
  work_periods: number;
  rest_periods: number;
  work_period_duration: number;
  rest_period_duration: number;
  rounds: number;
}

export interface WorkoutCategory {
  id: number;
  name: string;
  icon?: string;
  color?: string;
}

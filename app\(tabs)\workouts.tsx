import React, { useEffect, useState, useRef } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Image, ActivityIndicator, Animated, Dimensions, Platform } from 'react-native';
import { Stack, router } from 'expo-router';
import { supabase } from '../../lib/supabase';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { BlurView } from 'expo-blur';

interface Exercise {
    id: string;
    name: string;
    duration: string;
    reps: string;
    video_url?: string;
    image_url?: string;
    set_number: number;
}

interface Workout {
    id: number;
    name: string;
    type: string;
    difficulty: string;
    duration: number;
    description: string;
    exercise_count: number;
    calories_burned: number;
    exercises?: Exercise[];
    icon?: string;
}

export default function WorkoutsScreen() {
    const [workouts, setWorkouts] = useState<Workout[]>([]);
    const [filteredWorkouts, setFilteredWorkouts] = useState<Workout[]>([]);
    const [loading, setLoading] = useState(true);
    const [activeFilter, setActiveFilter] = useState('all');
    const scrollY = useRef(new Animated.Value(0)).current;

    useEffect(() => {
        fetchWorkouts();
    }, []);

    useEffect(() => {
        if (activeFilter === 'all') {
            setFilteredWorkouts(workouts);
        } else {
            setFilteredWorkouts(workouts.filter(workout => 
                workout.type.toLowerCase() === activeFilter.toLowerCase() ||
                workout.difficulty.toLowerCase() === activeFilter.toLowerCase()
            ));
        }
    }, [activeFilter, workouts]);

    const fetchWorkouts = async () => {
        try {
            const { data: workoutsData, error: workoutsError } = await supabase
                .from('workouts')
                .select(`
                    *,
                    exercises (*)
                `)
                .order('name');

            if (workoutsError) throw workoutsError;

            setWorkouts(workoutsData || []);
        } catch (error) {
            console.error('Error fetching workouts:', error);
        } finally {
            setLoading(false);
        }
    };

    const getDifficultyColor = (difficulty: string) => {
        switch (difficulty.toLowerCase()) {
            case 'beginner':
                return '#92A3FD';
            case 'intermediate':
                return '#FFA07A';
            case 'advanced':
                return '#FF4B4B';
            default:
                return '#92A3FD';
        }
    };

    const categories = [
        { id: 'all', name: 'All', icon: 'grid-outline' },
        { id: 'cardio', name: 'Cardio', icon: 'heart-outline' },
        { id: 'strength', name: 'Strength', icon: 'barbell-outline' },
        { id: 'beginner', name: 'Beginner', icon: 'leaf-outline' },
        { id: 'advanced', name: 'Advanced', icon: 'flame-outline' },
    ];

    const LoadingImage = ({ source, style }: { source: { uri: string }, style: any }) => {
        const [isLoading, setIsLoading] = useState(true);
        const [error, setError] = useState(false);
        const fadeAnim = useRef(new Animated.Value(0)).current;

        const onLoad = () => {
            Animated.timing(fadeAnim, {
                toValue: 1,
                duration: 300,
                useNativeDriver: true,
            }).start();
            setIsLoading(false);
        };

        return (
            <View style={[style, { justifyContent: 'center', alignItems: 'center', overflow: 'hidden' }]}>
                {isLoading && (
                    <View style={[StyleSheet.absoluteFill, { justifyContent: 'center', alignItems: 'center', backgroundColor: '#E0E5F2' }]}>
                        <ActivityIndicator size="small" color="#92A3FD" />
                    </View>
                )}
                <Animated.Image 
                    source={source}
                    style={[style, { opacity: fadeAnim }]}
                    onLoadStart={() => setIsLoading(true)}
                    onLoad={onLoad}
                    onError={() => setError(true)}
                />
                {error && (
                    <View style={[StyleSheet.absoluteFill, { justifyContent: 'center', alignItems: 'center', backgroundColor: '#E0E5F2' }]}>
                        <Ionicons name="image-outline" size={30} color="#92A3FD" />
                    </View>
                )}
            </View>
        );
    };

    const renderWorkoutCard = (workout: Workout, index: number) => {
        const isLastItem = index === filteredWorkouts.length - 1;
        
        return (
            <Animated.View
                key={workout.id}
                style={[
                    styles.workoutCard,
                    { marginBottom: isLastItem ? 100 : 16 },
                    { 
                        transform: [{ 
                            scale: scrollY.interpolate({
                                inputRange: [-100, 0, 100 * index, 100 * (index + 2)],
                                outputRange: [1, 1, 1, 0.95],
                                extrapolate: 'clamp'
                            }) 
                        }],
                        opacity: scrollY.interpolate({
                            inputRange: [100 * (index - 1), 100 * index, 100 * (index + 2)],
                            outputRange: [1, 1, 0.7],
                            extrapolate: 'clamp'
                        })
                    }
                ]}
            >
                <TouchableOpacity
                    activeOpacity={0.9}
                    onPress={() => router.push(`/workout/${workout.id}`)}
                    style={styles.cardTouchable}
                >
                    {workout.icon ? (
                        <LoadingImage 
                            source={{ uri: workout.icon }}
                            style={styles.workoutImage}
                        />
                    ) : (
                        <View style={[styles.workoutImage, { backgroundColor: '#E0E5F2', justifyContent: 'center', alignItems: 'center' }]}>
                            <Ionicons name="fitness-outline" size={40} color="#92A3FD" />
                        </View>
                    )}
                    
                    <View style={styles.cardOverlay}>
                        <BlurView intensity={20} style={styles.blurContainer}>
                            <View style={styles.workoutHeader}>
                                <Text style={styles.workoutName} numberOfLines={1}>{workout.name}</Text>
                                <View style={[
                                    styles.difficultyBadge,
                                    { backgroundColor: getDifficultyColor(workout.difficulty) }
                                ]}>
                                    <Text style={styles.difficultyText}>
                                        {workout.difficulty}
                                    </Text>
                                </View>
                            </View>

                            <View style={styles.workoutDetails}>
                                <View style={styles.detailItem}>
                                    <Ionicons name="time-outline" size={18} color="#fff" />
                                    <Text style={styles.detailText}>{workout.duration} Min</Text>
                                </View>
                                <View style={styles.detailItem}>
                                    <Ionicons name="fitness-outline" size={18} color="#fff" />
                                    <Text style={styles.detailText}>{workout.exercise_count} Übungen</Text>
                                </View>
                                <View style={styles.detailItem}>
                                    <Ionicons name="flame-outline" size={18} color="#fff" />
                                    <Text style={styles.detailText}>{workout.calories_burned} kcal</Text>
                                </View>
                            </View>
                        </BlurView>
                    </View>
                </TouchableOpacity>
            </Animated.View>
        );
    };

    return (
        <View style={styles.container}>
            <Stack.Screen
                options={{
                    title: 'Workouts',
                    headerStyle: {
                        backgroundColor: '#fff',
                    },
                    headerShadowVisible: false,
                }}
            />

            <View style={styles.categoriesContainer}>
                <ScrollView 
                    horizontal 
                    showsHorizontalScrollIndicator={false} 
                    contentContainerStyle={styles.categoriesScrollContent}
                >
                    {categories.map((category) => (
                        <TouchableOpacity
                            key={category.id}
                            style={[
                                styles.categoryButton,
                                activeFilter === category.id && styles.activeCategoryButton
                            ]}
                            onPress={() => setActiveFilter(category.id)}
                        >
                            <Ionicons 
                                name={category.icon as any} 
                                size={18} 
                                color={activeFilter === category.id ? '#fff' : '#92A3FD'} 
                            />
                            <Text style={[
                                styles.categoryText,
                                activeFilter === category.id && styles.activeCategoryText
                            ]}>
                                {category.name}
                            </Text>
                        </TouchableOpacity>
                    ))}
                </ScrollView>
            </View>

            {loading ? (
                <View style={styles.loaderContainer}>
                    <ActivityIndicator size="large" color="#92A3FD" />
                    <Text style={styles.loadingText}>Lädt Workouts...</Text>
                </View>
            ) : filteredWorkouts.length === 0 ? (
                <View style={styles.emptyContainer}>
                    <Ionicons name="fitness-outline" size={60} color="#92A3FD" />
                    <Text style={styles.noWorkoutsText}>Keine Workouts gefunden</Text>
                    {activeFilter !== 'all' && (
                        <TouchableOpacity 
                            style={styles.resetButton} 
                            onPress={() => setActiveFilter('all')}
                        >
                            <Text style={styles.resetButtonText}>Alle anzeigen</Text>
                        </TouchableOpacity>
                    )}
                </View>
            ) : (
                <Animated.ScrollView 
                    style={styles.scrollView}
                    contentContainerStyle={styles.scrollViewContent}
                    showsVerticalScrollIndicator={false}
                    scrollEventThrottle={16}
                    onScroll={Animated.event(
                        [{ nativeEvent: { contentOffset: { y: scrollY } } }],
                        { useNativeDriver: true }
                    )}
                >
                    {filteredWorkouts.map((workout, index) => renderWorkoutCard(workout, index))}
                </Animated.ScrollView>
            )}
        </View>
    );
}

const { width } = Dimensions.get('window');
const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#fff',
    },
    scrollView: {
        flex: 1,
    },
    scrollViewContent: {
        paddingHorizontal: 16,
        paddingTop: 8,
        paddingBottom: 20,
    },
    categoriesContainer: {
        paddingVertical: 12,
        borderBottomWidth: 1,
        borderBottomColor: '#F0F0F0',
    },
    categoriesScrollContent: {
        paddingHorizontal: 16,
        gap: 10,
    },
    categoryButton: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#F6F6F6',
        paddingHorizontal: 16,
        paddingVertical: 8,
        borderRadius: 20,
        gap: 6,
    },
    activeCategoryButton: {
        backgroundColor: '#92A3FD',
    },
    categoryText: {
        fontSize: 14,
        fontWeight: '500',
        color: '#92A3FD',
    },
    activeCategoryText: {
        color: '#fff',
    },
    loadingText: {
        textAlign: 'center',
        fontSize: 16,
        color: '#92A3FD',
        marginTop: 10,
        fontWeight: '500',
    },
    loaderContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    emptyContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        padding: 20,
    },
    noWorkoutsText: {
        textAlign: 'center',
        marginTop: 16,
        fontSize: 18,
        color: '#4F5E7B',
        fontWeight: '500',
    },
    resetButton: {
        marginTop: 16,
        backgroundColor: '#F6F6F6',
        paddingHorizontal: 20,
        paddingVertical: 10,
        borderRadius: 20,
    },
    resetButtonText: {
        color: '#92A3FD',
        fontWeight: '500',
    },
    workoutCard: {
        borderRadius: 24,
        overflow: 'hidden',
        elevation: 5,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        backgroundColor: '#fff',
        marginTop: 12,
        height: 220,
    },
    cardTouchable: {
        flex: 1,
    },
    cardGradient: {
        padding: 0,
        flex: 1,
    },
    workoutImage: {
        width: '100%',
        height: 220,
        borderRadius: 24,
    },
    cardOverlay: {
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
        borderBottomLeftRadius: 24,
        borderBottomRightRadius: 24,
        overflow: 'hidden',
    },
    blurContainer: {
        padding: 16,
        paddingBottom: 20,
    },
    workoutHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 12,
    },
    workoutName: {
        fontSize: 22,
        fontWeight: 'bold',
        color: '#fff',
        flex: 1,
        textShadowColor: 'rgba(0, 0, 0, 0.3)',
        textShadowOffset: { width: 0, height: 1 },
        textShadowRadius: 4,
    },
    difficultyBadge: {
        paddingHorizontal: 12,
        paddingVertical: 5,
        borderRadius: 12,
        marginLeft: 8,
    },
    difficultyText: {
        color: '#fff',
        fontSize: 13,
        fontWeight: '600',
    },
    workoutDetails: {
        flexDirection: 'row',
        justifyContent: 'flex-start',
        gap: 18,
    },
    detailItem: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 5,
    },
    detailText: {
        color: '#fff',
        fontSize: 14,
        fontWeight: '500',
        textShadowColor: 'rgba(0, 0, 0, 0.3)',
        textShadowOffset: { width: 0, height: 1 },
        textShadowRadius: 2,
    },
    workoutDescription: {
        color: '#fff',
        fontSize: 14,
        opacity: 0.9,
        marginTop: 10,
    },
});

import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  Animated,
  Alert,
  StatusBar,
  SectionList,
  Platform,
  Image,
  ActivityIndicator,
  ScrollView,
} from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
import { useSettings } from '../context/SettingsContext';
import { GestureHandlerRootView, Swipeable, RectButton } from 'react-native-gesture-handler';
import { supabase } from '@/lib/supabase';
import { LinearGradient } from 'expo-linear-gradient';
import { format, formatDistanceToNow, isToday, isYesterday, isThisWeek, parseISO } from 'date-fns';
import { BlurView } from 'expo-blur';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { <PERSON><PERSON><PERSON>iew } from 'moti';
import Lot<PERSON>View from 'lottie-react-native';

// Define animation sources statically - Metro bundler requires static paths
// If you have the animation files, uncomment these lines and add the files to your project
// const ANIMATIONS = {
//   emptyNotifications: require('../assets/animations/empty-notifications.json'),
//   loading: require('../assets/animations/loading.json'),
// };

// Check if animations exist
const hasAnimations = false; // Set to true when you add the animation files

// Define the Notification type
type Notification = {
  id: string;
  user_id: string | null;
  type: string;
  message: string;
  read: boolean | null;
  created_at: string;
  title?: string; // Optional title field
  action_url?: string; // Optional deep link
  data?: any; // Optional extra data
};

// Define section data structure
type Section = {
  title: string;
  data: Notification[];
};

export default function NotificationsScreen() {
  const router = useRouter();
  const { triggerHaptic } = useSettings();
  const insets = useSafeAreaInsets();
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(true);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const swipeableRefs = useRef<{ [key: string]: Swipeable | null }>({});
  const lottieRef = useRef<LottieView | null>(null);
  const scrollY = useRef(new Animated.Value(0)).current;
  const [filterType, setFilterType] = useState<string | null>(null);

  // Header opacity animation
  const headerOpacity = scrollY.interpolate({
    inputRange: [0, 50],
    outputRange: [0, 1],
    extrapolate: 'clamp',
  });

  // Title animation
  const titleScale = scrollY.interpolate({
    inputRange: [0, 100],
    outputRange: [1.1, 1],
    extrapolate: 'clamp',
  });

  const titleOpacity = scrollY.interpolate({
    inputRange: [0, 30, 100],
    outputRange: [0, 0.5, 1],
    extrapolate: 'clamp',
  });

  const fetchNotifications = useCallback(async (showLoading = true) => {
    if (showLoading) setLoading(true);
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        router.replace('/(auth)/login');
        return;
      }

      let query = supabase
        .from('notifications')
        .select('id, user_id, type, message, read, created_at, title, action_url, data')
        .eq('user_id', user.id);

      if (filterType) {
        query = query.eq('type', filterType);
      }

      const { data, error } = await query.order('created_at', { ascending: false });
      if (error) throw error;

      setNotifications(data || []);
    } catch (error) {
      console.error('Error fetching notifications:', error);
      Alert.alert('Error', 'Failed to load notifications. Please try again.');
    } finally {
      if (showLoading) setLoading(false);
      setRefreshing(false);
    }
  }, [router, filterType]);

  useEffect(() => {
    fetchNotifications();
  }, [fetchNotifications]);

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    // Remove animation code since we're not using it
    await fetchNotifications(false);
  }, [fetchNotifications]);

  const markAsRead = async (id: string, currentState?: boolean) => {
    const notification = notifications.find(n => n.id === id);
    if (!notification) return;
    
    // If currentState is provided, toggle read status, otherwise just mark as read
    const newReadState = currentState !== undefined ? !currentState : true;
    
    // Optimistic update
    setNotifications(prev =>
      prev.map(notif =>
        notif.id === id ? { ...notif, read: newReadState } : notif
      )
    );
    
    try {
      const { error } = await supabase
        .from('notifications')
        .update({ read: newReadState })
        .eq('id', id);

      if (error) throw error;
      
      triggerHaptic();
      swipeableRefs.current[id]?.close();
    } catch (error) {
      console.error('Error updating notification read status:', error);
      // Revert on error
      setNotifications(prev =>
        prev.map(notif =>
          notif.id === id ? { ...notif, read: !newReadState } : notif
        )
      );
    }
  };

  const deleteNotification = async (id: string) => {
    // Optimistic update
    const originalNotifications = [...notifications];
    setNotifications(prev => prev.filter(notif => notif.id !== id));
    triggerHaptic();

    try {
      const { error } = await supabase
        .from('notifications')
        .delete()
        .eq('id', id);

      if (error) throw error;
    } catch (error) {
      console.error('Error deleting notification:', error);
      setNotifications(originalNotifications);
      Alert.alert('Error', 'Failed to delete notification.');
    }
  };

  const markAllAsRead = async () => {
    // Check if there are any unread notifications
    const hasUnread = notifications.some(n => !n.read);
    if (!hasUnread) {
      Alert.alert('No unread notifications', 'All notifications are already marked as read.');
      return;
    }

    // Optimistic update
    const originalNotifications = [...notifications];
    setNotifications(prev => prev.map(notif => ({ ...notif, read: true })));
    triggerHaptic();

    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        setNotifications(originalNotifications);
        router.replace('/(auth)/login');
        return;
      }

      const { error } = await supabase
        .from('notifications')
        .update({ read: true })
        .eq('user_id', user.id)
        .eq('read', false);

      if (error) throw error;
    } catch (error) {
      console.error('Error marking all as read:', error);
      setNotifications(originalNotifications);
      Alert.alert('Error', 'Failed to mark all notifications as read.');
    }
  };

  const clearAllNotifications = () => {
    if (notifications.length === 0) {
      Alert.alert('No notifications', 'There are no notifications to clear.');
      return;
    }

    Alert.alert(
      'Clear All Notifications',
      'Are you sure you want to delete all notifications? This cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear All',
          style: 'destructive',
          onPress: async () => {
            const originalNotifications = [...notifications];
            setNotifications([]);
            triggerHaptic();

            try {
              const { data: { user } } = await supabase.auth.getUser();
              if (!user) {
                setNotifications(originalNotifications);
                router.replace('/(auth)/login');
                return;
              }

              const { error } = await supabase
                .from('notifications')
                .delete()
                .eq('user_id', user.id);

              if (error) throw error;
            } catch (error) {
              console.error('Error clearing notifications:', error);
              setNotifications(originalNotifications);
              Alert.alert('Error', 'Failed to clear notifications.');
            }
          },
        },
      ]
    );
  };

  // Group notifications by date sections
  const getSections = useCallback(() => {
    if (!notifications.length) return [];
    
    const today: Notification[] = [];
    const yesterday: Notification[] = [];
    const thisWeek: Notification[] = [];
    const earlier: Notification[] = [];

    notifications.forEach(notification => {
      const date = parseISO(notification.created_at);
      if (isToday(date)) {
        today.push(notification);
      } else if (isYesterday(date)) {
        yesterday.push(notification);
      } else if (isThisWeek(date)) {
        thisWeek.push(notification);
      } else {
        earlier.push(notification);
      }
    });
    
    const sections: Section[] = [];
    if (today.length) sections.push({ title: 'Today', data: today });
    if (yesterday.length) sections.push({ title: 'Yesterday', data: yesterday });
    if (thisWeek.length) sections.push({ title: 'This Week', data: thisWeek });
    if (earlier.length) sections.push({ title: 'Earlier', data: earlier });
    
    return sections;
  }, [notifications]);

  // --- Icon and Color Logic ---
  const getNotificationIcon = (type: string): keyof typeof Ionicons.glyphMap | keyof typeof MaterialCommunityIcons.glyphMap => {
    switch (type?.toLowerCase()) {
      case 'workout': return 'fitness';
      case 'water': return 'water';
      case 'period': return 'calendar-clock';
      case 'andree-workout': return 'dumbbell';
      case 'goal': return 'flag';
      case 'achievement': return 'trophy';
      case 'reminder': return 'alarm';
      case 'announcement': return 'bullhorn';
      case 'system': return 'cog';
      default: return 'bell';
    }
  };

  const getNotificationColor = (type: string): string[] => {
    switch (type?.toLowerCase()) {
      case 'workout': return ['#8E2DE2', '#4A00E0'];
      case 'water': return ['#2193b0', '#6dd5ed'];
      case 'period': return ['#FF416C', '#FF4B2B'];
      case 'andree-workout': return ['#FF9966', '#FF5E62'];
      case 'goal': return ['#fccb0b', '#fc9a0b'];
      case 'achievement': return ['#11998e', '#38ef7d'];
      case 'reminder': return ['#00C9FF', '#92FE9D'];
      case 'announcement': return ['#F857A6', '#FF5858'];
      case 'system': return ['#4e54c8', '#8f94fb'];
      default: return ['#6c757d', '#adb5bd'];
    }
  };

  // --- Get filter types from available notifications
  const getFilterTypes = useCallback(() => {
    if (!notifications.length) return [];
    
    const types = Array.from(new Set(notifications.map(n => n.type)));
    return types.sort();
  }, [notifications]);

  const handleFilterPress = (type: string | null) => {
    setFilterType(prevType => prevType === type ? null : type);
  };

  // --- Render Type Filters ---
  const renderTypeFilters = () => {
    const types = getFilterTypes();
    if (!types.length) return null;
    
    return (
      <MotiView
        from={{ opacity: 0, translateY: -10 }}
        animate={{ opacity: 1, translateY: 0 }}
        transition={{ type: 'timing', duration: 300 }}
        style={styles.filterContainer}
      >
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.filterScroll}
        >
          <TouchableOpacity
            style={[
              styles.filterChip,
              filterType === null && styles.activeFilterChip
            ]}
            onPress={() => handleFilterPress(null)}
          >
            <Text style={[
              styles.filterText,
              filterType === null && styles.activeFilterText
            ]}>
              All
            </Text>
          </TouchableOpacity>

          {types.map(type => (
            <TouchableOpacity
              key={type}
              style={[
                styles.filterChip,
                filterType === type && styles.activeFilterChip
              ]}
              onPress={() => handleFilterPress(type)}
            >
              <LinearGradient
                colors={getNotificationColor(type)}
                style={styles.filterIcon}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
              >
                <MaterialCommunityIcons
                  name={getNotificationIcon(type) as any}
                  size={12}
                  color="#fff"
                />
              </LinearGradient>
              <Text style={[
                styles.filterText,
                filterType === type && styles.activeFilterText
              ]}>
                {type.charAt(0).toUpperCase() + type.slice(1).replace('-', ' ')}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </MotiView>
    );
  };

  // --- Swipe Actions ---
  const renderRightActions = (
    progress: Animated.AnimatedInterpolation<number>,
    dragX: Animated.AnimatedInterpolation<number>,
    item: Notification
  ) => {
    const scale = dragX.interpolate({
      inputRange: [-80, 0],
      outputRange: [1, 0],
      extrapolate: 'clamp',
    });

    // Different action based on read status
    const readAction = (
      <RectButton
        style={[
          styles.rightAction,
          { backgroundColor: item.read ? '#2196F3' : '#8BC34A' }
        ]}
        onPress={() => markAsRead(item.id, item.read)}
      >
        <Animated.View style={[styles.actionIcon, { transform: [{ scale }] }]}>
          <MaterialCommunityIcons 
            name={item.read ? 'email-mark-as-unread' : 'email-check'} 
            size={24} 
            color="#fff" 
          />
          <Animated.Text style={[styles.actionText, { transform: [{ scale }] }]}>
            {item.read ? 'Unread' : 'Read'}
          </Animated.Text>
        </Animated.View>
      </RectButton>
    );

    const deleteAction = (
      <RectButton
        style={[styles.rightAction, { backgroundColor: '#FF3B30' }]}
        onPress={() => deleteNotification(item.id)}
      >
        <Animated.View style={[styles.actionIcon, { transform: [{ scale }] }]}>
          <MaterialCommunityIcons name="trash-can-outline" size={24} color="#fff" />
          <Animated.Text style={[styles.actionText, { transform: [{ scale }] }]}>
            Delete
          </Animated.Text>
        </Animated.View>
      </RectButton>
    );

    return (
      <View style={styles.rightActions}>
        {readAction}
        {deleteAction}
      </View>
    );
  };

  // --- Render Notification Item ---
  const renderNotificationItem = ({ item }: { item: Notification }) => (
    <Swipeable
      ref={ref => swipeableRefs.current[item.id] = ref}
      renderRightActions={(progress, dragX) =>
        renderRightActions(progress, dragX, item)
      }
      friction={2}
      rightThreshold={40}
      overshootRight={false}
      onSwipeableOpen={() => triggerHaptic('medium')}
    >
      <MotiView
        from={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{
          type: 'timing',
          duration: 300,
          delay: Math.random() * 300 // Staggered animation
        }}
      >
        <TouchableOpacity
          style={[
            styles.notificationItemContainer,
            !item.read && styles.unreadBackground
          ]}
          activeOpacity={0.7}
          onPress={() => markAsRead(item.id)}
        >
          <View style={styles.notificationItem}>
            {/* Enhanced icon with subtle animation */}
            <MotiView
              animate={{ scale: !item.read ? [1, 1.05, 1] : 1 }}
              transition={{
                loop: !item.read,
                type: 'timing',
                duration: 2000,
                repeatReverse: true
              }}
            >
              <LinearGradient
                colors={getNotificationColor(item.type)}
                style={styles.notificationIcon}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
              >
                <MaterialCommunityIcons
                  name={getNotificationIcon(item.type) as any}
                  size={22}
                  color="#fff"
                />
              </LinearGradient>
            </MotiView>

            <View style={styles.notificationContent}>
              <View style={styles.notificationHeader}>
                <Text style={styles.notificationTitle} numberOfLines={1}>
                  {item.title || 
                    (item.type ? 
                      (item.type.charAt(0).toUpperCase() + item.type.slice(1)).replace('-', ' ') : 
                      'Notification')}
                </Text>
                <Text style={styles.notificationTime}>
                  {formatDistanceToNow(parseISO(item.created_at), { addSuffix: true })}
                </Text>
              </View>

              <Text style={styles.notificationMessage}>
                {item.message}
              </Text>
              
              {/* Conditional action button */}
              {item.action_url && (
                <TouchableOpacity
                  style={styles.actionButton}
                  onPress={() => {
                    triggerHaptic();
                    markAsRead(item.id);
                    // Handle action URL - could be deep linking
                    router.push(item.action_url as any);
                  }}
                >
                  <Text style={styles.actionButtonText}>View Details</Text>
                  <MaterialCommunityIcons name="chevron-right" size={16} color="#6B8CFF" />
                </TouchableOpacity>
              )}
            </View>
            
            {/* Unread indicator */}
            {!item.read && <View style={styles.unreadIndicator} />}
          </View>
        </TouchableOpacity>
      </MotiView>
    </Swipeable>
  );

  // --- Section Header ---
  const renderSectionHeader = ({ section }: { section: Section }) => (
    <View style={styles.sectionHeader}>
      <Text style={styles.sectionHeaderText}>{section.title}</Text>
      <View style={styles.sectionDivider} />
    </View>
  );

  // --- Menu Options ---
  const handleMenuPress = () => {
    triggerHaptic();
    
    const options = [
      {
        text: "Mark All as Read",
        onPress: markAllAsRead,
        disabled: notifications.every(n => n.read)
      },
      {
        text: "Clear All Notifications",
        onPress: clearAllNotifications,
        style: "destructive",
        disabled: notifications.length === 0
      },
      {
        text: "Filter by Type",
        onPress: () => {}, // This will just close the menu since filters are visible in UI
      },
      {
        text: "Cancel",
        style: "cancel"
      }
    ];
    
    // Filter out disabled options
    const filteredOptions = options.filter(o => !o.disabled);
    
    Alert.alert(
      "Notification Options",
      "",
      filteredOptions.map(option => ({
        text: option.text,
        onPress: option.onPress,
        style: option.style as any
      })),
      { cancelable: true }
    );
  };

  // --- Empty State ---
  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <MotiView
        from={{ opacity: 0, translateY: 20 }}
        animate={{ opacity: 1, translateY: 0 }}
        transition={{ type: 'timing', duration: 800 }}
        style={styles.emptyIconContainer}
      >
        {/* Using static fallback icon since animation files aren't available */}
        <MaterialCommunityIcons 
          name="bell-off-outline" 
          size={80} 
          color="#CCCCCC" 
        />
      </MotiView>
      
      <MotiView
        from={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ type: 'timing', duration: 800, delay: 300 }}
      >
        <Text style={styles.emptyStateText}>All Caught Up!</Text>
      </MotiView>
      
      <MotiView
        from={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ type: 'timing', duration: 800, delay: 600 }}
      >
        <Text style={styles.emptyStateSubtext}>
          You don't have any notifications yet.
          {'\n'}We'll notify you when something important happens.
        </Text>
      </MotiView>
    </View>
  );

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <Stack.Screen options={{ headerShown: false }} />
      <View style={[styles.safeArea, { paddingTop: insets.top }]}>
        <StatusBar barStyle="dark-content" backgroundColor="#fff" />

        {/* Blurred Header Background */}
        <Animated.View
          style={[
            styles.headerBackground, 
            { opacity: headerOpacity, paddingTop: insets.top }
          ]}
        >
          <BlurView intensity={90} style={StyleSheet.absoluteFill} />
        </Animated.View>

        {/* Custom Header */}
        <View style={styles.customHeader}>
          <View style={styles.headerRow}>
            <TouchableOpacity
              style={styles.headerButton}
              onPress={() => {
                triggerHaptic();
                if (router.canGoBack()) {
                  router.back();
                } else {
                  router.replace('/(tabs)');
                }
              }}
            >
              <BlurView intensity={80} style={styles.headerButtonCircle}>
                <Ionicons name="chevron-back" size={24} color="#333" />
              </BlurView>
            </TouchableOpacity>

            <Animated.Text 
              style={[
                styles.headerTitle,
                { 
                  transform: [{ scale: titleScale }],
                  opacity: titleOpacity 
                }
              ]}
            >
              Notifications
            </Animated.Text>

            <TouchableOpacity style={styles.headerButton} onPress={handleMenuPress}>
              <BlurView intensity={80} style={styles.headerButtonCircle}>
                <Ionicons name="ellipsis-horizontal" size={20} color="#333" />
              </BlurView>
            </TouchableOpacity>
          </View>
        </View>

        {/* Content Area */}
        {loading ? (
          <View style={styles.centered}>
            <ActivityIndicator size="large" color="#6B8CFF" />
          </View>
        ) : (
          <View style={styles.contentContainer}>
            {/* Type Filters */}
            {notifications.length > 0 && renderTypeFilters()}
            
            {/* Notification List */}
            <SectionList
              sections={getSections()}
              keyExtractor={(item) => item.id}
              renderItem={renderNotificationItem}
              renderSectionHeader={renderSectionHeader}
              stickySectionHeadersEnabled={true}
              style={styles.container}
              contentContainerStyle={styles.scrollContent}
              onScroll={Animated.event(
                [{ nativeEvent: { contentOffset: { y: scrollY } } }],
                { useNativeDriver: false }
              )}
              ListHeaderComponent={() => (
                <MotiView
                  from={{ opacity: 0, translateY: -20 }}
                  animate={{ opacity: 1, translateY: 0 }}
                  transition={{ type: 'timing', duration: 500 }}
                  style={styles.listHeader}
                >
                  <Text style={styles.welcomeTitle}>Your Notifications</Text>
                  <Text style={styles.welcomeSubtitle}>
                    {notifications.length > 0 
                      ? `You have ${notifications.filter(n => !n.read).length} unread notifications`
                      : 'Stay updated with your latest activities'}
                  </Text>
                </MotiView>
              )}
              refreshControl={
                <RefreshControl
                  refreshing={refreshing}
                  onRefresh={onRefresh}
                  colors={["#6B8CFF"]}
                  tintColor={"#6B8CFF"}
                  progressViewOffset={20}
                />
              }
              ListEmptyComponent={renderEmptyState}
            />
          </View>
        )}
      </View>
    </GestureHandlerRootView>
  );
}

// --- Styles ---
const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  container: {
    flex: 1,
    backgroundColor: '#F8F9FF',
  },
  contentContainer: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 30,
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F8F9FF',
  },
  loadingAnimation: {
    width: 150,
    height: 150,
  },

  // --- Header Styles ---
  headerBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 60,
    zIndex: 10,
  },
  customHeader: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    zIndex: 10,
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    height: 48,
  },
  headerButton: {
    padding: 6,
  },
  headerButtonCircle: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
    backgroundColor: 'rgba(240, 240, 240, 0.8)',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1C1C1E',
  },

  // --- List Header ---
  listHeader: {
    padding: 16,
    paddingTop: 20,
    paddingBottom: 10,
  },
  welcomeTitle: {
    fontSize: 28,
    fontWeight: '700',
    color: '#1C1C1E',
    marginBottom: 8,
  },
  welcomeSubtitle: {
    fontSize: 16,
    color: '#6B7280',
  },

  // --- Section Header ---
  sectionHeader: {
    backgroundColor: '#F8F9FF',
    paddingHorizontal: 16,
    paddingTop: 20,
    paddingBottom: 10,
    flexDirection: 'row',
    alignItems: 'center',
  },
  sectionHeaderText: {
    fontSize: 15,
    fontWeight: '600',
    color: '#6B7280',
    marginRight: 10,
  },
  sectionDivider: {
    height: 1,
    backgroundColor: '#E5E7EB',
    flex: 1,
  },

  // --- Filter Styles ---
  filterContainer: {
    paddingVertical: 12,
    backgroundColor: '#F8F9FF',
  },
  filterScroll: {
    paddingHorizontal: 16,
    paddingRight: 24,
  },
  filterChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: '#F0F2F5',
    borderRadius: 100,
    marginRight: 8,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  activeFilterChip: {
    backgroundColor: '#E6EFFE',
    borderColor: '#6B8CFF',
  },
  filterIcon: {
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 6,
  },
  filterText: {
    fontSize: 13,
    color: '#4B5563',
    fontWeight: '500',
  },
  activeFilterText: {
    color: '#6B8CFF',
    fontWeight: '600',
  },

  // --- Notification Item Styles ---
  notificationItemContainer: {
    marginHorizontal: 16,
    marginVertical: 6,
    borderRadius: 16,
    backgroundColor: '#fff',
    shadowColor: '#8A959E',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 3,
    overflow: 'hidden',
    borderWidth: Platform.OS === 'ios' ? 0 : 1,
    borderColor: '#EAEAEA',
  },
  unreadBackground: {
    backgroundColor: '#F0F7FF',
  },
  notificationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  notificationIcon: {
    width: 46,
    height: 46,
    borderRadius: 23,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 14,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  notificationContent: {
    flex: 1,
    justifyContent: 'center',
    marginRight: 8,
  },
  notificationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  notificationTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1C1C1E',
    flexShrink: 1,
    marginRight: 8,
  },
  notificationMessage: {
    fontSize: 14,
    color: '#4B5563',
    lineHeight: 20,
    marginBottom: 6,
  },
  notificationTime: {
    fontSize: 12,
    color: '#6B7280',
    fontWeight: '500',
    flexShrink: 0,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-start',
    paddingVertical: 6,
    marginTop: 4,
  },
  actionButtonText: {
    fontSize: 14,
    color: '#6B8CFF',
    fontWeight: '500',
    marginRight: 4,
  },
  unreadIndicator: {
    width: 4,
    borderRadius: 2,
    backgroundColor: '#6B8CFF',
    position: 'absolute',
    left: 0,
    top: '20%',
    bottom: '20%',
  },

  // --- Swipe Action Styles ---
  rightActions: {
    width: 160,
    flexDirection: 'row',
  },
  rightAction: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  actionIcon: {
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    height: '100%',
  },
  actionText: {
    color: '#fff',
    fontSize: 12,
    backgroundColor: 'transparent',
    fontWeight: '600',
    marginTop: 4,
  },

  // --- Empty State Styles ---
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 30,
    marginTop: 30,
  },
  emptyIconContainer: {
    marginBottom: 20,
  },
  emptyAnimation: {
    width: 180,
    height: 180,
  },
  emptyStateText: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1C1C1E',
    textAlign: 'center',
    marginBottom: 12,
  },
  emptyStateSubtext: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 24,
  }
});
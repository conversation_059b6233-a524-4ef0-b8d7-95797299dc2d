// This configuration extends the React Native ESLint config
// ESLint 8.x is required for compatibility with React Native ESLint plugins
module.exports = {
  root: true,
  extends: '@react-native',
  ignorePatterns: [
    'node_modules/',
    'dist/',
    'build/',
    '.expo/',
    'web-build/'
  ],
  // Silence specific rules that might cause issues with React Native
  rules: {
    'react-native/no-inline-styles': 'warn',
    'react-hooks/exhaustive-deps': 'warn',
    'no-shadow': 'off',
    '@typescript-eslint/no-shadow': 'warn'
  },
  // Environments
  env: {
    'react-native/react-native': true,
    'jest': true
  }
};

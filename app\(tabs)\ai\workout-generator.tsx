import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  StatusBar,
  Dimensions,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';

const { width } = Dimensions.get('window');

export default function WorkoutGenerator() {
  const insets = useSafeAreaInsets();
  const router = useRouter();
  const [isGenerating, setIsGenerating] = useState(false);
  
  // Add any other state you need
  const [selectedLevel, setSelectedLevel] = useState('beginner');
  const [selectedGoal, setSelectedGoal] = useState('strength');
  const [selectedDuration, setSelectedDuration] = useState('30');

  const generateWorkout = () => {
    setIsGenerating(true);
    
    // Simulate API call
    setTimeout(() => {
      setIsGenerating(false);
      router.push('/workout-result');
    }, 2000);
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" />
      
      {/* Header with back button */}
      <LinearGradient
        colors={['#FFFFFF', '#F8F9FF']}
        style={[
          styles.header,
          { paddingTop: Math.max(insets.top, 40) }
        ]}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <Ionicons name="arrow-back" size={24} color="#333" />
          </TouchableOpacity>
          
          <Text style={styles.headerTitle}>AI Workout Generator</Text>
          
          <View style={{ width: 40 }} />
        </View>
      </LinearGradient>

      <ScrollView 
        style={styles.content}
        contentContainerStyle={[
          styles.scrollContent,
          { paddingBottom: 100 } // Simple bottom padding for content
        ]}
        showsVerticalScrollIndicator={false}
      >
        {/* Your form content here */}
      </ScrollView>
      
      {/* Generate Button - Fixed JSX structure */}
      <View style={[
        styles.generateButtonContainer, 
        { paddingBottom: insets.bottom + 20 }
      ]}>
        <TouchableOpacity
          onPress={generateWorkout}
          disabled={isGenerating}
          style={styles.generateButton}
          activeOpacity={0.8}
        >
          <LinearGradient
            colors={['#FF6B9C', '#FF4B8C']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={styles.generateGradient}
          >
            <View style={styles.generateContent}>
              <Ionicons name="fitness" size={20} color="#fff" style={{ marginRight: 8 }} />
              <Text style={styles.generateText}>
                {isGenerating ? 'Creating...' : 'Generate Workout'}
              </Text>
            </View>
          </LinearGradient>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FF',
  },
  header: {
    paddingBottom: 15,
    borderBottomRightRadius: 20,
    borderBottomLeftRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 10,
    elevation: 5,
    zIndex: 10,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#333',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(0,0,0,0.03)',
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    padding: 20,
    paddingBottom: 100,
  },
  generateButtonContainer: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 80, // Position above the tab bar
    paddingHorizontal: 20,
    paddingTop: 10,
    backgroundColor: '#F8F9FF',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.06,
    shadowRadius: 8,
    elevation: 10,
    zIndex: 10,
  },
  generateButton: {
    borderRadius: 16,
    overflow: 'hidden',
  },
  generateGradient: {
    paddingVertical: 18,
    alignItems: 'center',
    justifyContent: 'center',
  },
  generateContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  generateText: {
    fontSize: 18,
    fontWeight: '600',
    color: 'white',
  },
});

.datepicker-wrapper {
    width: 100%;
}

.datepicker-popper {
    z-index: 9999 !important;
}

.datepicker-calendar {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.react-datepicker {
    border: none !important;
    font-family: inherit;
}

.react-datepicker__header {
    background-color: #fff !important;
    border-bottom: 1px solid #e2e8f0;
    padding: 16px;
}

.react-datepicker__current-month {
    color: #1a202c;
    font-weight: 600;
    font-size: 1rem;
}

.react-datepicker__navigation {
    top: 16px;
}

.react-datepicker__day-name {
    color: #718096;
    font-weight: 500;
}

.react-datepicker__day {
    color: #4a5568;
    border-radius: 50%;
    width: 2rem;
    line-height: 2rem;
    margin: 0.2rem;
}

.react-datepicker__day:hover {
    background-color: #edf2ff;
}

.react-datepicker__day--selected {
    background-color: #7C9EFF !important;
    color: white !important;
}

.react-datepicker__day--keyboard-selected {
    background-color: #7C9EFF !important;
    color: white !important;
}

.react-datepicker__year-dropdown,
.react-datepicker__month-dropdown {
    background-color: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.react-datepicker__year-option:hover,
.react-datepicker__month-option:hover {
    background-color: #edf2ff;
}

.react-datepicker__year-option--selected,
.react-datepicker__month-option--selected {
    background-color: #7C9EFF !important;
    color: white !important;
}

.react-datepicker__day--today {
    font-weight: bold;
    color: #7C9EFF;
}

import React, { useRef, useEffect } from 'react';
import { Text, TextStyle, Platform, Animated } from 'react-native';

interface StyledTextProps {
  variant?: 'display' | 'title' | 'subtitle' | 'body' | 'caption';
  children: React.ReactNode;
  style?: TextStyle;
  animated?: boolean;
  animation?: {
    type?: 'fadeIn' | 'pulse';
    duration?: number;
    delay?: number;
  };
}

export function StyledText({ 
  variant = 'body', 
  children, 
  style, 
  animated = false,
  animation = { type: 'fadeIn', duration: 300, delay: 0 } 
}: StyledTextProps) {
  const baseStyle: TextStyle = {
    fontFamily: Platform.select({
      ios: 'SF Pro Display',
      android: 'sans-serif',
    }),
    letterSpacing: -0.3,
  };

  const variants: Record<string, TextStyle> = {
    display: {
      fontSize: 36,
      fontWeight: '700',
      letterSpacing: -1,
    },
    title: {
      fontSize: 24,
      fontWeight: '600',
      letterSpacing: -0.5,
    },
    subtitle: {
      fontSize: 18,
      fontWeight: '500',
    },
    body: {
      fontSize: 16,
      fontWeight: '400',
    },
    caption: {
      fontSize: 14,
      fontWeight: '400',
      opacity: 0.8,
    },
  };

  // Animation values
  const opacity = useRef(new Animated.Value(animated ? 0 : 1)).current;
  
  useEffect(() => {
    if (animated) {
      if (animation.type === 'fadeIn') {
        Animated.timing(opacity, {
          toValue: 1,
          duration: animation.duration,
          delay: animation.delay,
          useNativeDriver: true,
        }).start();
      } else if (animation.type === 'pulse') {
        Animated.loop(
          Animated.sequence([
            Animated.timing(opacity, {
              toValue: 0.6,
              duration: animation.duration! / 2,
              useNativeDriver: true,
            }),
            Animated.timing(opacity, {
              toValue: 1,
              duration: animation.duration! / 2,
              useNativeDriver: true,
            })
          ])
        ).start();
      }
    }
  }, [animated, animation]);

  return animated ? (
    <Animated.Text style={[baseStyle, variants[variant], style, { opacity }]}>
      {children}
    </Animated.Text>
  ) : (
    <Text style={[baseStyle, variants[variant], style]}>
      {children}
    </Text>
  );
}

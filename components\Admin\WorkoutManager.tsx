// src/components/WorkoutManager.tsx
import React, { useState, useEffect } from "react";
import { View, Text, FlatList, TouchableOpacity, ActivityIndicator, Alert, SafeAreaView } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { Ionicons } from "@expo/vector-icons";
import { supabase } from "../../lib/supabase";
import WorkoutCard from "./workout/WorkoutCard";
import WorkoutForm from "./workout/WorkoutForm";
import QuickAddModal from "./workout/QuickAddModal";
import styles from "./workout/styles";

const WorkoutManager: React.FC<{ refreshWorkouts: () => Promise<void> }> = ({ refreshWorkouts }) => {
  const [workouts, setWorkouts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [workoutFormVisible, setWorkoutFormVisible] = useState(false);
  const [currentWorkout, setCurrentWorkout] = useState<any>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [quickAddModalVisible, setQuickAddModalVisible] = useState(false);

  useEffect(() => {
    fetchWorkouts();
  }, []);

  const fetchWorkouts = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from("workouts")
        .select(`*, exercises (*)`)
        .order("name");
      if (error) throw error;
      setWorkouts(data || []);
    } catch (error) {
      console.error("Error fetching workouts:", error);
      Alert.alert("Error", "Workouts konnten nicht geladen werden.");
    } finally {
      setLoading(false);
    }
  };

  const handleWorkoutPress = (workout: any) => {
    setIsEditing(true);
    setCurrentWorkout(workout);
    setWorkoutFormVisible(true);
  };

  const handleCreateWorkout = () => {
    setIsEditing(false);
    setCurrentWorkout(null);
    setWorkoutFormVisible(true);
  };

  const handleFormClose = async () => {
    setWorkoutFormVisible(false);
    await fetchWorkouts();
    refreshWorkouts();
  };

  return (
    <SafeAreaView style={{ flex: 1 }}>
      {loading ? (
        <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
          <ActivityIndicator size="large" color="#92A3FD" />
          <Text>Workouts werden geladen...</Text>
        </View>
      ) : workouts.length > 0 ? (
        <FlatList
          data={workouts}
          renderItem={({ item }) => (
            <WorkoutCard workout={item} onPress={() => handleWorkoutPress(item)} />
          )}
          keyExtractor={(item) => item.id.toString()}
          contentContainerStyle={{ padding: 16 }}
        />
      ) : (
        <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
          <Ionicons name="fitness-outline" size={64} color="#C5CEE0" />
          <Text>Keine Workouts gefunden</Text>
          <Text>Erstelle dein erstes Workout mit dem Button unten</Text>
          <TouchableOpacity onPress={handleCreateWorkout}>
            <LinearGradient
              colors={["#92A3FD", "#9DCEFF"]}
              style={{ flexDirection: "row", alignItems: "center", padding: 16, borderRadius: 12 }}
            >
              <Ionicons name="add-circle-outline" size={24} color="#fff" />
              <Text style={{ marginLeft: 10, color: "#fff" }}>Workout erstellen</Text>
            </LinearGradient>
          </TouchableOpacity>
        </View>
      )}
      <TouchableOpacity onPress={handleCreateWorkout} style={styles.createButton}>
        <LinearGradient colors={["#92A3FD", "#9DCEFF"]} style={styles.createButtonGradient}>
          <Ionicons name="add" size={24} color="#fff" />
          <Text style={styles.createButtonText}>Neues Workout</Text>
        </LinearGradient>
      </TouchableOpacity>
      <WorkoutForm
        visible={workoutFormVisible}
        onClose={handleFormClose}
        workout={currentWorkout}
        isEditing={isEditing}
      />
      <QuickAddModal visible={quickAddModalVisible} onClose={() => setQuickAddModalVisible(false)} />
    </SafeAreaView>
  );
};

export default WorkoutManager;

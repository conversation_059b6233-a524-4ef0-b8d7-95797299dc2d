import { StyleSheet, Dimensions } from "react-native";

const { width } = Dimensions.get('window');

export const adminStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F5F7FA",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: "#8F9BB3",
  },
  headerBackButton: {
    padding: 8,
  },
  tabContainer: {
    flexDirection: "row",
    backgroundColor: "#FFFFFF",
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: "#EDF1F7",
  },
  tabButton: {
    flex: 1,
    alignItems: "center",
    paddingVertical: 12,
    flexDirection: "row",
    justifyContent: "center",
  },
  activeTabButton: {
    borderBottomWidth: 2,
    borderBottomColor: "#6B8CFF",
  },
  tabText: {
    fontSize: 16,
    fontWeight: "500",
    color: "#8F9BB3",
    marginLeft: 8,
  },
  activeTabText: {
    color: "#6B8CFF",
  },
  subTabContainer: {
    flexDirection: "row",
    backgroundColor: "#F7F9FC",
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderBottomWidth: 1,
    borderBottomColor: "#EDF1F7",
  },
  subTabButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginHorizontal: 4,
  },
  activeSubTabButton: {
    backgroundColor: "#E4E9F2",
  },
  subTabText: {
    fontSize: 14,
    fontWeight: "500",
    color: "#8F9BB3",
  },
  activeSubTabText: {
    color: "#6B8FF",
    fontWeight: "600",
  },
  contentContainer: {
    flex: 1,
  },
  listContent: {
    padding: 16,
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  emptyState: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  emptyStateText: {
    marginTop: 10,
    fontSize: 18,
    fontWeight: "600",
    color: "#8F9BB3",
  },
  emptyStateSubText: {
    marginTop: 8,
    fontSize: 14,
    color: "#8F9BB3",
    textAlign: "center",
  },
  searchContainer: {
    flexDirection: "row",
    backgroundColor: "#FFFFFF",
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#EDF1F7",
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: "#333",
  },
  clearButton: {
    marginLeft: 8,
  },
  clearSearchButton: {
    marginTop: 16,
    paddingVertical: 8,
    paddingHorizontal: 16,
    backgroundColor: "#E4E9F2",
    borderRadius: 20,
  },
  clearSearchText: {
    fontSize: 14,
    fontWeight: "500",
    color: "#6B8CFF",
  },
  
  // Modal styles
  modalOverlay: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  modalContainer: {
    width: width * 0.9,
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#2E3A59",
  },
  closeButton: {
    padding: 8,
  },
  modalSection: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#2E3A59",
    marginBottom: 8,
  },
  assignedList: {
    maxHeight: 200,
  },
  availableList: {
    maxHeight: 200,
  },
  noAssignedWorkouts: {
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  noWorkoutsText: {
    marginTop: 10,
    fontSize: 14,
    color: "#8F9BB3",
  },
  noAvailableWorkouts: {
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  modalActions: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  cancelButton: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: "#E4E9F2",
    borderRadius: 12,
  },
  cancelButtonText: {
    fontSize: 14,
    fontWeight: "500",
    color: "#6B8CFF",
  },
  assignButton: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: "#6B8CFF",
    borderRadius: 12,
  },
  assignButtonText: {
    fontSize: 14,
    fontWeight: "500",
    color: "#fff",
  },
});

import React, { useEffect, useState, useRef } from 'react';
import { 
    View, 
    Text, 
    StyleSheet, 
    ScrollView, 
    Image, 
    TouchableOpacity, 
    Modal as RNModal,
    StatusBar,
    Dimensions,
    ActivityIndicator,
    ImageBackground,
    Animated
} from 'react-native';
import { Stack, useLocalSearchParams, router } from 'expo-router';
import { supabase } from '../../lib/supabase';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { Video, ResizeMode } from 'expo-av';
import { BlurView } from 'expo-blur';


interface Exercise {
    id: string;
    name: string;
    duration: string;
    reps: string;
    video_url?: string;
    image_url?: string;
    set_number: number;
}

interface Workout {
    id: number;
    name: string;
    type: string;
    difficulty: string;
    duration: number;
    description: string;
    exercise_count: number;
    calories_burned: number;
    exercises?: Exercise[];
    icon?: string;
    image_url?: string;
}

const { width, height } = Dimensions.get('window');

export default function WorkoutDetailScreen() {
    const params = useLocalSearchParams();
    const workoutId = params.id ? parseInt(params.id as string) : null;
    const [workout, setWorkout] = useState<Workout | null>(null);
    const [loading, setLoading] = useState(true);
    const [selectedVideo, setSelectedVideo] = useState<string | null>(null);
    const [imageLoading, setImageLoading] = useState(true);
    const videoRef = useRef<Video | null>(null);
    
    // Animation values
    const fadeAnim = useRef(new Animated.Value(0)).current;
    const nameOpacity = useRef(new Animated.Value(0)).current;
    const statsY = useRef(new Animated.Value(20)).current;
    const statsOpacity = useRef(new Animated.Value(0)).current;
    const descriptionY = useRef(new Animated.Value(20)).current;
    const descriptionOpacity = useRef(new Animated.Value(0)).current;
    const exercisesAnimatedValues = useRef<{y: Animated.Value, opacity: Animated.Value}[]>([]);

    useEffect(() => {
        if (workoutId) {
            fetchWorkout();
        }
    }, [workoutId]);
    
    // Animate elements when workout is loaded
    useEffect(() => {
        if (workout) {
            // Animate name and stats
            Animated.sequence([
                Animated.timing(nameOpacity, {
                    toValue: 1,
                    duration: 600,
                    delay: 300,
                    useNativeDriver: true
                }),
                Animated.parallel([
                    Animated.timing(statsOpacity, {
                        toValue: 1,
                        duration: 500,
                        useNativeDriver: true
                    }),
                    Animated.timing(statsY, {
                        toValue: 0,
                        duration: 500,
                        useNativeDriver: true
                    })
                ])
            ]).start();
            
            // Animate description
            Animated.timing(descriptionOpacity, {
                toValue: 1,
                duration: 500,
                delay: 300,
                useNativeDriver: true
            }).start();
            
            Animated.timing(descriptionY, {
                toValue: 0,
                duration: 500,
                delay: 300,
                useNativeDriver: true
            }).start();
            
            // Create and animate exercise items
            if (workout.exercises) {
                exercisesAnimatedValues.current = workout.exercises.map(() => ({
                    y: new Animated.Value(20),
                    opacity: new Animated.Value(0)
                }));
                
                // Animate each exercise with a delay
                workout.exercises.forEach((_, index) => {
                    Animated.parallel([
                        Animated.timing(exercisesAnimatedValues.current[index].opacity, {
                            toValue: 1,
                            duration: 500,
                            delay: 400 + (index * 100),
                            useNativeDriver: true
                        }),
                        Animated.timing(exercisesAnimatedValues.current[index].y, {
                            toValue: 0,
                            duration: 500,
                            delay: 400 + (index * 100),
                            useNativeDriver: true
                        })
                    ]).start();
                });
            }
        }
    }, [workout]);

    const fetchWorkout = async () => {
        try {
            const { data: workoutData, error: workoutError } = await supabase
                .from('workouts')
                .select(`
                    *,
                    exercises (*)
                `)
                .eq('id', workoutId)
                .single();

            if (workoutError) throw workoutError;

            // Sort exercises by set_number if they exist
            if (workoutData.exercises) {
                workoutData.exercises.sort((a, b) => a.set_number - b.set_number);
            }

            setWorkout(workoutData);
        } catch (error) {
            console.error('Error fetching workout:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleVideoPress = async (videoUrl: string) => {
        setSelectedVideo(videoUrl);
        // Reset video playback when opened
        if (videoRef.current) {
            await videoRef.current.setStatusAsync({
                shouldPlay: true,
                positionMillis: 0
            });
        }
    };

    const getDifficultyColor = (difficulty: string) => {
        switch (difficulty?.toLowerCase()) {
            case "beginner":
                return "#92A3FD";
            case "intermediate":
                return "#FFA07A";
            case "advanced":
                return "#FF4B4B";
            default:
                return "#92A3FD";
        }
    };

    if (loading) {
        return (
            <View style={styles.loadingContainer}>
                <StatusBar barStyle="light-content" />
                <ActivityIndicator size="large" color="#92A3FD" />
                <Text style={styles.loadingText}>Lädt Workout...</Text>
            </View>
        );
    }

    if (!workout) {
        return (
            <View style={styles.errorContainer}>
                <StatusBar barStyle="dark-content" />
                <Ionicons name="alert-circle-outline" size={64} color="#FF4B4B" />
                <Text style={styles.errorText}>Workout nicht gefunden</Text>
                <TouchableOpacity 
                    style={styles.backButton}
                    onPress={() => router.back()}
                >
                    <Ionicons name="arrow-back" size={20} color="#fff" />
                    <Text style={styles.backButtonText}>Zurück</Text>
                </TouchableOpacity>
            </View>
        );
    }

    // Background image for the header
    const headerBgImage = workout.image_url || workout.icon || 'https://images.unsplash.com/photo-1517836357463-d25dfeac3438?q=80&w=1000';

    return (
        <View style={styles.container}>
            <StatusBar barStyle="light-content" />

            {/* Custom Header with Background Image */}
            <View style={styles.header}>
                <ImageBackground 
                    source={{ uri: headerBgImage }}
                    style={styles.headerBackground}
                    onLoadStart={() => setImageLoading(true)}
                    onLoadEnd={() => setImageLoading(false)}
                >
                    {imageLoading && (
                        <View style={styles.imageLoadingContainer}>
                            <ActivityIndicator color="#fff" size="small" />
                        </View>
                    )}
                    
                    {/* Gradient overlay */}
                    <LinearGradient
                        colors={['rgba(0,0,0,0.7)', 'rgba(0,0,0,0.2)', 'rgba(0,0,0,0.7)']}
                        style={styles.headerGradient}
                    >
                        <BlurView intensity={15} style={StyleSheet.absoluteFill} />
                        
                        {/* Back button */}
                        <TouchableOpacity 
                            style={styles.backButtonContainer}
                            onPress={() => router.back()}
                        >
                            <Ionicons name="chevron-back" size={28} color="#fff" />
                        </TouchableOpacity>

                        {/* Workout info */}
                        <View style={styles.headerContent}>
                            <View style={[
                                styles.difficultyBadge,
                                { backgroundColor: getDifficultyColor(workout.difficulty) }
                            ]}>
                                <Text style={styles.difficultyText}>{workout.difficulty}</Text>
                            </View>

                            <Animated.Text 
                                style={[
                                    styles.workoutName,
                                    { opacity: nameOpacity }
                                ]}
                            >
                                {workout.name}
                            </Animated.Text>
                            
                            <Animated.View 
                                style={[
                                    styles.workoutStats,
                                    { 
                                        opacity: statsOpacity,
                                        transform: [{ translateY: statsY }]
                                    }
                                ]}
                            >
                                <View style={styles.statItem}>
                                    <Ionicons name="time-outline" size={24} color="#fff" />
                                    <Text style={styles.statText}>{workout.duration} Min</Text>
                                </View>
                                <View style={styles.statDivider} />
                                <View style={styles.statItem}>
                                    <Ionicons name="fitness-outline" size={24} color="#fff" />
                                    <Text style={styles.statText}>{workout.exercise_count} Übungen</Text>
                                </View>
                                <View style={styles.statDivider} />
                                <View style={styles.statItem}>
                                    <Ionicons name="flame-outline" size={24} color="#fff" />
                                    <Text style={styles.statText}>{workout.calories_burned} kcal</Text>
                                </View>
                            </Animated.View>
                        </View>
                    </LinearGradient>
                </ImageBackground>
            </View>

            {/* Main Content */}
            <ScrollView 
                style={styles.scrollView}
                showsVerticalScrollIndicator={false}
                contentContainerStyle={styles.scrollContent}
            >
                {/* Description Section */}
                <Animated.View 
                    style={[
                        styles.descriptionContainer,
                        {
                            opacity: descriptionOpacity,
                            transform: [{ translateY: descriptionY }]
                        }
                    ]}
                >
                    <View style={styles.sectionHeaderContainer}>
                        <Ionicons name="information-circle-outline" size={22} color="#92A3FD" />
                        <Text style={styles.sectionTitle}>Beschreibung</Text>
                    </View>
                    <Text style={styles.description}>{workout.description}</Text>
                </Animated.View>
                
                {/* Exercises Section */}
                <View style={styles.exercisesContainer}>
                    <View style={styles.sectionHeaderContainer}>
                        <Ionicons name="barbell-outline" size={22} color="#92A3FD" />
                        <Text style={styles.sectionTitle}>Übungen</Text>
                    </View>

                    {workout.exercises?.map((exercise, index) => (
                        <Animated.View 
                            key={exercise.id} 
                            style={[
                                styles.exerciseCard,
                                {
                                    opacity: exercisesAnimatedValues.current[index]?.opacity || 1,
                                    transform: [{ translateY: exercisesAnimatedValues.current[index]?.y || 0 }]
                                }
                            ]}
                        >
                            <View style={styles.exerciseHeader}>
                                <View style={styles.exerciseNumberContainer}>
                                    <Text style={styles.exerciseNumber}>{index + 1}</Text>
                                </View>
                                <Text style={styles.exerciseName}>{exercise.name}</Text>
                            </View>

                            {exercise.image_url && (
                                <View style={styles.exerciseImageContainer}>
                                    <Image
                                        source={{ uri: exercise.image_url }}
                                        style={styles.exerciseImage}
                                        resizeMode="cover"
                                    />
                                    {exercise.video_url && (
                                        <TouchableOpacity
                                            style={styles.playIconOverlay}
                                            onPress={() => handleVideoPress(exercise.video_url!)}
                                        >
                                            <View style={styles.playIconBackground}>
                                                <Ionicons name="play" size={28} color="#fff" />
                                            </View>
                                        </TouchableOpacity>
                                    )}
                                </View>
                            )}

                            <View style={styles.exerciseDetails}>
                                <View style={styles.exerciseDetailItem}>
                                    <Ionicons name="time-outline" size={20} color="#92A3FD" />
                                    <Text style={styles.exerciseDetailText}>{exercise.duration}</Text>
                                </View>
                                <View style={styles.exerciseDetailItem}>
                                    <Ionicons name="repeat-outline" size={20} color="#92A3FD" />
                                    <Text style={styles.exerciseDetailText}>{exercise.reps} Wiederholungen</Text>
                                </View>
                            </View>

                            {exercise.video_url && !exercise.image_url && (
                                <TouchableOpacity
                                    style={styles.videoButton}
                                    onPress={() => handleVideoPress(exercise.video_url!)}
                                >
                                    <LinearGradient
                                        colors={['#92A3FD', '#9DCEFF']}
                                        style={styles.videoButtonGradient}
                                        start={{ x: 0, y: 0 }}
                                        end={{ x: 1, y: 0 }}
                                    >
                                        <Ionicons name="play-circle-outline" size={22} color="#fff" />
                                        <Text style={styles.videoButtonText}>Video ansehen</Text>
                                    </LinearGradient>
                                </TouchableOpacity>
                            )}
                        </Animated.View>
                    ))}
                </View>

                {/* Bottom padding */}
                <View style={{ height: 20 }} />
            </ScrollView>

            {/* Video Modal */}
            <RNModal
                animationType="fade"
                transparent={true}
                visible={!!selectedVideo}
                onRequestClose={() => setSelectedVideo(null)}
            >
                <View style={styles.modalContainer}>
                    <View style={styles.videoContainer}>
                        <TouchableOpacity
                            style={styles.closeButton}
                            onPress={() => setSelectedVideo(null)}
                        >
                            <Ionicons name="close-circle" size={32} color="#fff" />
                        </TouchableOpacity>

                        {selectedVideo && (
                            <Video
                                ref={videoRef}
                                style={styles.video}
                                source={{ uri: selectedVideo }}
                                useNativeControls
                                resizeMode={ResizeMode.CONTAIN}
                                isLooping={false}
                                shouldPlay={true}
                            />
                        )}
                    </View>
                </View>
            </RNModal>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#fff',
    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#f8f9fa',
    },
    loadingText: {
        marginTop: 16,
        fontSize: 16,
        color: '#666',
        fontWeight: '500',
    },
    errorContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#f8f9fa',
        padding: 20,
    },
    errorText: {
        fontSize: 18,
        color: '#444',
        marginVertical: 16,
        textAlign: 'center',
    },
    header: {
        height: height * 0.35,
        overflow: 'hidden',
    },
    headerBackground: {
        width: '100%',
        height: '100%',
    },
    imageLoadingContainer: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgba(0,0,0,0.2)',
    },
    headerGradient: {
        flex: 1,
        justifyContent: 'space-between',
        padding: 16,
        paddingTop: 40,
    },
    backButtonContainer: {
        width: 40,
        height: 40,
        borderRadius: 20,
        backgroundColor: 'rgba(0,0,0,0.4)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    headerContent: {
        alignItems: 'flex-start',
    },
    difficultyBadge: {
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 16,
        marginBottom: 12,
    },
    difficultyText: {
        fontSize: 13,
        fontWeight: '700',
        color: '#fff',
    },
    workoutName: {
        fontSize: 28,
        fontWeight: 'bold',
        color: '#fff',
        marginBottom: 16,
        textShadowColor: 'rgba(0,0,0,0.5)',
        textShadowOffset: { width: 1, height: 1 },
        textShadowRadius: 5,
    },
    workoutStats: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: 'rgba(0,0,0,0.4)',
        paddingHorizontal: 16,
        paddingVertical: 10,
        borderRadius: 20,
    },
    statItem: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    statText: {
        color: '#fff',
        fontSize: 14,
        fontWeight: '600',
        marginLeft: 6,
    },
    statDivider: {
        width: 1,
        height: 20,
        backgroundColor: 'rgba(255,255,255,0.3)',
        marginHorizontal: 12,
    },
    scrollView: {
        flex: 1,
        backgroundColor: '#f8f9fa',
    },
    scrollContent: {
        padding: 16,
    },
    descriptionContainer: {
        backgroundColor: '#fff',
        borderRadius: 16,
        padding: 16,
        marginBottom: 16,
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
    },
    sectionHeaderContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 12,
    },
    sectionTitle: {
        fontSize: 18,
        fontWeight: '700',
        color: '#333',
        marginLeft: 8,
    },
    description: {
        fontSize: 15,
        color: '#555',
        lineHeight: 22,
    },
    exercisesContainer: {
        backgroundColor: '#fff',
        borderRadius: 16,
        padding: 16,
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
    },
    exerciseCard: {
        backgroundColor: '#f8f9fa',
        borderRadius: 12,
        padding: 16,
        marginBottom: 12,
        borderWidth: 1,
        borderColor: '#eaeaea',
    },
    exerciseHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 12,
    },
    exerciseNumberContainer: {
        width: 28,
        height: 28,
        borderRadius: 14,
        backgroundColor: '#92A3FD',
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 12,
    },
    exerciseNumber: {
        fontSize: 14,
        fontWeight: 'bold',
        color: '#fff',
    },
    exerciseName: {
        fontSize: 17,
        fontWeight: '600',
        color: '#333',
        flex: 1,
    },
    exerciseImageContainer: {
        position: 'relative',
        marginBottom: 12,
        borderRadius: 12,
        overflow: 'hidden',
    },
    exerciseImage: {
        width: '100%',
        height: 180,
        borderRadius: 12,
    },
    playIconOverlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        justifyContent: 'center',
        alignItems: 'center',
    },
    playIconBackground: {
        width: 60,
        height: 60,
        borderRadius: 30,
        backgroundColor: 'rgba(0,0,0,0.5)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    exerciseDetails: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        gap: 12,
        marginBottom: 12,
    },
    exerciseDetailItem: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#f0f4f8',
        paddingVertical: 6,
        paddingHorizontal: 12,
        borderRadius: 20,
    },
    exerciseDetailText: {
        fontSize: 14,
        color: '#444',
        marginLeft: 6,
    },
    videoButton: {
        borderRadius: 8,
        overflow: 'hidden',
        marginTop: 4,
    },
    videoButtonGradient: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 10,
    },
    videoButtonText: {
        fontSize: 15,
        color: '#fff',
        fontWeight: '600',
        marginLeft: 8,
    },
    modalContainer: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.9)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    videoContainer: {
        width: '100%',
        height: '60%',
        justifyContent: 'center',
        alignItems: 'center',
    },
    video: {
        width: '100%',
        height: '100%',
    },
    closeButton: {
        position: 'absolute',
        top: -40,
        right: 16,
        zIndex: 1,
    },
    backButton: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#92A3FD',
        paddingVertical: 10,
        paddingHorizontal: 20,
        borderRadius: 25,
        marginTop: 20,
    },
    backButtonText: {
        color: '#fff',
        fontSize: 16,
        fontWeight: '600',
        marginLeft: 8,
    },
});

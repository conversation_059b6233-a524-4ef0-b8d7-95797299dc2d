import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Animated,
  Dimensions,
  StatusBar,
} from 'react-native';
import { BlurView } from 'expo-blur';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { supabase } from '@/lib/supabase';

const { width } = Dimensions.get('window');

interface Message {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
}

const suggestions = [
  "Create a workout plan for me 🏋️‍♀️",
  "Give me nutrition tips 🥗",
  "How can I improve my sleep? 😴",
  "Tips for motivation 💪",
];

// Enhanced message bubble with professional styling
const MessageBubble = React.memo(({ message }: { message: Message }) => {
  // Create animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const translateYAnim = useRef(new Animated.Value(10)).current;
  
  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(translateYAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);
  
  // Animation values for AI avatar
  const avatarScale = useRef(new Animated.Value(0.8)).current;
  const avatarOpacity = useRef(new Animated.Value(0)).current;
  
  useEffect(() => {
    if (!message.isUser) {
      Animated.parallel([
        Animated.timing(avatarScale, {
          toValue: 1,
          duration: 300,
          delay: 150,
          useNativeDriver: true,
        }),
        Animated.timing(avatarOpacity, {
          toValue: 1,
          duration: 300,
          delay: 150,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, []);

  return (
    <Animated.View
      style={[
        styles.messageBubble,
        message.isUser ? styles.userMessage : styles.aiMessage,
        {
          opacity: fadeAnim,
          transform: [{ translateY: translateYAnim }]
        }
      ]}
    >
      {!message.isUser && (
        <Animated.View
          style={[
            styles.aiAvatar,
            {
              opacity: avatarOpacity,
              transform: [{ scale: avatarScale }],
            }
          ]}
        >
          <LinearGradient
            colors={['#FF9DC4', '#FF6B9C']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={styles.avatarGradient}
          >
            <Ionicons name="fitness" size={16} color="#fff" />
          </LinearGradient>
        </Animated.View>
      )}
      <LinearGradient
        colors={message.isUser 
          ? ['#FF6B9C', '#FF558D'] 
          : ['#FFFFFF', '#F8F9FF']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={[
          styles.messageContent,
          message.isUser ? styles.userMessageContent : styles.aiMessageContent,
        ]}
      >
        <Text 
          style={[
            styles.messageText,
            message.isUser ? styles.userMessageText : styles.aiMessageText
          ]}
          adjustsFontSizeToFit={false}
        >
          {message.text}
        </Text>
      </LinearGradient>
    </Animated.View>
  );
});

// Animated typing indicator with dots
const TypingIndicator = () => {
  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const translateYAnim = useRef(new Animated.Value(10)).current;
  const dotAnimations = [0, 1, 2].map(() => useRef(new Animated.Value(0.3)).current);
  
  useEffect(() => {
    // Animate container
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(translateYAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
    
    // Animate dots
    dotAnimations.forEach((anim, i) => {
      Animated.loop(
        Animated.sequence([
          Animated.timing(anim, {
            toValue: 1,
            duration: 600,
            delay: i * 200,
            useNativeDriver: true,
          }),
          Animated.timing(anim, {
            toValue: 0.3,
            duration: 600,
            useNativeDriver: true,
          }),
        ])
      ).start();
    });
    
    return () => {
      dotAnimations.forEach(anim => anim.stopAnimation());
    };
  }, []);

  return (
    <Animated.View
      style={[
        styles.typingIndicator,
        {
          opacity: fadeAnim,
          transform: [{ translateY: translateYAnim }]
        }
      ]}
    >
      <View style={styles.typingDotsContainer}>
        {dotAnimations.map((anim, i) => (
          <Animated.View
            key={i}
            style={[
              styles.typingDot,
              {
                opacity: anim
              }
            ]}
          />
        ))}
      </View>
    </Animated.View>
  );
};

export default function AIScreen() {
  const insets = useSafeAreaInsets();
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      text: "Hi! 👋 I'm your FitFemme AI Assistant.\nHow can I help you today?",
      isUser: false,
      timestamp: new Date(),
    },
  ]);
  const [inputText, setInputText] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const scrollViewRef = useRef<ScrollView>(null);
  const [isNearBottom, setIsNearBottom] = useState(true);
  const [showSuggestions, setShowSuggestions] = useState(true);

  const handleScroll = (event: any) => {
    const { layoutMeasurement, contentOffset, contentSize } = event.nativeEvent;
    const paddingToBottom = 20;
    const isCloseToBottom = layoutMeasurement.height + contentOffset.y >= 
      contentSize.height - paddingToBottom;
    setIsNearBottom(isCloseToBottom);
  };

  const handleTextChange = (text: string) => {
    setInputText(text);
    if (isNearBottom) {
      setTimeout(() => {
        scrollViewRef.current?.scrollToEnd({ animated: false });
      }, 50);
    }
  };

  const sendMessage = async (text: string) => {
    if (!text.trim()) return;

    const newUserMessage: Message = {
      id: Date.now().toString(),
      text: text.trim(),
      isUser: true,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, newUserMessage]);
    setInputText('');
    setIsTyping(true);
    setShowSuggestions(false); // Hide suggestions after sending a message

    // Scroll after sending
    setTimeout(() => {
      scrollViewRef.current?.scrollToEnd({ animated: true });
    }, 100);

    try {
      // Simulated AI response
      setTimeout(() => {
        const aiResponse: Message = {
          id: (Date.now() + 1).toString(),
          text: "That's a great question! Let me help you with that...",
          isUser: false,
          timestamp: new Date(),
        };
        setMessages(prev => [...prev, aiResponse]);
        setIsTyping(false);
        
        setTimeout(() => {
          scrollViewRef.current?.scrollToEnd({ animated: true });
        }, 100);
      }, 1500);
    } catch (error) {
      console.error('Error sending message:', error);
      setIsTyping(false);
    }
  };

  // Scroll to bottom when component mounts
  useEffect(() => {
    setTimeout(() => {
      scrollViewRef.current?.scrollToEnd({ animated: false });
    }, 300);
  }, []);

  // Calculate the bottom position of the suggestions container
  // to ensure it doesn't overlap with the input field
  // Add extra space to account for the tab bar height (65px) plus its margin (10px)
  const TAB_BAR_HEIGHT = 65 + 10 + insets.bottom;
  const INPUT_HEIGHT = 70;
  const suggestionsBottomPosition = TAB_BAR_HEIGHT + INPUT_HEIGHT + 10;

  // Fix other components that use animation props
  const headerContentOpacity = useRef(new Animated.Value(0)).current;
  const headerContentTranslateY = useRef(new Animated.Value(-10)).current;
  const welcomeContainerOpacity = useRef(new Animated.Value(0)).current;
  const welcomeContainerTranslateY = useRef(new Animated.Value(20)).current;
  const suggestionsOpacity = useRef(new Animated.Value(0)).current;
  const suggestionsTranslateY = useRef(new Animated.Value(30)).current;
  
  useEffect(() => {
    // Animate header content
    Animated.parallel([
      Animated.timing(headerContentOpacity, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(headerContentTranslateY, {
        toValue: 0,
        duration: 600, 
        useNativeDriver: true,
      }),
    ]).start();
    
    // Animate welcome container
    Animated.parallel([
      Animated.timing(welcomeContainerOpacity, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(welcomeContainerTranslateY, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();
    
    // Animate suggestions
    Animated.parallel([
      Animated.timing(suggestionsOpacity, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(suggestionsTranslateY, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" />
      
      {/* Elegant header with gradient background */}
      <LinearGradient
        colors={['#FFFFFF', '#F8F9FF']}
        style={[
          styles.header,
          { paddingTop: Math.max(insets.top, 40) }
        ]}
      >
        <Animated.View
          style={[
            styles.headerContent,
            {
              opacity: headerContentOpacity,
              transform: [{ translateY: headerContentTranslateY }]
            }
          ]}
        >
          <Text style={styles.headerTitle}>FitFemme AI</Text>
          <Text style={styles.headerSubtitle}>Your personal fitness assistant</Text>
        </Animated.View>
      </LinearGradient>

      {/* Main container with proper spacing for input */}
      <View style={styles.mainContainer}>
        {/* Chat messages area with improved sizing */}
        <ScrollView
          ref={scrollViewRef}
          contentContainerStyle={[
            styles.scrollViewContent,
            { paddingBottom: TAB_BAR_HEIGHT + INPUT_HEIGHT + 20 } // Add padding to account for tab bar and input
          ]}
          showsVerticalScrollIndicator={false}
          scrollEnabled={true}
          keyboardShouldPersistTaps="handled"
          scrollEventThrottle={16}
          onScroll={handleScroll}
          overScrollMode="never"
        >
          {/* Added a welcome container for better initial layout */}
          {messages.length === 1 && (
            <Animated.View 
              style={[
                styles.welcomeContainer,
                {
                  opacity: welcomeContainerOpacity,
                  transform: [{ translateY: welcomeContainerTranslateY }]
                }
              ]}
            >
              <LinearGradient
                colors={['rgba(255,107,156,0.15)', 'rgba(255,107,156,0.05)']}
                style={styles.welcomeGradient}
              >
                <Text style={styles.welcomeTitle}>Welcome to FitFemme AI</Text>
                <Text style={styles.welcomeText}>
                  I can help you with workout plans, nutrition tips, and wellness questions.
                  Just ask me anything or choose a suggestion below.
                </Text>
              </LinearGradient>
            </Animated.View>
          )}
          
          {messages.map((message) => (
            <MessageBubble key={message.id} message={message} />
          ))}
          
          {isTyping && <TypingIndicator />}
          
          {/* Add padding at bottom to ensure content doesn't go under input */}
          <View style={{ height: 20 }} />
        </ScrollView>
      </View>

      {/* Redesigned Suggestions - Now more visible and prominent but not blocking input field */}
      {showSuggestions && (
        <Animated.View
          style={[
            styles.suggestionsOuterContainer,
            { 
              bottom: suggestionsBottomPosition,
              opacity: suggestionsOpacity,
              transform: [{ translateY: suggestionsTranslateY }]
            }
          ]}
        >
          <BlurView intensity={50} tint="light" style={styles.suggestionsBlurContainer}>
            <Text style={styles.suggestionsTitle}>
              <Ionicons name="bulb-outline" size={18} color="#FF6B9C" style={{ marginRight: 6 }} />
              What would you like to ask?
            </Text>
            
            <View style={styles.suggestionsGrid}>
              {suggestions.map((suggestion, index) => {
                // Create animation refs for each suggestion
                const suggestionOpacity = useRef(new Animated.Value(0)).current;
                const suggestionScale = useRef(new Animated.Value(0.9)).current;
                
                useEffect(() => {
                  Animated.parallel([
                    Animated.timing(suggestionOpacity, {
                      toValue: 1,
                      duration: 300,
                      delay: 300 + index * 100,
                      useNativeDriver: true,
                    }),
                    Animated.timing(suggestionScale, {
                      toValue: 1,
                      duration: 300,
                      delay: 300 + index * 100,
                      useNativeDriver: true,
                    }),
                  ]).start();
                }, []);
                
                return (
                  <Animated.View
                    key={index}
                    style={[
                      styles.suggestionWrapper,
                      {
                        opacity: suggestionOpacity,
                        transform: [{ scale: suggestionScale }]
                      }
                    ]}
                  >
                    <TouchableOpacity
                      style={styles.suggestionBubble}
                      onPress={() => sendMessage(suggestion)}
                      activeOpacity={0.7}
                    >
                      <LinearGradient
                        colors={['#FFF', '#F8F8FF']}
                        style={styles.suggestionGradient}
                      >
                        <Text style={styles.suggestionText}>{suggestion}</Text>
                      </LinearGradient>
                    </TouchableOpacity>
                  </Animated.View>
                );
              })}
            </View>
          </BlurView>
        </Animated.View>
      )}

      {/* Fixed input container - always visible above the tab bar */}
      <View style={[
        styles.fixedInputContainer,
        { bottom: TAB_BAR_HEIGHT } // Position above the tab bar
      ]}>
        <BlurView intensity={90} tint="light" style={styles.inputContainer}>
          <TextInput
            style={styles.input}
            value={inputText}
            onChangeText={handleTextChange}
            placeholder="Ask a question..."
            placeholderTextColor="rgba(0,0,0,0.35)"
            multiline
            maxLength={1000}
          />
          <TouchableOpacity
            style={[
              styles.sendButton,
              !inputText.trim() && styles.sendButtonDisabled
            ]}
            onPress={() => sendMessage(inputText)}
            disabled={!inputText.trim()}
            activeOpacity={0.7}
          >
            <LinearGradient
              colors={['#FF6B9C', '#FF4B8C']}
              style={styles.sendButtonGradient}
            >
              <Ionicons 
                name="send" 
                size={20} 
                color={inputText.trim() ? '#fff' : 'rgba(255,255,255,0.7)'}
              />
            </LinearGradient>
          </TouchableOpacity>
        </BlurView>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FF',
  },
  header: {
    paddingBottom: 15,
    borderBottomRightRadius: 20,
    borderBottomLeftRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 10,
    elevation: 5,
    zIndex: 10,
  },
  headerContent: {
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  headerTitle: {
    fontSize: 26,
    fontWeight: '700',
    color: '#333',
    letterSpacing: 0.5,
  },
  headerSubtitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666',
    marginTop: 6,
  },
  chatWrapper: {
    flex: 1,
    paddingHorizontal: 16,
    paddingBottom: 80,
  },
  scrollViewContent: {
    paddingTop: 20,
    paddingHorizontal: 16, // Added horizontal padding to center chat content
  },
  messageBubble: {
    marginVertical: 8,
    maxWidth: '75%', // Reduced width to match input field width
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  userMessage: {
    alignSelf: 'flex-end',
  },
  aiMessage: {
    alignSelf: 'flex-start',
    marginRight: 20,
    marginLeft: 4,
  },
  messageContent: {
    borderRadius: 22,
    padding: 14,
    paddingHorizontal: 16,
    minHeight: 44,
    flexShrink: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
  },
  userMessageContent: {
    borderTopRightRadius: 4,
    minWidth: 60,
  },
  aiMessageContent: {
    borderTopLeftRadius: 4,
    minWidth: 60,
  },
  messageText: {
    fontSize: 16,
    lineHeight: 22,
    flexWrap: 'wrap',
    flexShrink: 1,
  },
  userMessageText: {
    color: '#fff',
    fontWeight: '500',
  },
  aiMessageText: {
    color: '#333',
  },
  aiAvatar: {
    width: 32,
    height: 32,
    marginRight: 8,
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  avatarGradient: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  typingIndicator: {
    alignSelf: 'flex-start',
    marginLeft: 40,
    marginTop: 8,
    marginBottom: 8,
  },
  typingDotsContainer: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    paddingHorizontal: 14,
    paddingVertical: 10,
    borderRadius: 18,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 1,
  },
  typingDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#FF6B9C',
    marginHorizontal: 3,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 18,
    paddingVertical: 8,
    borderRadius: 24,
    backgroundColor: 'rgba(255,255,255,0.95)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 4,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.03)',
  },
  input: {
    flex: 1,
    fontSize: 16,
    color: '#333',
    paddingVertical: 10,
    maxHeight: 120,
    minHeight: 40,
  },
  sendButton: {
    marginLeft: 10,
  },
  sendButtonDisabled: {
    opacity: 0.5,
  },
  sendButtonGradient: {
    width: 42,
    height: 42,
    borderRadius: 21,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#FF6B9C',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 2,
  },
  suggestionsOuterContainer: {
    position: 'absolute',
    left: 16,
    right: 16,
    borderRadius: 20,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 10,
    elevation: 5,
    zIndex: 5,
    maxHeight: '40%', // Limit height to ensure it doesn't take too much space
  },
  suggestionsBlurContainer: {
    padding: 20,
    paddingBottom: 15,
    backgroundColor: 'rgba(255,255,255,0.8)',
  },
  suggestionsTitle: {
    fontSize: 17,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
    textAlign: 'center',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  suggestionsGrid: {
    flexDirection: 'column',
    justifyContent: 'space-between',
    width: '100%',
  },
  suggestionWrapper: {
    marginBottom: 12,
    width: '100%',
  },
  suggestionBubble: {
    width: '100%',
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#FF6B9C',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
    borderWidth: 1,
    borderColor: 'rgba(255,107,156,0.15)',
  },
  suggestionGradient: {
    paddingVertical: 14,
    paddingHorizontal: 18,
    alignItems: 'center',
  },
  suggestionText: {
    color: '#FF558D',
    fontSize: 15,
    fontWeight: '500',
    textAlign: 'center',
  },
  welcomeContainer: {
    marginTop: 20,
    marginBottom: 15,
    paddingHorizontal: 10,
  },
  welcomeGradient: {
    borderRadius: 18,
    padding: 20,
    paddingVertical: 16,
  },
  welcomeTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
    textAlign: 'center',
  },
  welcomeText: {
    fontSize: 14,
    lineHeight: 20,
    color: '#666',
    textAlign: 'center',
  },
  mainContainer: {
    flex: 1,
    position: 'relative',
  },
  fixedInputContainer: {
    position: 'absolute',
    left: 16,
    right: 16,
    paddingVertical: 10,
    zIndex: 10, // Ensure input is always on top
  },
});

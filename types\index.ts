export interface User {
  id: string;
  email: string;
  full_name: string;
  profile_picture_url: string | null;
  is_admin: boolean;
  created_at: string;
  last_sign_in_at: string;
  height?: number;
  weight?: number;
  birth_date?: string | null;
  subscription_type: 'basic' | 'premium' | null;
}

export interface Exercise {
  id: string;
  name: string;
  duration: string;
  reps: string;
  video_url?: string;
  image_url?: string;
  set_number: number;
}

export interface Workout {
  id: number;
  name: string;
  type: string;
  difficulty: string;
  duration: number;
  description: string;
  exercise_count: number;
  calories_burned: number;
  exercises?: Exercise[];
  icon?: string;
  selected?: boolean;
}

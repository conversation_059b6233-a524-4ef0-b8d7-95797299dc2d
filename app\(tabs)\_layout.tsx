import React, { useState, useRef, useEffect } from 'react';
import { Pressable, StyleSheet, View, GestureResponderEvent, Platform, Text, Dimensions, Animated } from 'react-native';
import { Tabs, useRouter } from 'expo-router';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useColorScheme } from 'react-native';
import { useSettings } from '../../context/SettingsContext';

import FontAwesome from '@expo/vector-icons/FontAwesome';
import Ionicons from '@expo/vector-icons/Ionicons';

import { BlurView } from 'expo-blur';
import { LinearGradient } from 'expo-linear-gradient';

// Import our new AddButtonMenu component
import AddButtonMenu from '../../components/AddButtonMenu';

// Window dimensions for positioning
const { width } = Dimensions.get('window');

type AddButtonProps = {
  onPress?: (event?: GestureResponderEvent) => void;
  focused: boolean;
};

// AI Tab Button Props
type AITabButtonProps = {
  color: string;
  focused: boolean;
  onPress?: (event?: GestureResponderEvent) => void;
};

function TabBarIcon(props: {
  name: React.ComponentProps<typeof FontAwesome>['name'];
  color: string;
  focused: boolean;
}) {
  const scaleAnim = useRef(new Animated.Value(1)).current;
  
  useEffect(() => {
    Animated.timing(scaleAnim, {
      toValue: props.focused ? 1.1 : 1,
      duration: 200,
      useNativeDriver: true,
    }).start();
  }, [props.focused]);
  
  return (
    <Animated.View
      style={[
        styles.iconContainer,
        props.focused && styles.activeIconContainer,
        { transform: [{ scale: scaleAnim }] }
      ]}
    >
      <FontAwesome size={22} {...props} />
    </Animated.View>
  );
}

// Custom AI Tab Button with dropdown menu
function AITabButton({ color, focused, onPress }: AITabButtonProps) {
  // AI features for the dropdown - Define with proper types
  const aiFeatures = [
    { name: "Chat Assistant", icon: "chatbubble-ellipses", route: "ai" }, // Use tab name
    { name: "Workout Generator", icon: "fitness", route: "ai/workout-generator" },
    { name: "Meal Planner", icon: "restaurant", route: "ai/meal-planner" },
  ];
  
  const [isOpen, setIsOpen] = useState(false);
  const router = useRouter();
  
  // Animation values
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const rotateAnim = useRef(new Animated.Value(0)).current;
  const translateYAnim = useRef(new Animated.Value(0)).current;
  const dropdownOpacity = useRef(new Animated.Value(0)).current;
  const dropdownScale = useRef(new Animated.Value(0.9)).current;
  const dropdownTranslateY = useRef(new Animated.Value(0)).current;
  
  // Create animation refs for each menu item outside of map function
  const itemAnims = useRef(
    aiFeatures.map(() => new Animated.Value(0))
  ).current;
  
  useEffect(() => {
    Animated.timing(scaleAnim, {
      toValue: focused ? 1.1 : 1,
      duration: 200,
      useNativeDriver: true,
    }).start();
  }, [focused]);
  
  useEffect(() => {
    if (isOpen) {
      Animated.parallel([
        Animated.timing(rotateAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(translateYAnim, {
          toValue: -2,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(dropdownOpacity, {
          toValue: 1,
          duration: 250,
          useNativeDriver: true,
        }),
        Animated.timing(dropdownScale, {
          toValue: 1,
          duration: 250,
          useNativeDriver: true,
        }),
        Animated.timing(dropdownTranslateY, {
          toValue: -10,
          duration: 250,
          useNativeDriver: true,
        }),
        // Animate each menu item
        ...itemAnims.map((anim, index) =>
          Animated.timing(anim, {
            toValue: 1,
            duration: 250,
            delay: 100 + index * 70,
            useNativeDriver: true,
          })
        )
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(rotateAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(translateYAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(dropdownOpacity, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(dropdownScale, {
          toValue: 0.9,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(dropdownTranslateY, {
          toValue: 5,
          duration: 200,
          useNativeDriver: true,
        })
      ]).start();
      
      // Reset all item animations
      itemAnims.forEach(anim => anim.setValue(0));
    }
  }, [isOpen]);
  
  const rotate = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '180deg'],
  });
  
  const handlePress = (e?: GestureResponderEvent) => {
    // Prevent default behavior to avoid page refresh and navigation
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    
    // Toggle dropdown menu without navigating
    setIsOpen(prev => !prev);
    
    // Do not navigate on first tap, just show the menu
    // We'll remove this navigation code:
    // if (onPress && !focused && !isOpen) {
    //   setTimeout(() => onPress(e), 50);
    // }
  };
  
  const navigateToAIFeature = (route: string) => {
    setIsOpen(false);
    
    // Add a small delay for animation to finish
    setTimeout(() => {
      try {
        // For AI, navigate explicitly, for other routes use navigate
        if (route === "ai") {
          // Main AI tab - use the passed onPress prop for tab navigation
          if (onPress) onPress();
        } else {
          // For nested routes, use router.navigate
          router.navigate(route as any);
        }
      } catch (error) {
        console.error("Navigation error:", error);
      }
    }, 100);
  };
  

  return (
    <View style={{ alignItems: 'center' }}>
      {/* Main AI icon with arrow indicator */}
      <Pressable
        onPress={handlePress}
        style={[
          styles.iconContainer,
          focused && styles.activeIconContainer,
        ]}
      >
        <Animated.View
          style={{
            transform: [{ scale: scaleAnim }],
            alignItems: 'center', 
            justifyContent: 'center'
          }}
        >
          <Ionicons name="sparkles-outline" size={24} color={color} />
          
          {/* Animated arrow indicator */}
          <Animated.View
            style={{
              position: 'absolute', 
              bottom: -12,
              transform: [
                { rotate },
                { translateY: translateYAnim }
              ]
            }}
          >
            <Ionicons 
              name="chevron-up" 
              size={12} 
              color={isOpen ? '#ff558d' : color} 
              style={{ opacity: focused ? 1 : 0.7 }}
            />
          </Animated.View>
        </Animated.View>
      </Pressable>

      {/* Dropdown menu with beautiful animation */}
      {(isOpen || dropdownOpacity._value > 0) && (
        <Animated.View
          style={[
            styles.aiDropdownContainer,
            {
              opacity: dropdownOpacity,
              transform: [
                { scale: dropdownScale },
                { translateY: dropdownTranslateY }
              ]
            }
          ]}
        >
          <BlurView intensity={80} tint="light" style={styles.aiDropdownBlur}>
            <View style={styles.dropdownArrow} />
            
            {aiFeatures.map((feature, index) => {
              return (
                <Animated.View
                  key={feature.name}
                  style={{
                    opacity: itemAnims[index],
                    transform: [{ translateX: itemAnims[index].interpolate({
                      inputRange: [0, 1],
                      outputRange: [-10, 0]
                    }) }]
                  }}
                >
                  <Pressable
                    style={({ pressed }) => [
                      styles.aiFeatureItem,
                      pressed && { opacity: 0.8, transform: [{ scale: 0.98 }] }
                    ]}
                    onPress={() => navigateToAIFeature(feature.route)}
                  >
                    <LinearGradient
                      colors={feature.route === 'ai' ? ['#FF6B9C', '#FF558D'] : ['#FFF', '#F8F8FF']}
                      start={{ x: 0, y: 0 }}
                      end={{ x: 1, y: 1 }}
                      style={[
                        styles.featureIconContainer,
                        feature.route === 'ai' && { borderWidth: 0 }
                      ]}
                    >
                      <Ionicons 
                        name={feature.icon as any} 
                        size={16} 
                        color={feature.route === 'ai' ? '#fff' : '#FF558D'} 
                      />
                    </LinearGradient>
                    <Text style={styles.featureText}>{feature.name}</Text>
                    
                    {feature.route === 'ai' && (
                      <View style={styles.activeDot} />
                    )}
                  </Pressable>
                </Animated.View>
              );
            })}
          </BlurView>
        </Animated.View>
      )}
    </View>
  );
}

// Unser mittiger Plus-Button mit horizontalem Menü
function AddButton({ onPress, focused }: AddButtonProps) {
  const [isOpen, setIsOpen] = useState(false);
  const { triggerHaptic } = useSettings();
  const menuOpenAnim = useRef(new Animated.Value(0)).current;
  const buttonScaleAnim = useRef(new Animated.Value(1)).current;
  const backdropOpacity = useRef(new Animated.Value(0)).current;
  
  // Create individual animations for each menu option
  const leftOptionAnim = useRef(new Animated.Value(0)).current;
  const centerOptionAnim = useRef(new Animated.Value(0)).current;
  const rightOptionAnim = useRef(new Animated.Value(0)).current;
  
  // Menu options data
  const menuOptions = [
    { name: "Workout", icon: "fitness", color: "#FF686B", animation: leftOptionAnim },
    { name: "Nutrition", icon: "nutrition", color: "#FF8E6E", animation: centerOptionAnim },
    { name: "Health", icon: "heart", color: "#FF758F", animation: rightOptionAnim }
  ];

  // Handle button press with improved animation sequence
  const handlePress = (e?: GestureResponderEvent) => {
    triggerHaptic();
    setIsOpen(prev => !prev);
    
    // Menu button scale animation
    Animated.sequence([
      Animated.timing(buttonScaleAnim, {
        toValue: 0.9,
        duration: 100,
        useNativeDriver: true
      }),
      Animated.spring(buttonScaleAnim, {
        toValue: 1,
        friction: 5,
        tension: 300,
        useNativeDriver: true
      })
    ]).start();

    // Optional navigation callback if provided
    if (onPress && !isOpen) {
      // We could call onPress here if needed, but we're avoiding navigation
    }
  };

  // Handle menu option selection
  const handleOptionPress = (option: string) => {
    triggerHaptic();
    console.log(`${option} option selected`);
    
    // Close menu after selection with a slight delay
    setTimeout(() => {
      setIsOpen(false);
    }, 100);
  };

  // Control animations when menu state changes
  useEffect(() => {
    // Prepare animation sequence
    const animationSequence = [];
    
    if (isOpen) {
      // Open animations
      animationSequence.push(
        // Backdrop fade in
        Animated.timing(backdropOpacity, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true
        }),
        
        // Menu opening animation
        Animated.timing(menuOpenAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true
        })
      );
      
      // Staggered option animations
      animationSequence.push(
        Animated.stagger(80, [
          Animated.spring(leftOptionAnim, {
            toValue: 1,
            friction: 6,
            tension: 300,
            useNativeDriver: true
          }),
          Animated.spring(centerOptionAnim, {
            toValue: 1,
            friction: 6,
            tension: 300,
            useNativeDriver: true
          }),
          Animated.spring(rightOptionAnim, {
            toValue: 1,
            friction: 6,
            tension: 300,
            useNativeDriver: true
          })
        ])
      );
    } else {
      // Close animations in reverse order
      animationSequence.push(
        // Options disappear first
        Animated.parallel([
          Animated.timing(leftOptionAnim, {
            toValue: 0,
            duration: 150,
            useNativeDriver: true
          }),
          Animated.timing(centerOptionAnim, {
            toValue: 0,
            duration: 150,
            useNativeDriver: true
          }),
          Animated.timing(rightOptionAnim, {
            toValue: 0,
            duration: 150,
            useNativeDriver: true
          })
        ]),
        
        // Then close the menu and fade out backdrop
        Animated.parallel([
          Animated.timing(menuOpenAnim, {
            toValue: 0,
            duration: 200,
            useNativeDriver: true
          }),
          Animated.timing(backdropOpacity, {
            toValue: 0,
            duration: 150,
            useNativeDriver: true
          })
        ])
      );
    }
    
    // Run animations in sequence
    Animated.sequence(animationSequence).start();
  }, [isOpen]);
  
  // Calculate menu position based on screen width - FIX POSITIONING
  const menuWidth = Math.min(width * 0.7, 300); // Limit max width to prevent overflow
  const menuLeftPosition = -menuWidth / 2 + 30; // Center the menu
  
  // Dynamic transforms for menu options based on their position - FIXED POSITION CALCULATION
  const getOptionTransform = (animation: Animated.Value, index: number) => {
    const baseTranslateY = -25; // Reduced from -70 to -55 to place icons lower
    const positionMultiplier = index === 1 ? 1.2 : 1; // Slightly reduced from 1.3 to 1.2
    
    // Calculate horizontal spread to keep icons on screen
    let translateX = 0;
    if (index === 0) translateX = -10; // Left icon, increased spread
    else if (index === 2) translateX = 10; // Right icon, increased spread
    // Middle icon (index === 1) remains at translateX = 0
    
    return {
      opacity: animation,
      transform: [
        { scale: animation },
        { 
          translateY: animation.interpolate({
            inputRange: [0, 1],
            outputRange: [0, baseTranslateY * positionMultiplier]
          }) 
        },
        {
          translateX: animation.interpolate({
            inputRange: [0, 1],
            outputRange: [0, translateX]
          })
        }
      ]
    };
  };

  return (
    <View style={{ alignItems: 'center', justifyContent: 'center' }}>
      {/* Backdrop overlay - appears behind menu options when menu is open */}
      {isOpen && (
        <Pressable 
          style={{
            position: 'absolute',
            top: -1000, // Extend well above the tab bar
            left: -500,
            right: -500,
            bottom: -100,
            backgroundColor: 'rgba(0,0,0,0.3)',
            zIndex: 50
          }}
          onPress={() => setIsOpen(false)} // Added onPress for better touch handling
        >
          <Animated.View 
            style={{
              width: '100%',
              height: '100%',
              opacity: backdropOpacity,
            }}
          />
        </Pressable>
      )}
      
      {/* The menu container for positioning options - IMPROVED POSITIONING */}
      <Animated.View
        style={{
          position: 'absolute',
          bottom: 75,
          left: 0, // Center absolutely
          right: 0,
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 100,
          pointerEvents: isOpen ? "auto" : "none"
        }}
      >
        {/* Menu options */}
        <View style={{
          flexDirection: 'row',
          justifyContent: 'center',
          width: '100%',
          position: 'absolute',
          bottom: 0
        }}>
          {menuOptions.map((option, index) => (
            <Animated.View 
              key={option.name}
              style={[
                getOptionTransform(option.animation, index),
                { alignItems: 'center' }
              ]}
            >
              {/* Option button */}
              <Pressable
                onPress={() => handleOptionPress(option.name)}
                style={({ pressed }) => [
                  styles.menuOption,
                  { 
                    backgroundColor: option.color,
                    transform: [{ scale: pressed ? 0.92 : 1 }],
                    shadowOpacity: pressed ? 0.1 : 0.25,
                  }
                ]}
                android_ripple={{ color: 'rgba(255,255,255,0.3)', borderless: true }}
              >
                <Ionicons 
                  name={`${option.icon}${index === 1 ? '' : '-outline'}`} 
                  size={22} 
                  color="#fff" 
                />
              </Pressable>
              
              {/* Option label */}
              <Animated.View 
                style={{
                  backgroundColor: 'rgba(255,255,255,0.95)',
                  paddingHorizontal: 8,
                  paddingVertical: 4,
                  borderRadius: 12,
                  marginTop: 8,
                  shadowColor: '#000',
                  shadowOffset: { width: 0, height: 2 },
                  shadowOpacity: 0.1,
                  shadowRadius: 3,
                  elevation: 3
                }}
              >
                <Text style={{ 
                  color: '#333', 
                  fontSize: 12, 
                  fontWeight: '600' 
                }}>
                  {option.name}
                </Text>
              </Animated.View>
            </Animated.View>
          ))}
        </View>
      </Animated.View>

      {/* Main add button */}
      <Animated.View style={{ 
        transform: [{ scale: buttonScaleAnim }],
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 3 },
        shadowOpacity: 0.2,
        shadowRadius: 6,
        elevation: 8
      }}>
        <Pressable
          onPress={handlePress}
          style={({ pressed }) => [
            {
              width: 60,
              height: 60,
              borderRadius: 30,
              backgroundColor: isOpen ? '#ff5277' : '#ff758f',
              alignItems: 'center',
              justifyContent: 'center',
              marginBottom: 20,
              borderWidth: 4,
              borderColor: 'rgba(255,255,255,0.25)',
              transform: [{ scale: pressed ? 0.95 : 1 }],
              shadowColor: '#000',
              shadowOffset: { width: 0, height: pressed ? 2 : 4 },
              shadowOpacity: pressed ? 0.15 : 0.25,
              shadowRadius: pressed ? 3 : 6,
              elevation: pressed ? 4 : 8,
            }
          ]}
        >
          <Animated.View style={{
            transform: [{ 
              rotate: menuOpenAnim.interpolate({
                inputRange: [0, 1],
                outputRange: ['0deg', '45deg']
              }) 
            }]
          }}>
            <Ionicons name="add" size={32} color="#fff" />
          </Animated.View>
        </Pressable>
      </Animated.View>
    </View>
  );
}

export default function TabLayout() {
  const { triggerHaptic } = useSettings();
  const insets = useSafeAreaInsets();
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: '#ff758f',
        tabBarInactiveTintColor: isDark ? '#666' : '#999',
        tabBarStyle: {
          position: 'absolute',
          bottom: insets.bottom + 10,
          left: 20,
          right: 20,
          elevation: 0,
          borderRadius: 20,
          height: 65,
          backgroundColor: isDark ? 'rgba(30, 30, 30, 0.9)' : 'rgba(255, 255, 255, 0.9)',
          borderTopWidth: 0,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: 0.1,
          shadowRadius: 12,
          paddingHorizontal: 10,
          paddingTop: 12,
          ...Platform.select({
            ios: { backdropFilter: 'blur(20px)' },
            android: { elevation: 8 },
          }),
        },
        tabBarItemStyle: {
          height: 54,
          marginTop: 3,
          justifyContent: 'center',
          alignItems: 'center',
        },
        tabBarShowLabel: false,
        headerTransparent: true,
        headerTitle: '',
        headerStyle: {
          backgroundColor: 'transparent',
        },
        headerShadowVisible: false,
        headerLeftContainerStyle: {
          paddingLeft: 16,
          paddingTop: 0,
        },
        headerRightContainerStyle: {
          paddingRight: 16,
          paddingTop: 0,
        },
      }}
      screenListeners={{
        tabPress: () => {
          triggerHaptic();
        },
      }}
    >
      <Tabs.Screen
        name="index"
        options={{
          tabBarIcon: ({ color, focused }) => (
            <TabBarIcon name="home" color={color} focused={focused} />
          ),
        }}
      />

      <Tabs.Screen
        name="workouts"
        options={{
          tabBarIcon: ({ color, focused }) => (
            <View style={[styles.iconContainer, focused && styles.activeIconContainer]}>
              <Ionicons name="barbell-outline" size={24} color={color} />
            </View>
          ),
        }}
      />

      {/* Replace the inline AddButton with our imported component */}
      <Tabs.Screen
        name="add"
        options={{
          tabBarButton: (props) => {
            const handlePress = () => {
              if (props.onPress) {
                // props.onPress();  // <-- wenn du NICHT navigieren willst, auskommentieren
              }
            };
            return (
              <AddButton
                onPress={handlePress}
                focused={!!props.accessibilityState?.selected}
              />
            );
          },
          headerShown: false,
        }}
      />

      <Tabs.Screen
        name="wellness"
        options={{
          tabBarIcon: ({ color, focused }) => (
            <View style={[styles.iconContainer, focused && styles.activeIconContainer]}>
              <Ionicons name="flower-outline" size={24} color={color} />
            </View>
          ),
        }}
      />

      {/* Enhanced AI-Tab with dropdown menu */}
      <Tabs.Screen
        name="ai"
        options={{
          tabBarButton: (props) => (
            <AITabButton 
              color={props.accessibilityState?.selected ? '#ff758f' : isDark ? '#666' : '#999'} 
              focused={!!props.accessibilityState?.selected} 
              onPress={props.onPress}
            />
          ),
        }}
      />

      {/* Hide the workout-generator and meal-planner from the tab bar */}
      <Tabs.Screen
        name="ai/workout-generator"
        options={{
          href: null, // This prevents it from being accessible directly via the tab bar
          headerShown: true,
        }}
      />
      
      <Tabs.Screen
        name="ai/meal-planner"
        options={{
          href: null, // This prevents it from being accessible directly via the tab bar
          headerShown: true,
        }}
      />
    </Tabs>
  );
}

const styles = StyleSheet.create({
  iconContainer: {
    width: 45,
    height: 45,
    borderRadius: 23,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'transparent',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 3,
      },
      android: {
        elevation: 3,
      },
    }),
  },
  activeIconContainer: {
    backgroundColor: '#ff758f15',
    transform: [{ translateY: -4 }],
  },
  menuOption: {
    width: 50,
    height: 50,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 15,
    borderWidth: 3,
    borderColor: 'rgba(255,255,255,0.25)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.25,
    shadowRadius: 5,
    elevation: 5,
  },
  // New styles for AI dropdown menu
  aiDropdownContainer: {
    position: 'absolute',
    bottom: 60,
    width: 220,
    borderRadius: 18,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.12,
    shadowRadius: 14,
    elevation: 10,
    zIndex: 999,
  },
  
  aiDropdownBlur: {
    padding: 10,
    backgroundColor: 'rgba(255,255,255,0.85)',
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.7)',
  },
  
  dropdownArrow: {
    width: 14,
    height: 14,
    backgroundColor: 'rgba(255,255,255,0.95)',
    position: 'absolute',
    bottom: -6,
    left: '50%',
    marginLeft: -7,
    transform: [{ rotate: '45deg' }],
    borderWidth: 1,
    borderLeftWidth: 0,
    borderTopWidth: 0,
    borderColor: 'rgba(255,255,255,0.8)',
    shadowColor: '#000',
    shadowOffset: { width: 1, height: 1 },
    shadowOpacity: 0.06,
    shadowRadius: 1,
  },
  
  aiFeatureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 12,
    borderRadius: 12,
    marginBottom: 2,
  },
  
  featureIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    borderWidth: 1,
    borderColor: 'rgba(255,107,156,0.2)',
  },
  
  featureText: {
    fontSize: 15,
    fontWeight: '500',
    color: '#333',
    flexGrow: 1,
  },
  
  activeDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#FF558D',
    marginLeft: 8,
  },
});

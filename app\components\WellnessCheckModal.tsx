import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Platform,
  Dimensions,
  Animated,
  ScrollView
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import { BlurView } from 'expo-blur';

const { width, height } = Dimensions.get('window');

interface WellnessResponses {
  mood: number;
  energy: number;
  sleep: number;
  [key: string]: number;
}

type WellnessCategory = 'mood' | 'energy' | 'sleep';

interface WellnessCheckModalProps {
  visible: boolean;
  onClose: () => void;
  onSubmit: (responses: WellnessResponses) => void;
  initialResponses?: WellnessResponses;
  hapticEnabled?: boolean;
}

const WellnessCheckModal: React.FC<WellnessCheckModalProps> = ({
  visible,
  onClose,
  onSubmit,
  initialResponses = { mood: 0, energy: 0, sleep: 0 },
  hapticEnabled = true
}) => {
  const [responses, setResponses] = useState<WellnessResponses>(initialResponses);
  const [animatedValues] = useState({
    container: new Animated.Value(0),
    content: new Animated.Value(0),
    categories: [
      new Animated.Value(0),
      new Animated.Value(0),
      new Animated.Value(0)
    ]
  });

  useEffect(() => {
    if (visible) {
      animateIn();
    } else {
      // Reset responses when modal is closed
      setResponses(initialResponses);
    }
  }, [visible]);

  const animateIn = () => {
    // Reset values
    animatedValues.container.setValue(0);
    animatedValues.content.setValue(0);
    animatedValues.categories.forEach(anim => anim.setValue(0));
    
    // Start animations in sequence
    Animated.sequence([
      Animated.timing(animatedValues.container, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true
      }),
      Animated.timing(animatedValues.content, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true
      }),
      Animated.stagger(100, [
        ...animatedValues.categories.map(anim => 
          Animated.spring(anim, {
            toValue: 1,
            tension: 50,
            friction: 7,
            useNativeDriver: true
          })
        )
      ])
    ]).start();
  };

  const animateOut = (callback?: () => void) => {
    Animated.parallel([
      Animated.timing(animatedValues.container, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true
      }),
      Animated.timing(animatedValues.content, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true
      })
    ]).start(callback);
  };

  const handleClose = () => {
    animateOut(onClose);
  };

  const handleSubmit = () => {
    if (!responses.mood || !responses.energy || !responses.sleep) {
      triggerHaptic('Warning');
      return;
    }
    
    triggerHaptic('Success');
    animateOut(() => onSubmit(responses));
  };

  const handleRatingSelect = (category: WellnessCategory, rating: number) => {
    setResponses(prev => ({
      ...prev,
      [category]: rating
    }));
    triggerHaptic('Selection');
  };

  const triggerHaptic = (type: 'Selection' | 'Success' | 'Warning') => {
    if (!hapticEnabled || (Platform.OS !== 'ios' && Platform.OS !== 'android')) return;
    
    try {
      if (type === 'Selection') {
        Haptics.selectionAsync();
      } else if (type === 'Success') {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      } else if (type === 'Warning') {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
      }
    } catch (error) {
      console.debug('Haptics not available on this platform');
    }
  };

  const getMoodEmoji = (rating: number) => {
    return rating === 1 ? '😔' : 
           rating === 2 ? '😐' : 
           rating === 3 ? '🙂' : 
           rating === 4 ? '😊' : '😁';
  };

  const getEnergyIcon = (rating: number) => {
    return (
      <View style={[styles.energyIcon, {opacity: rating / 5}]}>
        <Ionicons 
          name="battery" 
          size={22} 
          color="#fff" 
        />
      </View>
    );
  };

  const getSleepIcon = (rating: number) => {
    return (
      <View style={[styles.sleepIcon, {opacity: rating / 5}]}>
        <Ionicons 
          name="moon" 
          size={22} 
          color="#fff" 
        />
      </View>
    );
  };

  const renderRatingButtons = (category: WellnessCategory): JSX.Element[] => {
    return [1, 2, 3, 4, 5].map((rating: number) => {
      const isSelected: boolean = responses[category] === rating;
      let displayElement, gradientColors, pressedColors;
      
      if (category === 'mood') {
        displayElement = <Text style={styles.emojiText}>{getMoodEmoji(rating)}</Text>;
        gradientColors = isSelected ? ['#FF85B3', '#FF4D8C'] : ['#f6f6f6', '#eeeeee'];
        pressedColors = ['#FF4D8C', '#DF3A7B'];
      } else if (category === 'energy') {
        displayElement = getEnergyIcon(rating);
        gradientColors = isSelected ? ['#6B8CFF', '#4B6FE0'] : ['#f6f6f6', '#eeeeee'];
        pressedColors = ['#4B6FE0', '#3A5ECF'];
      } else { // sleep
        displayElement = getSleepIcon(rating);
        gradientColors = isSelected ? ['#9DCEFF', '#6BA3FF'] : ['#f6f6f6', '#eeeeee'];
        pressedColors = ['#6BA3FF', '#5A92EE'];
      }
      
      return (
        <TouchableOpacity
          key={`${category}-${rating}`}
          activeOpacity={0.8}
          style={styles.ratingButtonContainer}
          onPress={() => handleRatingSelect(category, rating)}
        >
          <LinearGradient
            colors={gradientColors}
            style={[
              styles.ratingButton,
              isSelected && styles.selectedRatingButton
            ]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            {displayElement}
            <Text style={[
              styles.ratingButtonText,
              isSelected && styles.selectedRatingButtonText
            ]}>
              {rating}
            </Text>
          </LinearGradient>
        </TouchableOpacity>
      );
    });
  };

  const containerAnimation = {
    opacity: animatedValues.container
  };

  const contentAnimation = {
    opacity: animatedValues.content,
    transform: [
      {
        translateY: animatedValues.content.interpolate({
          inputRange: [0, 1],
          outputRange: [50, 0]
        })
      }
    ]
  };

  if (!visible) return null;

  return (
    <Modal
      animationType="none"
      transparent={true}
      visible={visible}
      onRequestClose={handleClose}
    >
      <Animated.View style={[styles.overlay, containerAnimation]}>
        {Platform.OS === 'ios' ? (
          <BlurView intensity={30} style={styles.blur}>
            <TouchableOpacity 
              style={styles.blurTouchable} 
              onPress={handleClose}
              activeOpacity={1}
            />
          </BlurView>
        ) : (
          <TouchableOpacity 
            style={styles.overlayBackground} 
            onPress={handleClose}
            activeOpacity={1}
          />
        )}
        
        <Animated.View style={[styles.modalContainer, contentAnimation]}>
          <ScrollView 
            contentContainerStyle={styles.scrollContent}
            showsVerticalScrollIndicator={false}
          >
            <View style={styles.headerContainer}>
              <View style={styles.headerImageContainer}>
                <Ionicons name="flower-outline" size={32} color="#FF6B8C" />
              </View>
              <Text style={styles.modalTitle}>Daily Wellness Check</Text>
              <Text style={styles.modalSubtitle}>
                How are you feeling today? This helps us personalize your experience.
              </Text>
            </View>

            {['mood', 'energy', 'sleep'].map((category, index) => (
              <Animated.View 
                key={category}
                style={[
                  styles.ratingContainer,
                  {
                    opacity: animatedValues.categories[index],
                    transform: [{
                      translateX: animatedValues.categories[index].interpolate({
                        inputRange: [0, 1],
                        outputRange: [-20, 0]
                      })
                    }]
                  }
                ]}
              >
                <View style={styles.ratingLabelContainer}>
                  <Text style={styles.ratingLabel}>
                    {category.charAt(0).toUpperCase() + category.slice(1)}
                  </Text>
                  <Text style={styles.ratingHelperText}>
                    {category === 'mood' 
                      ? 'How do you feel emotionally?' 
                      : category === 'energy' 
                        ? 'How energetic are you?' 
                        : 'How well did you sleep?'}
                  </Text>
                </View>
                <View style={styles.ratingButtons}>
                  {renderRatingButtons(category as WellnessCategory)}
                </View>
              </Animated.View>
            ))}
            
            <View style={styles.modalActions}>
              <TouchableOpacity
                style={styles.modalCancelButton}
                onPress={handleClose}
                activeOpacity={0.7}
              >
                <Text style={styles.modalCancelButtonText}>Skip</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.modalSubmitButton,
                  (!responses.mood || !responses.energy || !responses.sleep) && 
                  styles.modalSubmitButtonDisabled
                ]}
                onPress={handleSubmit}
                activeOpacity={0.8}
              >
                <LinearGradient
                  colors={['#FF85B3', '#FF4D8C']}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                  style={styles.modalSubmitButtonGradient}
                >
                  <Text style={styles.modalSubmitButtonText}>Submit</Text>
                  <Ionicons name="arrow-forward" size={18} color="#fff" />
                </LinearGradient>
              </TouchableOpacity>
            </View>
          </ScrollView>
        </Animated.View>
      </Animated.View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  blur: {
    position: 'absolute',
    width: '100%',
    height: '100%',
  },
  blurTouchable: {
    width: '100%',
    height: '100%',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
  },
  overlayBackground: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContainer: {
    width: width * 0.9,
    maxWidth: 400,
    maxHeight: height * 0.9,
    backgroundColor: '#fff',
    borderRadius: 28,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.15,
    shadowRadius: 20,
    elevation: 10,
  },
  scrollContent: {
    padding: 24,
  },
  headerContainer: {
    alignItems: 'center',
    marginBottom: 24,
  },
  headerImageContainer: {
    width: 72,
    height: 72,
    borderRadius: 36,
    backgroundColor: 'rgba(255, 107, 140, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#222',
    textAlign: 'center',
    marginBottom: 8,
  },
  modalSubtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 22,
    paddingHorizontal: 12,
  },
  ratingContainer: {
    marginBottom: 24,
  },
  ratingLabelContainer: {
    marginBottom: 12,
  },
  ratingLabel: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  ratingHelperText: {
    fontSize: 14,
    color: '#888',
  },
  ratingButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  ratingButtonContainer: {
    flex: 1,
    marginHorizontal: 4,
  },
  ratingButton: {
    borderRadius: 16,
    paddingVertical: 16,
    paddingHorizontal: 4,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  selectedRatingButton: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },
  ratingButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#666',
    marginTop: 6,
  },
  selectedRatingButtonText: {
    color: '#fff',
  },
  emojiText: {
    fontSize: 28,
  },
  energyIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#6B8CFF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  sleepIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#9DCEFF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  modalCancelButton: {
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#ddd',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  modalCancelButtonText: {
    color: '#666',
    fontSize: 16,
    fontWeight: '600',
  },
  modalSubmitButton: {
    flex: 1,
    borderRadius: 16,
    overflow: 'hidden',
  },
  modalSubmitButtonDisabled: {
    opacity: 0.6,
  },
  modalSubmitButtonGradient: {
    paddingVertical: 16,
    paddingHorizontal: 24,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalSubmitButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    marginRight: 8,
  },
});

export default WellnessCheckModal;

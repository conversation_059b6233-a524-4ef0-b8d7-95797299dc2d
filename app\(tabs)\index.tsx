import React, { useEffect, useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Dimensions,
  Platform,
  Modal,
  TextInput,
  Animated,
  StatusBar,
  SafeAreaView,
  LogBox,
  Alert
} from 'react-native';
import {
  LinearGradient
} from 'expo-linear-gradient';
import { Ionicons, FontAwesome5, MaterialCommunityIcons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import { useSettings } from '../../context/SettingsContext';
import { supabase } from '@/lib/supabase';
import { useRouter } from "expo-router";
import { createWaterReminderNotification, createPeriodNotification } from '@/utils/notifications';
import SkeletonPlaceholder from "@/components/SkeletonPlaceholder";
import AsyncStorage from '@react-native-async-storage/async-storage';
import { BlurView } from 'expo-blur';
import { format } from 'date-fns';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import SafeAreaLayout from '../../components/SafeAreaLayout';
import WellnessCheckModal from '@/app/components/WellnessCheckModal';

const screenWidth = Dimensions.get('window').width;

// Define types for better TypeScript support
interface WellnessCheck {
  id: string;
  user_id: string;
  check_date: string;
  created_at: string;
  mood_rating: number;
  energy_rating: number;
  sleep_rating: number;
}

interface ActivityData {
  steps: number;
  calories: number;
  activeMinutes: number;
  sleepHours: number;
}

interface UserProfile {
  name: string;
  email: string;
  avatar_url: string;
  age: string | number;
  weight: string | number;
  height: string | number;
  goal: string;
}

interface Workout {
  id: number;
  name: string;
  duration: number;
  difficulty: string;
  icon: string | null;
}

// Fallback workout suggestions when no workout is assigned
const workoutSuggestions = [
  { title: "Rest & Recovery", description: "Take it easy today, girly! Focus on hydration and stretching.", icon: "heart-circle-outline" },
  { title: "Quick HIIT Session", description: "Boost your energy with a quick 15-min cardio routine.", icon: "fitness-outline" },
  { title: "Mindful Movement", description: "Try some gentle yoga to improve flexibility and reduce stress.", icon: "leaf-outline" },
  { title: "Dance It Out", description: "Put on your favorite playlist and dance for 20 minutes!", icon: "musical-notes-outline" },
  { title: "Strength Focus", description: "Build those curves with some bodyweight exercises.", icon: "barbell-outline" },
  { title: "Cardio & Chill", description: "Get your heart pumping with a fun cardio session.", icon: "pulse-outline" }
];

const HomeScreen = () => {
  const { hapticEnabled } = useSettings();
  const [profile, setProfile] = useState<UserProfile>({
    name: "Beautiful",
    email: "",
    avatar_url: "",
    age: "N/A",
    weight: "N/A",
    height: "N/A",
    goal: "No specific goal"
  });

  // Admin state
  const [isAdmin, setIsAdmin] = useState(false);

  // -------------------------------------
  // STATES
  // -------------------------------------
  const [waterIntake, setWaterIntake] = useState(0);
  const [waterGoal, setWaterGoal] = useState(2500);
  const [showWaterModal, setShowWaterModal] = useState(false);
  const [waterAmount, setWaterAmount] = useState('');
  const [wellnessResponses, setWellnessResponses] = useState({
    mood: 0,
    energy: 0,
    sleep: 0,
  });
  const [lastWaterIntakeDate, setLastWaterIntakeDate] = useState<string>('');
  const [showWaterReminder, setShowWaterReminder] = useState(false);
  const [todayWellnessData, setTodayWellnessData] = useState<WellnessCheck | null>(null);
  const [activityData, setActivityData] = useState<ActivityData>({
    steps: 0,
    calories: 0,
    activeMinutes: 0,
    sleepHours: 0,
  });
  const [todayWorkout, setTodayWorkout] = useState<Workout | null>(null);
  const [showWellnessCheck, setShowWellnessCheck] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  const router = useRouter();

  // Animation states
  const [isLoading, setIsLoading] = useState(true);
  const scrollY = useRef(new Animated.Value(0)).current;
  const [visibleSections, setVisibleSections] = useState({
    target: false,
    wellness: false,
    quickActions: false
  });

  const insets = useSafeAreaInsets();

  // Check if wellness check was already done today
  const checkWellnessStatus = async () => {
    try {
      // 1. Check authentication status FIRST
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        // If not logged in, ensure the modal is not shown and exit
        setShowWellnessCheck(false);
        return;
      }

      // 2. If authenticated, proceed with the date check
      const today = new Date().toISOString().split('T')[0];
      const lastCheck = await AsyncStorage.getItem('lastWellnessCheckDate');

      let shouldShowCheck = false;
      if (lastCheck !== today) {
        // Tentatively set to show check only if authenticated and not done today via AsyncStorage
        shouldShowCheck = true;
      }

      // 3. Fetch today's wellness data if it exists (user is confirmed non-null here)
      const { data, error } = await supabase
        .from('wellness_checks')
        .select('id, mood_rating, energy_rating, sleep_rating') // Select only needed fields
        .eq('user_id', user.id)
        .eq('check_date', today)
        .maybeSingle();

      if (error) {
         console.error('Error fetching today wellness data:', error);
         // Keep shouldShowCheck as is, maybe the check should still appear if DB fetch fails
      }

      if (data) {
        // If data exists in DB for today, definitely don't show the check
        shouldShowCheck = false;
        setTodayWellnessData(data as WellnessCheck); // Cast needed if structure differs slightly
        setWellnessResponses({
          mood: data.mood_rating,
          energy: data.energy_rating,
          sleep: data.sleep_rating
        });
      } else if (shouldShowCheck) {
         // Reset existing data if no check found for today but modal should show
         setTodayWellnessData(null);
         setWellnessResponses({ mood: 0, energy: 0, sleep: 0 });
      }

      // 4. Set the final state based on checks
      setShowWellnessCheck(shouldShowCheck);

    } catch (error) {
      console.error('Error checking wellness status:', error);
      // Hide modal on generic error to prevent unexpected behavior
      setShowWellnessCheck(false);
    }
  };

  // Fetch user profile
  const fetchProfile = async () => {
    try {
      const { data: authUser, error: authError } = await supabase.auth.getUser();

      if (authError || !authUser?.user) {
        router.replace("/login");
        throw new Error("User is not authenticated");
      }

      const userId = authUser.user.id;

      // Updated to also fetch is_admin from profiles table
      const { data: profileData, error: profileError } = await supabase
        .from("profiles")
        .select("full_name, email, avatar_url, is_admin")
        .eq("id", userId)
        .maybeSingle();

      if (profileError) {
        console.error("Database error:", profileError);
        throw profileError;
      }

      // Also fetch user data from the users table
      const { data: userData, error: dbError } = await supabase
        .from("users")
        .select("full_name, email, profile_picture_url, age, weight, height, goal, daily_water_goal_ml")
        .eq("id", userId)
        .maybeSingle();

      if (dbError) {
        console.error("Database error:", dbError);
        throw dbError;
      }

      if (profileData) {
        // Update isAdmin state based on profile data
        setIsAdmin(profileData.is_admin || false);
      }

      if (userData) {
        setProfile({
          name: userData.full_name || "Beautiful",
          email: userData.email || "",
          avatar_url: userData.profile_picture_url || "",
          age: userData.age || "N/A",
          weight: userData.weight || "N/A",
          height: userData.height || "N/A",
          goal: userData.goal || "No specific goal"
        });
        
        // Set water goal if available
        if (userData.daily_water_goal_ml) {
          setWaterGoal(userData.daily_water_goal_ml);
        }
      }
    } catch (error) {
      console.error("Error fetching profile:", error);
      Alert.alert(
        "Error",
        "Could not load profile. Please try again."
      );
    }
  };

  // Fetch user activities
  const fetchUserActivities = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const today = new Date().toISOString().split('T')[0];

      const { data, error } = await supabase
        .from('user_activities')
        .select('water_intake_ml, foot_steps, calories, exercise_duration, sleep_hours')
        .eq('user_id', user.id)
        .eq('activity_date', today)
        .order('created_at', { ascending: false })
        .limit(1);

      if (error) {
        console.error('Error fetching activities:', error);
        return;
      }

      // Get the first item if exists
      const latestActivity = data?.[0];
      if (latestActivity) {
        setWaterIntake(latestActivity.water_intake_ml || 0);
        
        // Set activity data
        setActivityData({
          steps: latestActivity.foot_steps || 0,
          calories: latestActivity.calories || 0,
          activeMinutes: latestActivity.exercise_duration || 0,
          sleepHours: latestActivity.sleep_hours || 0,
        });
      } else {
        // No activities found for today
        setWaterIntake(0);
        
        // Set mock activity data when no data available
        setActivityData({
          steps: 0,
          calories: 0,
          activeMinutes: 0,
          sleepHours: 0,
        });
      }

      // Check period tracking
      const { data: periodData } = await supabase
        .from('period_tracking')
        .select('*')
        .eq('user_id', user.id)
        .order('period_start_date', { ascending: false })
        .limit(1)
        .maybeSingle();

      if (periodData) {
        const lastPeriodStart = new Date(periodData.period_start_date);
        const nextPeriodStart = new Date(lastPeriodStart);
        nextPeriodStart.setDate(nextPeriodStart.getDate() + (periodData.cycle_length || 28));

        const today = new Date();
        const daysUntilNextPeriod = Math.ceil((nextPeriodStart.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));

        // Send notification 3 days before next period
        if (daysUntilNextPeriod <= 3 && daysUntilNextPeriod > 0) {
          await createPeriodNotification(user.id, daysUntilNextPeriod);
        }
      }

    } catch (error) {
      console.error('Error fetching user activities:', error);
    }
  };

  // Fetch today's workout
  const fetchTodayWorkout = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      // Get today's date in user's timezone
      const today = new Date().toLocaleDateString('en-CA'); // YYYY-MM-DD format

      const { data, error } = await supabase
        .from('assigned_workouts')
        .select(`
          id,
          workout:workouts (
            id,
            name,
            duration,
            difficulty,
            icon
          )
        `)
        .eq('user_id', user.id)
        .eq('assigned_date', today)
        .maybeSingle();

      if (error) throw error;

      if (data?.workout) {
        setTodayWorkout(data.workout as Workout);
      }
    } catch (error) {
      console.error('Error fetching today workout:', error);
    }
  };

  // Check water intake
  const checkWaterIntake = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const today = new Date().toISOString().split('T')[0];

      const { data, error } = await supabase
        .from('user_activities')
        .select('water_intake_ml')
        .eq('user_id', user.id)
        .eq('activity_date', today)
        .maybeSingle();

      if (error) throw error;

      // If no water intake today and it's past 10 AM, send a reminder
      const currentHour = new Date().getHours();
      if ((!data || !data.water_intake_ml) && currentHour >= 10) {
        await createWaterReminderNotification(user.id);
      }

      setWaterIntake(data?.water_intake_ml || 0);
      setLastWaterIntakeDate(today);
    } catch (error) {
      console.error('Error checking water intake:', error);
    }
  };

  // Update water intake
  const handleAddWater = async () => {
    try {
      const amount = parseInt(waterAmount);
      if (isNaN(amount) || amount <= 0) {
        Alert.alert('Invalid Amount', 'Please enter a valid water amount');
        return;
      }

      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const today = new Date().toISOString().split('T')[0];

      const { data: existingEntry } = await supabase
        .from('user_activities')
        .select('id, water_intake_ml')
        .eq('user_id', user.id)
        .eq('activity_date', today)
        .single();

      if (existingEntry) {
        const newTotal = (existingEntry.water_intake_ml || 0) + amount;
        const { error } = await supabase
          .from('user_activities')
          .update({ water_intake_ml: newTotal })
          .eq('id', existingEntry.id);

        if (error) throw error;
        setWaterIntake(newTotal);
      } else {
        const { error } = await supabase
          .from('user_activities')
          .insert([
            {
              user_id: user.id,
              activity_date: today,
              water_intake_ml: amount,
              foot_steps: activityData.steps,
              calories: activityData.calories,
              exercise_duration: activityData.activeMinutes,
              sleep_hours: activityData.sleepHours
            }
          ]);

        if (error) throw error;
        setWaterIntake(amount);
      }

      setWaterAmount('');
      setShowWaterModal(false);
      triggerHaptic();
      Alert.alert('Success', 'Water intake updated successfully');
    } catch (error) {
      console.error('Error updating water intake:', error);
      Alert.alert('Error', 'Failed to update water intake');
    }
  };

  useEffect(() => {
    const loadInitialData = async () => {
      setIsLoading(true);
      await fetchProfile();
      await fetchUserActivities();
      await fetchTodayWorkout();
      await checkWaterIntake();
      await checkWellnessStatus();
      setIsLoading(false);
      
      // Animate sections into view after data is loaded
      setTimeout(() => {
        setVisibleSections(prev => ({ ...prev, target: true }));
      }, 100);
      
      setTimeout(() => {
        setVisibleSections(prev => ({ ...prev, wellness: true }));
      }, 300);
      
      setTimeout(() => {
        setVisibleSections(prev => ({ ...prev, quickActions: true }));
      }, 500);
    };

    loadInitialData();

    // Check for workouts every 2 minutes
    const workoutInterval = setInterval(() => {
      fetchTodayWorkout();
    }, 2 * 60 * 1000);

    // Check water intake every 30 minutes
    const waterInterval = setInterval(() => {
      checkWaterIntake();
    }, 30 * 60 * 1000);

    // Reset notification flags at midnight
    const midnightCheck = setInterval(() => {
      const now = new Date();
      if (now.getHours() === 0 && now.getMinutes() === 0) {
        fetchTodayWorkout();
        checkWellnessStatus(); // Check for wellness check at midnight
      }
    }, 60 * 1000); // Check every minute for midnight

    return () => {
      clearInterval(workoutInterval);
      clearInterval(waterInterval);
      clearInterval(midnightCheck);
    };
  }, []);

  // -------------------------------------
  // HAPTICS (Vibration)
  // -------------------------------------
  const triggerHaptic = async () => {
    if (!hapticEnabled || (Platform.OS !== 'ios' && Platform.OS !== 'android')) return;
    try {
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    } catch (error) {
      console.debug('Haptics nicht verfügbar auf dieser Plattform');
    }
  };

  // Submit wellness check
  const submitWellnessCheck = async (wellnessResponses: {
    mood: number;
    energy: number;
    sleep: number;
  }) => {
    try {
      // Validate that user selected all ratings
      if (!wellnessResponses.mood || !wellnessResponses.energy || !wellnessResponses.sleep) {
        Alert.alert('Incomplete Check-in', 'Please rate all categories to complete your check-in');
        return;
      }
      
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const today = new Date().toISOString().split('T')[0];

      const { error } = await supabase
        .from('wellness_checks')
        .insert([
          {
            user_id: user.id,
            check_date: today,
            mood_rating: wellnessResponses.mood,
            energy_rating: wellnessResponses.energy,
            sleep_rating: wellnessResponses.sleep
          }
        ]);

      if (error) throw error;

      // Save today's date to AsyncStorage to prevent showing check again today
      await AsyncStorage.setItem('lastWellnessCheckDate', today);
      
      // Update local state with check data
      setTodayWellnessData({
        id: '', // Will be generated by the database
        user_id: user.id,
        check_date: today,
        created_at: new Date().toISOString(),
        mood_rating: wellnessResponses.mood,
        energy_rating: wellnessResponses.energy,
        sleep_rating: wellnessResponses.sleep
      });

      setShowWellnessCheck(false);
      triggerHaptic();
      Alert.alert('Success', 'Thank you for checking in! 🌸');
    } catch (error) {
      console.error('Error saving wellness check:', error);
      Alert.alert('Error', 'Failed to save wellness check');
    }
  };

  // Render wellness rating buttons
  interface WellnessResponses {
    mood: number;
    energy: number;
    sleep: number;
    [key: string]: number; // Index signature for dynamic access
  }
  
  type WellnessCategory = 'mood' | 'energy' | 'sleep';

  const renderRatingButtons = (category: WellnessCategory): JSX.Element[] => {
    return [1, 2, 3, 4, 5].map((rating: number) => {
      const isSelected: boolean = wellnessResponses[category] === rating;
      let emoji: string, color: string;
      
      if (category === 'mood') {
        emoji = rating === 1 ? '😔' : rating === 2 ? '😐' : rating === 3 ? '🙂' : rating === 4 ? '😊' : '😁';
        color = isSelected ? '#FF6B8C' : '#f0f0f0';
      } else if (category === 'energy') {
        emoji = rating === 1 ? '🔋' : rating === 2 ? '🔋' : rating === 3 ? '🔋' : rating === 4 ? '🔋' : '🔋';
        color = isSelected ? '#6B8CFF' : '#f0f0f0';
      } else { // sleep
        emoji = rating === 1 ? '😴' : rating === 2 ? '😴' : rating === 3 ? '😴' : rating === 4 ? '😴' : '😴';
        color = isSelected ? '#9DCEFF' : '#f0f0f0';
      }
      
      return (
        <TouchableOpacity
          key={`${category}-${rating}`}
          style={[
            styles.ratingButton,
            { backgroundColor: color }
          ]}
          onPress={() => {
            setWellnessResponses((prev: WellnessResponses) => ({
              ...prev,
              [category]: rating
            }));
            triggerHaptic();
          }}
        >
          <Text style={[
            styles.emojiText,
            isSelected && styles.selectedEmoji
          ]}>{emoji}</Text>
          <Text style={[
            styles.ratingButtonText,
            isSelected && styles.ratingButtonTextSelected
          ]}>{rating}</Text>
        </TouchableOpacity>
      );
    });
  };

  // Animation for header based on scroll position
  const headerOpacity = scrollY.interpolate({
    inputRange: [0, 100],
    outputRange: [1, 0.98],
    extrapolate: 'clamp',
  });

  const headerTranslate = scrollY.interpolate({
    inputRange: [0, 100],
    outputRange: [0, -10],
    extrapolate: 'clamp',
  });

  const headerElevation = scrollY.interpolate({
    inputRange: [0, 20],
    outputRange: [0, 8],
    extrapolate: 'clamp',
  });

  const handleScroll = Animated.event(
    [{ nativeEvent: { contentOffset: { y: scrollY } } }],
    { useNativeDriver: false }
  );

  // Get wellness data for display
  const getWellnessDescription = () => {
    if (!todayWellnessData) return "Check in to track your wellness";
    
    const moodRating = todayWellnessData.mood_rating;
    const energyRating = todayWellnessData.energy_rating;
    const sleepRating = todayWellnessData.sleep_rating;
    
    let description = '';
    
    // Generate a personalized message based on wellness check
    if (moodRating <= 2 && energyRating <= 2) {
      return "Rest day recommended - you need to recharge 💗";
    } else if (moodRating >= 4 && energyRating >= 4) {
      return "You're feeling great today! Perfect for a challenge 💪";
    } else if (sleepRating <= 2) {
      return "Low sleep detected - take it easy today 🌙";
    } else {
      return "You're having a balanced day. Stay focused! 🌸";
    }
  };

  // Render the wellness mood emoji based on rating
  const renderWellnessMoodEmoji = () => {
    if (!todayWellnessData) return "😊";
    
    const rating = todayWellnessData.mood_rating;
    return rating === 1 ? '😔' : 
           rating === 2 ? '😐' : 
           rating === 3 ? '🙂' : 
           rating === 4 ? '😊' : '😁';
  };

  // Render skeleton loading states or actual content
  const renderSkeletonOrContent = () => {
    if (isLoading) {
      return (
        <>
          <View style={styles.skeletonHeader}>
            <SkeletonPlaceholder>
              <SkeletonPlaceholder.Item width={200} height={20} marginBottom={8} borderRadius={4} />
              <SkeletonPlaceholder.Item width={150} height={30} borderRadius={4} />
            </SkeletonPlaceholder>
            
            <View style={styles.headerActions}>
              <SkeletonPlaceholder>
                <SkeletonPlaceholder.Item width={44} height={44} borderRadius={12} />
              </SkeletonPlaceholder>
              <SkeletonPlaceholder>
                <SkeletonPlaceholder.Item width={44} height={44} borderRadius={12} />
              </SkeletonPlaceholder>
            </View>
          </View>
          
          <View style={styles.targetSection}>
            <SkeletonPlaceholder>
              <SkeletonPlaceholder.Item width={150} height={24} borderRadius={4} marginBottom={16} />
              <SkeletonPlaceholder.Item width={'100%'} height={80} borderRadius={16} marginBottom={16} />
              <SkeletonPlaceholder.Item width={'100%'} height={80} borderRadius={16} />
            </SkeletonPlaceholder>
          </View>
          
          <View style={styles.activitySection}>
            <SkeletonPlaceholder>
              <SkeletonPlaceholder.Item width={150} height={24} borderRadius={4} marginBottom={16} />
              <SkeletonPlaceholder.Item width={'100%'} height={180} borderRadius={16} />
            </SkeletonPlaceholder>
          </View>
          
          <View style={styles.quickActionsSection}>
            <SkeletonPlaceholder>
              <SkeletonPlaceholder.Item width={150} height={24} borderRadius={4} marginBottom={16} />
              <View style={styles.skeletonGrid}>
                <SkeletonPlaceholder.Item width={'48%'} height={120} borderRadius={16} />
                <SkeletonPlaceholder.Item width={'48%'} height={120} borderRadius={16} />
                <SkeletonPlaceholder.Item width={'48%'} height={120} borderRadius={16} marginTop={12} />
                <SkeletonPlaceholder.Item width={'48%'} height={120} borderRadius={16} marginTop={12} />
              </View>
            </SkeletonPlaceholder>
          </View>
        </>
      );
    }
    
    return (
      <>
        {/* HEADER - Note we're not using SafeAreaView here anymore */}
        <Animated.View 
          style={[
            styles.header,
            {
              opacity: headerOpacity,
              transform: [{ translateY: headerTranslate }],
              elevation: headerElevation,
              // Apply inset at the top of header
              paddingTop: 20 + insets.top,
            }
          ]}
        >
          <View style={styles.headerLeftSection}>
            <View style={styles.adminButtonsContainer}>
              {isAdmin && (
                <View
                  from={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ type: 'spring', stiffness: 150, damping: 15 }}
                  style={styles.adminButtonContainer}
                >
                  <TouchableOpacity
                    style={styles.adminButton}
                    onPress={() => {
                      triggerHaptic();
                      router.push('/admin');
                    }}
                  >
                    <Ionicons name="shield-checkmark" size={18} color="#fff" />
                    <Text style={styles.adminButtonText}>Admin</Text>
                  </TouchableOpacity>
                </View>
              )}
              
              {/* New Onboarding Button */}
              <View
                from={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ type: 'spring', stiffness: 150, damping: 15 }}
                style={styles.adminButtonContainer}
              >
                <TouchableOpacity
                  style={styles.onboardingButton}
                  onPress={() => {
                    triggerHaptic();
                    router.push('/onboarding/onboardingscreen');
                  }}
                >
                  <Ionicons name="rocket-outline" size={18} color="#fff" />
                  <Text style={styles.adminButtonText}>Onboarding</Text>
                </TouchableOpacity>
              </View>
            </View>
            
            <View
              from={{ opacity: 0, translateY: 10 }}
              animate={{ opacity: 1, translateY: 0 }}
              transition={{ type: 'timing', duration: 500 }}
            >
              <Text style={styles.welcomeText}>Welcome Back,</Text>
            </View>
            <View
              from={{ opacity: 0, translateX: -20 }}
              animate={{ opacity: 1, translateX: 0 }}
              transition={{ type: 'timing', duration: 600, delay: 200 }}
            >
              <Text style={styles.userName}>{profile.name}</Text>
            </View>
          </View>
          <View style={styles.headerActions}>
            <View
              from={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ type: 'spring', stiffness: 150, damping: 15 }}
            >
              <TouchableOpacity
                style={styles.headerButton}
                onPress={() => {
                  triggerHaptic();
                  router.push('/profile');
                }}
              >
                {profile.avatar_url ? (
                  <Image 
                    source={{ uri: profile.avatar_url }} 
                    style={styles.avatarImage} 
                  />
                ) : (
                  <View style={styles.avatarPlaceholder}>
                    <Ionicons name="person" size={20} color="#FB8BB5" />
                  </View>
                )}
              </TouchableOpacity>
            </View>
            <View
              from={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ type: 'spring', stiffness: 150, damping: 15, delay: 100 }}
            >
              <TouchableOpacity
                style={styles.headerButton}
                onPress={() => {
                  triggerHaptic();
                  router.push('/notifications');
                }}
              >
                <Ionicons name="notifications-outline" size={22} color="#333" />
                <View
                  from={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ type: 'spring', stiffness: 150, damping: 15, delay: 600 }}
                  style={styles.notificationDot} 
                />
              </TouchableOpacity>
            </View>
          </View>
        </Animated.View>

        {/* WELLNESS SUMMARY */}
        <View
          from={{ opacity: 0, translateY: 20 }}
          animate={{ 
            opacity: visibleSections.wellness ? 1 : 0, 
            translateY: visibleSections.wellness ? 0 : 20 
          }}
          transition={{ type: 'timing', duration: 500 }}
          style={styles.wellnessSection}
        >
          <View style={styles.wellnessSummaryCard}>
            <View style={styles.wellnessMoodSection}>
              <View style={styles.wellnessMoodIconContainer}>
                <Text style={styles.wellnessMoodEmoji}>{renderWellnessMoodEmoji()}</Text>
              </View>
              <View style={styles.wellnessMoodTextContainer}>
                <Text style={styles.wellnessMoodTitle}>Today's Mood</Text>
                <Text style={styles.wellnessMoodDescription}>{getWellnessDescription()}</Text>
              </View>
            </View>
            <View style={styles.wellnessMetricsSection}>
              <View style={styles.wellnessMetric}>
                <FontAwesome5 name="walking" size={20} color="#6B8CFF" />
                <Text style={styles.wellnessMetricValue}>{activityData.steps}</Text>
                <Text style={styles.wellnessMetricLabel}>Steps</Text>
              </View>
              <View style={styles.wellnessMetric}>
                <FontAwesome5 name="fire" size={20} color="#FF6B8C" />
                <Text style={styles.wellnessMetricValue}>{activityData.calories}</Text>
                <Text style={styles.wellnessMetricLabel}>Calories</Text>
              </View>
              <View style={styles.wellnessMetric}>
                <FontAwesome5 name="clock" size={20} color="#9DCEFF" />
                <Text style={styles.wellnessMetricValue}>{activityData.activeMinutes}</Text>
                <Text style={styles.wellnessMetricLabel}>Active Min</Text>
              </View>
              <View style={styles.wellnessMetric}>
                <FontAwesome5 name="bed" size={20} color="#6B8CFF" />
                <Text style={styles.wellnessMetricValue}>{activityData.sleepHours}</Text>
                <Text style={styles.wellnessMetricLabel}>Sleep Hrs</Text>
              </View>
            </View>
          </View>
        </View>

        {/* TODAY TARGET */}
        <View
          from={{ opacity: 0, translateY: 20 }}
          animate={{ 
            opacity: visibleSections.target ? 1 : 0, 
            translateY: visibleSections.target ? 0 : 20 
          }}
          transition={{ type: 'timing', duration: 500 }}
          style={styles.targetSection}
        >
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Today Target</Text>
          </View>

          <View style={styles.targetContent}>
            {todayWorkout && (
              <View
                from={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ type: 'spring', stiffness: 100, damping: 15, delay: 200 }}
              >
                <TouchableOpacity
                  style={styles.todayWorkoutCard}
                  onPress={() => {
                    triggerHaptic();
                    router.push(`/workout/${todayWorkout.id}`);
                  }}
                >
                  <LinearGradient
                    colors={['#92A3FD', '#9DCEFF']}
                    style={styles.workoutCardGradient}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 1 }}
                  >
                    {todayWorkout.icon && (
                      <Image
                        source={{ uri: todayWorkout.icon }}
                        style={styles.workoutCardImage}
                      />
                    )}
                    <View style={styles.workoutCardContent}>
                      <Text style={styles.workoutCardTitle}>{todayWorkout.name}</Text>
                      <Text style={styles.workoutCardDetails}>
                        {todayWorkout.duration} min • {todayWorkout.difficulty}
                      </Text>
                    </View>
                    <View style={styles.workoutCardAction}>
                      <Ionicons name="arrow-forward" size={24} color="#fff" />
                    </View>
                  </LinearGradient>
                </TouchableOpacity>
              </View>
            )}

            <View
              from={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ type: 'spring', stiffness: 100, damping: 15, delay: 300 }}
            >
              <TouchableOpacity
                style={styles.reminderCard}
                onPress={() => {
                  triggerHaptic();
                  router.push('/wellness');
                }}
              >
                <LinearGradient
                  colors={['#FF9DC4', '#FF6B9C']}
                  style={styles.reminderCardGradient}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                >
                  <View style={styles.reminderIconContainer}>
                    <Ionicons name="flower" size={24} color="#fff" />
                  </View>

                  <View style={styles.reminderContent}>
                    <Text style={styles.reminderTitle}>Track Your Wellness 🌸</Text>
                    <Text style={styles.reminderText}>
                      Check your daily wellness and stay hydrated!
                    </Text>
                  </View>
                  <View style={styles.reminderAction}>
                    <Ionicons name="arrow-forward" size={24} color="#fff" />
                  </View>
                </LinearGradient>
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* Quick Actions Section */}
        <View
          from={{ opacity: 0, translateY: 40 }}
          animate={{ 
            opacity: visibleSections.quickActions ? 1 : 0, 
            translateY: visibleSections.quickActions ? 0 : 40 
          }}
          transition={{ type: 'timing', duration: 500 }}
          style={styles.quickActionsSection}
        >
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.quickActionsGrid}>
            {[
              {
                icon: "water-outline",
                title: "Water Intake",
                value: `${waterIntake} ml`,
                colors: ['#6B8CFF', '#4B6FE0'],
                route: '/water-tracker',
                delay: 100
              },
              {
                icon: "water-outline",
                title: "Onboarding",
                value: `${waterIntake} ml`,
                colors: ['#6B8CFF', '#4B6FE0'],
                route: '/onboarding/onboardingscreen',
                delay: 200
              },
              {
                icon: "calendar-outline",
                title: "Period Tracker",
                subtext: "Track your cycle",
                colors: ['#FF6B8C', '#E04B6F'],
                route: '/period-tracker',
                delay: 300
              },
              {
                icon: "body-outline",
                title: "BMI Score",
                subtext: "Calculate BMI",
                colors: ['#6B8CFF', '#4B6FE0'],
                route: '/bmi-calculator',
                delay: 400
              },
              {
                icon: "scale-outline",
                title: "Weight Tracker",
                colors: ['#92A3FD', '#9DCEFF'],
                route: '/weight-tracker',
                delay: 500
              },
              {
                icon: "fitness-outline",
                title: "Health Tracker",
                subtext: "Monitor vitals",
                colors: ['#FFB86B', '#E09B4B'],
                route: '/health-tracker',
                delay: 600
              }
            ].map((action, index) => (
              <View
                key={index}
                from={{ opacity: 0, translateY: 20, scale: 0.9 }}
                animate={{ opacity: 1, translateY: 0, scale: 1 }}
                transition={{ 
                  type: 'spring', 
                  delay: action.delay, 
                  stiffness: 100, 
                  damping: 10 
                }}
                style={{ width: '48%' }}
              >
                <TouchableOpacity
                  style={styles.quickActionCard}
                  onPress={() => {
                    triggerHaptic();
                    router.push(action.route);
                  }}
                >
                  <LinearGradient
                    colors={action.colors}
                    style={styles.iconContainer}
                  >
                    <Ionicons name={action.icon} size={24} color="#fff" />
                  </LinearGradient>
                  <Text style={styles.quickActionTitle}>{action.title}</Text>
                  {action.value && <Text style={styles.quickActionValue}>{action.value}</Text>}
                  {action.subtext && <Text style={styles.quickActionSubtext}>{action.subtext}</Text>}
                </TouchableOpacity>
              </View>
            ))}
          </View>
        </View>
      </>
    );
  };

  useEffect(() => {
    try {
      // Log that the component mounted successfully
      console.log('HomeScreen mounted successfully');
    } catch (error) {
      // Display any errors that occur during mounting
      console.error('Error in HomeScreen:', error);
      Alert.alert('Error', 'An error occurred while loading the home screen: ' + error.message);
    }
  }, []);

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="transparent" translucent />
      
      <Animated.ScrollView 
        style={styles.scrollContainer}
        onScroll={handleScroll}
        scrollEventThrottle={16}
        contentContainerStyle={{
          paddingBottom: insets.bottom + 20, // Add bottom padding for notch devices
        }}
      >
        {renderSkeletonOrContent()}
      </Animated.ScrollView>

      {/* Water Input Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={showWaterModal}
        onRequestClose={() => setShowWaterModal(false)}
      >
        <View
          from={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          style={styles.modalOverlay}
        >
          <View
            from={{ opacity: 0, scale: 0.9, translateY: 20 }}
            animate={{ opacity: 1, scale: 1, translateY: 0 }}
            exit={{ opacity: 0, scale: 0.9, translateY: 20 }}
            transition={{ type: 'spring', stiffness: 200, damping: 20 }}
            style={styles.modalContent}
          >
            <Text style={styles.modalTitle}>Add Water Intake</Text>
            <TextInput
              style={styles.waterInput}
              keyboardType="number-pad"
              placeholder="Enter amount in ml"
              value={waterAmount}
              onChangeText={setWaterAmount}
            />
            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => setShowWaterModal(false)}
              >
                <Text style={styles.buttonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.modalButton, styles.addButton]}
                onPress={handleAddWater}
              >
                <Text style={styles.buttonText}>Add</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Replace the old Wellness Check Modal with our new component */}
      <WellnessCheckModal
        visible={showWellnessCheck}
        onClose={() => setShowWellnessCheck(false)}
        onSubmit={submitWellnessCheck}
        initialResponses={wellnessResponses}
        hapticEnabled={hapticEnabled}
      />
    </SafeAreaView>
  );
};

// -------------------------------------
// STYLES
// -------------------------------------
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F7FF',
  },
  scrollContainer: {
    flex: 1,
  },
  header: {
    backgroundColor: '#fff',
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
    boxShadow: '0px 2px 3.84px rgba(0, 0, 0, 0.1)',
    elevation: 5,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingTop: 20, // This will be adjusted with insets.top
    paddingBottom: 20,
    marginBottom: 20,
  },
  welcomeText: {
    fontSize: 18,
    color: '#333',
  },
  userName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  headerButton: {
    backgroundColor: '#F5F7FF',
    padding: 10,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    height: 44,
    width: 44,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 6,
    elevation: 2,
    position: 'relative',
  },
  avatarImage: {
    width: 35,
    height: 35,
    borderRadius: 17.5,
    borderWidth: 2,
    borderColor: '#6B8CFF',
  },
  avatarPlaceholder: {
    width: 35,
    height: 35,
    borderRadius: 17.5,
    backgroundColor: '#E0E7FF',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#6B8CFF',
  },
  notificationDot: {
    position: 'absolute',
    width: 8,
    height: 8,
    backgroundColor: '#FF6B8C',
    borderRadius: 4,
    top: 8,
    right: 10,
    borderWidth: 1,
    borderColor: '#fff',
  },
  notificationButton: {
    backgroundColor: '#F5F7FF',
    padding: 10,
    borderRadius: 10,
  },
  targetSection: {
    padding: 20,
    backgroundColor: '#fff',
  },
  targetContent: {
    marginTop: 15,
    gap: 15,
  },
  todayWorkoutCard: {
    borderRadius: 20,
    overflow: 'hidden',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  workoutCardGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
  },
  workoutCardImage: {
    width: 60,
    height: 60,
    borderRadius: 12,
    marginRight: 15,
  },
  workoutCardContent: {
    flex: 1,
  },
  workoutCardTitle: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 5,
  },
  workoutCardDetails: {
    color: '#fff',
    opacity: 0.9,
    fontSize: 14,
  },
  workoutCardAction: {
    padding: 10,
  },
  reminderCard: {
    borderRadius: 20,
    overflow: 'hidden',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  reminderCardGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
  },
  reminderIconContainer: {
    width: 45,
    height: 45,
    borderRadius: 22.5,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  reminderContent: {
    flex: 1,
  },
  reminderTitle: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 1,
  },
  reminderText: {
    color: '#fff',
    opacity: 0.9,
    fontSize: 14,
  },
  reminderAction: {
    padding: 10,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderRadius: 25,
    padding: 20,
    width: '90%',
    maxWidth: 400,
  },
  modalTitle: {
    fontSize: 24,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 5,
    color: '#333',
  },
  modalSubtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
    color: '#666',
  },
  ratingContainer: {
    marginBottom: 20,
  },
  ratingLabel: {
    fontSize: 16,
    marginBottom: 10,
    color: '#333',
    fontWeight: '500',
  },
  ratingButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 10,
  },
  ratingButton: {
    flex: 1,
    backgroundColor: '#f0f0f0',
    padding: 12,
    borderRadius: 12,
    alignItems: 'center',
  },
  ratingButtonSelected: {
    backgroundColor: '#FF9DC4',
  },
  ratingButtonText: {
    fontSize: 16,
    color: '#666',
    fontWeight: '500',
  },
  ratingButtonTextSelected: {
    color: '#fff',
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
    gap: 15,
  },
  modalCancelButton: {
    flex: 1,
    padding: 15,
    borderRadius: 25,
    borderWidth: 1,
    borderColor: '#FF9DC4',
    alignItems: 'center',
  },
  modalCancelButtonText: {
    color: '#FF9DC4',
    fontSize: 16,
    fontWeight: '600',
  },
  modalSubmitButton: {
    flex: 1,
    borderRadius: 25,
    overflow: 'hidden',
  },
  modalSubmitButtonGradient: {
    padding: 15,
    alignItems: 'center',
  },
  modalSubmitButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  activitySection: {
    padding: 16,
  },
  heartRateCard: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 16,
    marginTop: 16,
    boxShadow: '0px 2px 3.84px rgba(0, 0, 0, 0.1)',
    elevation: 5,
  },
  heartRateValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  chartContainer: {
    marginTop: 16,
  },
  chart: {
    borderRadius: 16,
  },
  timeIndicator: {
    marginTop: 16,
  },
  timeText: {
    fontSize: 12,
    color: '#666',
  },
  statsCard: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 16,
    marginTop: 16,
    boxShadow: '0px 2px 3.84px rgba(0, 0, 0, 0.1)',
    elevation: 5,
  },
  statsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statsTitle: {
    fontSize: 16,
    color: '#333',
    marginLeft: 10,
  },
  statsValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#6B8CFF',
    marginTop: 10,
  },
  statsSubtext: {
    fontSize: 12,
    color: '#666',
    marginTop: 5,
  },
  caloriesCard: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 16,
    marginTop: 16,
    boxShadow: '0px 2px 3.84px rgba(0, 0, 0, 0.1)',
    elevation: 5,
  },
  caloriesTitle: {
    fontSize: 16,
    color: '#333',
  },
  caloriesContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 10,
  },
  caloriesValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#6B8CFF',
  },
  caloriesRemaining: {
    fontSize: 14,
    color: '#666',
  },
  quickActionsSection: {
    padding: 16,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 25,
    marginTop: 16,
  },
  quickActionCard: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 16,
    width: '48%',
    marginBottom: 16,
    alignItems: 'center',
    boxShadow: '0px 2px 3.84px rgba(0, 0, 0, 0.1)',
    elevation: 5,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  quickActionTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  quickActionValue: {
    fontSize: 16,
    color: '#6B8CFF',
    fontWeight: '600',
  },
  quickActionSubtext: {
    fontSize: 12,
    color: '#666',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 20,
    width: '90%',
    boxShadow: '0px 2px 3.84px rgba(0, 0, 0, 0.1)',
    elevation: 5,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  waterInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 10,
    padding: 10,
    width: '100%',
    marginBottom: 20,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  modalButton: {
    padding: 15,
    borderRadius: 10,
    width: '45%',
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: '#ff4444',
  },
  addButton: {
    backgroundColor: '#6B8CFF',
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  notificationButton: {
    backgroundColor: '#F5F7FF',
    padding: 10,
    borderRadius: 10,
  },
  skeletonHeader: {
    height: 200,
    backgroundColor: '#fff',
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  
  skeletonGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  
  // ...rest of existing styles...

  // Enhanced styles
  heartRateCard: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 16,
    marginTop: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
    transform: [{ translateY: 0 }],
  },
  
  quickActionCard: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 16,
    width: '100%', // Changed to 100% since parent View has width 48%
    marginBottom: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  headerLeftSection: {
    flex: 1,
  },
  
  adminButtonsContainer: {
    flexDirection: 'row',
    marginBottom: 8,
    gap: 10,
  },
  
  adminButtonContainer: {
    marginBottom: 8,
  },
  
  adminButton: {
    backgroundColor: '#FF6B8C',
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 16,
    alignSelf: 'flex-start',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  
  onboardingButton: {
    backgroundColor: '#6B8CFF',
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 16,
    alignSelf: 'flex-start',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  
  adminButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
    marginLeft: 4,
  },
  wellnessSection: {
    padding: 20,
    backgroundColor: '#fff',
    marginTop: 20,
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  wellnessSummaryCard: {
    backgroundColor: '#fff',
    borderRadius: 20,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  wellnessMoodSection: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  wellnessMoodIconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#F5F7FF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  wellnessMoodEmoji: {
    fontSize: 30,
  },
  wellnessMoodTextContainer: {
    flex: 1,
  },
  wellnessMoodTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  wellnessMoodDescription: {
    fontSize: 14,
    color: '#666',
  },
  wellnessMetricsSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  wellnessMetric: {
    alignItems: 'center',
  },
  wellnessMetricValue: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginTop: 5,
  },
  wellnessMetricLabel: {
    fontSize: 12,
    color: '#666',
  },
  emojiText: {
    fontSize: 24,
  },
  selectedEmoji: {
    fontSize: 28,
  },
});

export default HomeScreen;


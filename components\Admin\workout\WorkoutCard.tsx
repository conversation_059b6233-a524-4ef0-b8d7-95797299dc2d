// src/components/WorkoutCard.tsx
import React, { useState } from "react";
import { View, Text, TouchableOpacity, Image, ActivityIndicator } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { Ionicons } from "@expo/vector-icons";
import styles from "./styles";

const LoadingImage = ({ source, style }: { source: { uri: string }; style: any }) => {
  const [isLoading, setIsLoading] = useState(true);
  return (
    <View style={[style, { justifyContent: "center", alignItems: "center" }]}>
      <Image
        source={source}
        style={[style, { position: isLoading ? "absolute" : "relative" }]}
        onLoadStart={() => setIsLoading(true)}
        onLoadEnd={() => setIsLoading(false)}
      />
      {isLoading && <ActivityIndicator size="small" color="#fff" />}
    </View>
  );
};

const getDifficultyColor = (diff: string) => {
  switch (diff.toLowerCase()) {
    case "beginner":
      return "#92A3FD";
    case "intermediate":
      return "#FFA07A";
    case "advanced":
      return "#FF4B4B";
    default:
      return "#92A3FD";
  }
};

const WorkoutCard = ({ workout, onPress }: { workout: any; onPress: () => void }) => {
  const [pressed, setPressed] = useState(false);
  
  const handlePressIn = () => setPressed(true);
  const handlePressOut = () => setPressed(false);
  
  return (
    <TouchableOpacity 
      style={[
        styles.workoutCard, 
        pressed && { transform: [{ scale: 0.98 }] }
      ]} 
      onPress={onPress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      activeOpacity={0.9}
    >
      <LinearGradient 
        colors={["#92A3FD", "#9DCEFF"]} 
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.cardGradient}
      >
        {workout.icon ? (
          <>
            <LoadingImage source={{ uri: workout.icon }} style={styles.workoutImage} />
            <LinearGradient
              colors={['transparent', 'rgba(0,0,0,0.4)']}
              style={{
                position: 'absolute',
                left: 0,
                right: 0,
                bottom: 0,
                height: '70%',
                borderRadius: 16,
              }}
            />
          </>
        ) : (
          <View style={[styles.workoutImage, { backgroundColor: '#7B90FD' }]}>
            <Ionicons name="fitness" size={48} color="rgba(255,255,255,0.8)" />
          </View>
        )}
        
        <View style={styles.contentContainer}>
          <View style={styles.workoutHeader}>
            <Text style={styles.workoutName} numberOfLines={1} ellipsizeMode="tail">
              {workout.name}
            </Text>
            <View style={styles.workoutBadges}>
              <View 
                style={[
                  styles.difficultyBadge, 
                  { backgroundColor: getDifficultyColor(workout.difficulty) }
                ]}
              >
                <Text style={styles.difficultyText}>{workout.difficulty}</Text>
              </View>
            </View>
          </View>
          
          <Text style={styles.workoutDescription} numberOfLines={2}>
            {workout.description}
          </Text>
          
          <View style={styles.workoutDetails}>
            <View style={styles.detailItem}>
              <Ionicons name="time-outline" size={18} color="#fff" />
              <Text style={styles.detailText}>{workout.duration} Min</Text>
            </View>
            <View style={styles.detailItem}>
              <Ionicons name="fitness-outline" size={18} color="#fff" />
              <Text style={styles.detailText}>{workout.exercise_count} Übungen</Text>
            </View>
            <View style={styles.detailItem}>
              <Ionicons name="flame-outline" size={18} color="#fff" />
              <Text style={styles.detailText}>{workout.calories_burned} kcal</Text>
            </View>
          </View>
        </View>
      </LinearGradient>
    </TouchableOpacity>
  );
};

export default WorkoutCard;

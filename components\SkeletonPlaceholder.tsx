import React from 'react';
import { View, ViewStyle } from 'react-native';

interface SkeletonItemProps {
  width?: number | string;
  height?: number | string;
  borderRadius?: number;
  marginBottom?: number;
  marginTop?: number;
  marginLeft?: number;
  marginRight?: number;
  style?: ViewStyle;
}

interface SkeletonPlaceholderProps {
  backgroundColor?: string;
  highlightColor?: string;
  speed?: number;
  children: React.ReactNode;
}

const SkeletonItem: React.FC<SkeletonItemProps> = ({
  width = '100%',
  height = 15,
  borderRadius = 4,
  marginBottom = 0,
  marginTop = 0,
  marginLeft = 0,
  marginRight = 0,  style = {},
}) => {
  return (
    <View
      style={[
        {
          width,
          height,
          borderRadius,
          marginBottom,
          marginTop,
          marginLeft,
          marginRight,
          overflow: 'hidden',
        },
        style,
      ]}
    >
      <View
        from={{ opacity: 0.5 }}
        animate={{ opacity: 1 }}
        transition={{
          type: 'timing',
          duration: 1000,
          loop: true,
          repeatReverse: true,
        }}
        style={{
          width: '100%',
          height: '100%',
          backgroundColor: '#E1E9EE',
        }}
      />
    </View>
  );
};

const SkeletonPlaceholder: React.FC<SkeletonPlaceholderProps> & {
  Item: typeof SkeletonItem;
} = ({ backgroundColor = '#E1E9EE', highlightColor = '#F2F8FC', speed = 800, children }) => {
  return <>{children}</>;
};

SkeletonPlaceholder.Item = SkeletonItem;

export default SkeletonPlaceholder;

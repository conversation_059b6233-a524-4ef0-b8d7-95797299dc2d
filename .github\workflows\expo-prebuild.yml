name: Expo iOS Prebuild

on:
  workflow_dispatch:

jobs:
  build-ios:
    runs-on: macos-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: "20"
      - name: Install Expo CLI
        run: npm install -g expo-cli
      - name: Install dependencies
        run: npm install
      - name: Run Expo prebuild
        run: expo prebuild
      - name: Zip iOS Folder
        run: zip -r ios-folder.zip ios
      - uses: actions/upload-artifact@v4
        with:
          name: ios-folder
          path: ios-folder.zip

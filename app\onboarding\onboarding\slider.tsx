import React, { useEffect, useRef, useState } from "react";
import { Platform, StyleSheet, View, Animated, PanResponder } from "react-native";
import Wave from "./wave";
import { HEIGHT, MARGIN_WIDTH, MIN_LEDGE, Side, WIDTH } from "@/configs/constants";

const PREV = WIDTH;
const NEXT = 0;
const LEFT_SNAP_POINTS = [MARGIN_WIDTH, PREV];
const RIGHT_SNAP_POINTS = [NEXT, WIDTH - MARGIN_WIDTH];

interface SliderProps {
  index: number;
  setIndex: (value: number) => void;
  children: JSX.Element;
  prev?: JSX.Element;
  next?: JSX.Element;
}

const Slider = ({
  index,
  children: current,
  prev,
  next,
  setIndex,
}: SliderProps) => {
  const hasPrev = !!prev;
  const hasNext = !!next;
  
  // States and animation values
  const [zIndex, setZIndex] = useState(0);
  const [activeSide, setActiveSide] = useState(Side.NONE);
  const [isTransitionLeft, setIsTransitionLeft] = useState(false);
  const [isTransitionRight, setIsTransitionRight] = useState(false);
  
  // Animation values
  const leftX = useRef(new Animated.Value(MIN_LEDGE)).current;
  const leftY = useRef(new Animated.Value(HEIGHT / 2)).current;
  const rightX = useRef(new Animated.Value(MIN_LEDGE)).current;
  const rightY = useRef(new Animated.Value(HEIGHT / 2)).current;

  // Helper function to determine snap point
  const snapPoint = (x: number, velocity: number, points: number[]): number => {
    'worklet';
    const sortedPoints = [...points].sort((a, b) => a - b);
    const point = sortedPoints.find((p) => x <= p) || sortedPoints[sortedPoints.length - 1];
    
    // Apply velocity bias
    const threshold = 0.5;
    if (Math.abs(velocity) < threshold) return point;
    
    const nextPoint = velocity > 0 ? sortedPoints.find(p => p > point) : sortedPoints.reverse().find(p => p < point);
    return nextPoint ?? point;
  };

  // PanResponder setup
  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onMoveShouldSetPanResponder: () => true,
      onPanResponderGrant: (evt, gestureState) => {
        const { x0 } = gestureState;
        if (x0 <= MARGIN_WIDTH && hasPrev) {
          setActiveSide(Side.LEFT);
          setZIndex(100);
        } else if (x0 >= WIDTH - MARGIN_WIDTH && hasNext) {
          setActiveSide(Side.RIGHT);
        } else {
          setActiveSide(Side.NONE);
        }
      },
      onPanResponderMove: (evt, gestureState) => {
        const { moveX, moveY } = gestureState;
        if (activeSide === Side.LEFT) {
          leftX.setValue(Math.max(moveX, MARGIN_WIDTH));
          leftY.setValue(moveY);
        } else if (activeSide === Side.RIGHT) {
          rightX.setValue(Math.max(WIDTH - moveX, MARGIN_WIDTH));
          rightY.setValue(moveY);
        }
      },
      onPanResponderRelease: (evt, gestureState) => {
        const { moveX, vx, vy } = gestureState;
        
        if (activeSide === Side.LEFT) {
          const dest = snapPoint(moveX, vx, LEFT_SNAP_POINTS);
          const shouldTransition = dest === PREV;
          setIsTransitionLeft(shouldTransition);
          
          Animated.spring(leftX, {
            toValue: dest,
            velocity: vx,
            overshootClamping: shouldTransition,
            useNativeDriver: false
          }).start(() => {
            if (shouldTransition) {
              setIndex(index - 1);
            } else {
              setZIndex(0);
              setActiveSide(Side.NONE);
            }
          });
          
          Animated.spring(leftY, {
            toValue: HEIGHT / 2,
            velocity: vy,
            useNativeDriver: false
          }).start();
          
        } else if (activeSide === Side.RIGHT) {
          const dest = snapPoint(moveX, vx, RIGHT_SNAP_POINTS);
          const shouldTransition = dest === NEXT;
          setIsTransitionRight(shouldTransition);
          
          Animated.spring(rightX, {
            toValue: WIDTH - dest,
            velocity: vx,
            overshootClamping: shouldTransition,
            useNativeDriver: false
          }).start(() => {
            if (shouldTransition) {
              setIndex(index + 1);
            } else {
              setActiveSide(Side.NONE);
            }
          });
          
          Animated.spring(rightY, {
            toValue: HEIGHT / 2,
            velocity: vy,
            useNativeDriver: false
          }).start();
        }
      },
    })
  ).current;

  const leftStyle = {
    zIndex,
  };

  useEffect(() => {
    // Initialize rightX
    Animated.spring(rightX, {
      toValue: Platform.OS === "ios" ? WIDTH * 0.167 : WIDTH * 0.185,
      useNativeDriver: false
    }).start();
  }, []);

  return (
    <View style={StyleSheet.absoluteFill} {...panResponder.panHandlers}>
      {current}
      {prev && (
        <Animated.View style={[StyleSheet.absoluteFill, leftStyle]}>
          <Wave
            side={Side.LEFT}
            position={{ x: leftX, y: leftY }}
            isTransitioning={isTransitionLeft}
          >
            {prev}
          </Wave>
        </Animated.View>
      )}
      {next && (
        <View style={StyleSheet.absoluteFill}>
          <Wave
            side={Side.RIGHT}
            position={{ x: rightX, y: rightY }}
            isTransitioning={isTransitionRight}
          >
            {next}
          </Wave>
        </View>
      )}
    </View>
  );
};

export default Slider;

export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      assigned_workouts: {
        Row: {
          assigned_date: string
          completed: boolean | null
          created_at: string
          id: string
          user_id: string | null
          workout_id: number | null
        }
        Insert: {
          assigned_date: string
          completed?: boolean | null
          created_at?: string
          id?: string
          user_id?: string | null
          workout_id?: number | null
        }
        Update: {
          assigned_date?: string
          completed?: boolean | null
          created_at?: string
          id?: string
          user_id?: string | null
          workout_id?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "assigned_workouts_workout_id_fkey"
            columns: ["workout_id"]
            isOneToOne: false
            referencedRelation: "workouts"
            referencedColumns: ["id"]
          },
        ]
      }
      equipment: {
        Row: {
          id: number
          image_url: string
          name: string
        }
        Insert: {
          id?: number
          image_url: string
          name: string
        }
        Update: {
          id?: number
          image_url?: string
          name?: string
        }
        Relationships: []
      }
      exercises: {
        Row: {
          duration: string | null
          id: number
          image_url: string
          name: string
          reps: string | null
          set_number: number
          video_url: string | null
          workout_id: number | null
        }
        Insert: {
          duration?: string | null
          id?: number
          image_url: string
          name: string
          reps?: string | null
          set_number: number
          video_url?: string | null
          workout_id?: number | null
        }
        Update: {
          duration?: string | null
          id?: number
          image_url?: string
          name?: string
          reps?: string | null
          set_number?: number
          video_url?: string | null
          workout_id?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "exercises_workout_id_fkey"
            columns: ["workout_id"]
            isOneToOne: false
            referencedRelation: "workouts"
            referencedColumns: ["id"]
          },
        ]
      }
      notifications: {
        Row: {
          created_at: string
          id: string
          message: string
          read: boolean | null
          type: string
          user_id: string | null
        }
        Insert: {
          created_at?: string
          id?: string
          message: string
          read?: boolean | null
          type: string
          user_id?: string | null
        }
        Update: {
          created_at?: string
          id?: string
          message?: string
          read?: boolean | null
          type?: string
          user_id?: string | null
        }
        Relationships: []
      }
      period_tracking: {
        Row: {
          created_at: string
          cycle_length: number | null
          id: string
          notes: string | null
          period_end_date: string | null
          period_length: number | null
          period_start_date: string
          symptoms: string[] | null
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          cycle_length?: number | null
          id?: string
          notes?: string | null
          period_end_date?: string | null
          period_length?: number | null
          period_start_date: string
          symptoms?: string[] | null
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          cycle_length?: number | null
          id?: string
          notes?: string | null
          period_end_date?: string | null
          period_length?: number | null
          period_start_date?: string
          symptoms?: string[] | null
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      profiles: {
        Row: {
          avatar_url: string | null
          created_at: string
          dietary_preferences: string[] | null
          email: string | null
          fitness_goal: string | null
          fitness_level: string | null
          full_name: string | null
          health_conditions: string[] | null
          id: string
          is_admin: boolean | null
          preferred_workout_time: string | null
          subscription_type: string | null
          target_weight: number | null
          updated_at: string
          weekly_workout_days: number | null
        }
        Insert: {
          avatar_url?: string | null
          created_at?: string
          dietary_preferences?: string[] | null
          email?: string | null
          fitness_goal?: string | null
          fitness_level?: string | null
          full_name?: string | null
          health_conditions?: string[] | null
          id: string
          is_admin?: boolean | null
          preferred_workout_time?: string | null
          subscription_type?: string | null
          target_weight?: number | null
          updated_at?: string
          weekly_workout_days?: number | null
        }
        Update: {
          avatar_url?: string | null
          created_at?: string
          dietary_preferences?: string[] | null
          email?: string | null
          fitness_goal?: string | null
          fitness_level?: string | null
          full_name?: string | null
          health_conditions?: string[] | null
          id?: string
          is_admin?: boolean | null
          preferred_workout_time?: string | null
          subscription_type?: string | null
          target_weight?: number | null
          updated_at?: string
          weekly_workout_days?: number | null
        }
        Relationships: []
      }
      user_activities: {
        Row: {
          activity_date: string
          calories: number
          created_at: string
          exercise_duration: number
          exercise_type: string | null
          foot_steps: number
          heart_rate: number
          id: string
          period_end: string | null
          period_notes: string | null
          period_start: string | null
          sleep_hours: number
          updated_at: string
          user_id: string
          water_intake_ml: number
        }
        Insert: {
          activity_date?: string
          calories?: number
          created_at?: string
          exercise_duration?: number
          exercise_type?: string | null
          foot_steps?: number
          heart_rate?: number
          id?: string
          period_end?: string | null
          period_notes?: string | null
          period_start?: string | null
          sleep_hours?: number
          updated_at?: string
          user_id: string
          water_intake_ml?: number
        }
        Update: {
          activity_date?: string
          calories?: number
          created_at?: string
          exercise_duration?: number
          exercise_type?: string | null
          foot_steps?: number
          heart_rate?: number
          id?: string
          period_end?: string | null
          period_notes?: string | null
          period_start?: string | null
          sleep_hours?: number
          updated_at?: string
          user_id?: string
          water_intake_ml?: number
        }
        Relationships: [
          {
            foreignKeyName: "user_activities_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      user_photos: {
        Row: {
          created_at: string | null
          id: number
          photo_url: string
          user_id: string
        }
        Insert: {
          created_at?: string | null
          id?: number
          photo_url: string
          user_id: string
        }
        Update: {
          created_at?: string | null
          id?: number
          photo_url?: string
          user_id?: string
        }
        Relationships: []
      }
      user_roles: {
        Row: {
          created_at: string
          id: string
          role: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          role: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          role?: string
          user_id?: string
        }
        Relationships: []
      }
      users: {
        Row: {
          age: number | null
          birth_date: string | null
          created_at: string | null
          daily_water_goal_ml: number | null
          email: string
          full_name: string | null
          gender: string | null
          goal: string | null
          height: number | null
          id: string
          is_active: boolean | null
          language: string | null
          last_login: string | null
          password_hash: string
          profile_picture_url: string | null
          subscription_type: string | null
          updated_at: string | null
          username: string | null
          weight: number | null
        }
        Insert: {
          age?: number | null
          birth_date?: string | null
          created_at?: string | null
          daily_water_goal_ml?: number | null
          email: string
          full_name?: string | null
          gender?: string | null
          goal?: string | null
          height?: number | null
          id?: string
          is_active?: boolean | null
          language?: string | null
          last_login?: string | null
          password_hash: string
          profile_picture_url?: string | null
          subscription_type?: string | null
          updated_at?: string | null
          username?: string | null
          weight?: number | null
        }
        Update: {
          age?: number | null
          birth_date?: string | null
          created_at?: string | null
          daily_water_goal_ml?: number | null
          email?: string
          full_name?: string | null
          gender?: string | null
          goal?: string | null
          height?: number | null
          id?: string
          is_active?: boolean | null
          language?: string | null
          last_login?: string | null
          password_hash?: string
          profile_picture_url?: string | null
          subscription_type?: string | null
          updated_at?: string | null
          username?: string | null
          weight?: number | null
        }
        Relationships: []
      }
      weight_entries: {
        Row: {
          created_at: string
          date: string
          id: string
          note: string | null
          user_id: string
          weight: number
        }
        Insert: {
          created_at?: string
          date?: string
          id?: string
          note?: string | null
          user_id: string
          weight: number
        }
        Update: {
          created_at?: string
          date?: string
          id?: string
          note?: string | null
          user_id?: string
          weight?: number
        }
        Relationships: []
      }
      weight_tracking: {
        Row: {
          created_at: string
          date: string
          id: string
          notes: string | null
          updated_at: string
          user_id: string
          weight: number
        }
        Insert: {
          created_at?: string
          date?: string
          id?: string
          notes?: string | null
          updated_at?: string
          user_id: string
          weight: number
        }
        Update: {
          created_at?: string
          date?: string
          id?: string
          notes?: string | null
          updated_at?: string
          user_id?: string
          weight?: number
        }
        Relationships: [
          {
            foreignKeyName: "weight_tracking_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      wellness_checks: {
        Row: {
          check_date: string
          created_at: string
          energy_rating: number
          id: string
          mood_rating: number
          sleep_rating: number
          user_id: string
        }
        Insert: {
          check_date: string
          created_at?: string
          energy_rating: number
          id?: string
          mood_rating: number
          sleep_rating: number
          user_id: string
        }
        Update: {
          check_date?: string
          created_at?: string
          energy_rating?: number
          id?: string
          mood_rating?: number
          sleep_rating?: number
          user_id?: string
        }
        Relationships: []
      }
      workout_equipment: {
        Row: {
          equipment_id: number
          workout_id: number
        }
        Insert: {
          equipment_id: number
          workout_id: number
        }
        Update: {
          equipment_id?: number
          workout_id?: number
        }
        Relationships: [
          {
            foreignKeyName: "workout_equipment_equipment_id_fkey"
            columns: ["equipment_id"]
            isOneToOne: false
            referencedRelation: "equipment"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "workout_equipment_workout_id_fkey"
            columns: ["workout_id"]
            isOneToOne: false
            referencedRelation: "workouts"
            referencedColumns: ["id"]
          },
        ]
      }
      workout_history: {
        Row: {
          created_at: string | null
          end_time: string | null
          id: string
          start_time: string
          status: string
          updated_at: string | null
          user_id: string
          workout_id: number
        }
        Insert: {
          created_at?: string | null
          end_time?: string | null
          id?: string
          start_time: string
          status: string
          updated_at?: string | null
          user_id: string
          workout_id: number
        }
        Update: {
          created_at?: string | null
          end_time?: string | null
          id?: string
          start_time?: string
          status?: string
          updated_at?: string | null
          user_id?: string
          workout_id?: number
        }
        Relationships: [
          {
            foreignKeyName: "workout_history_workout_id_fkey"
            columns: ["workout_id"]
            isOneToOne: false
            referencedRelation: "workouts"
            referencedColumns: ["id"]
          },
        ]
      }
      workouts: {
        Row: {
          calories_burned: number
          description: string
          difficulty: string
          duration: number
          exercises_count: number
          icon: string | null
          id: number
          name: string
          schedule_time: string
          type: string
        }
        Insert: {
          calories_burned: number
          description: string
          difficulty: string
          duration: number
          exercises_count: number
          icon?: string | null
          id?: number
          name: string
          schedule_time: string
          type: string
        }
        Update: {
          calories_burned?: number
          description?: string
          difficulty?: string
          duration?: number
          exercises_count?: number
          icon?: string | null
          id?: number
          name?: string
          schedule_time?: string
          type?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      add_icon_column_if_not_exists: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type PublicSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  PublicTableNameOrOptions extends
    | keyof (PublicSchema["Tables"] & PublicSchema["Views"])
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
        Database[PublicTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
      Database[PublicTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : PublicTableNameOrOptions extends keyof (PublicSchema["Tables"] &
        PublicSchema["Views"])
    ? (PublicSchema["Tables"] &
        PublicSchema["Views"])[PublicTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
    ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
    ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  PublicEnumNameOrOptions extends
    | keyof PublicSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends PublicEnumNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = PublicEnumNameOrOptions extends { schema: keyof Database }
  ? Database[PublicEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : PublicEnumNameOrOptions extends keyof PublicSchema["Enums"]
    ? PublicSchema["Enums"][PublicEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof PublicSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof PublicSchema["CompositeTypes"]
    ? PublicSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

import React, { useState, useEffect, useRef } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  ScrollView, 
  StatusBar,
  Image,
  Dimensions,
  TextInput,
  Animated,
  Easing
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { BlurView } from 'expo-blur';

const { width } = Dimensions.get('window');

// Sample meal data for demo purposes
const SAMPLE_MEALS = [
  {
    id: '1',
    type: 'breakfast',
    title: 'Greek Yogurt Bowl',
    calories: 320,
    protein: 24,
    carbs: 42,
    fat: 8,
    image: 'https://images.unsplash.com/photo-1511690078903-71de64ac9c54?q=80&w=400&auto=format'
  },
  {
    id: '2',
    type: 'lunch',
    title: 'Quinoa Salad with Avocado',
    calories: 450,
    protein: 18,
    carbs: 56,
    fat: 22,
    image: 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?q=80&w=400&auto=format'
  },
  {
    id: '3',
    type: 'dinner',
    title: 'Grilled Salmon & Vegetables',
    calories: 520,
    protein: 42,
    carbs: 18,
    fat: 28,
    image: 'https://images.unsplash.com/photo-1467003909585-2f8a72700288?q=80&w=400&auto=format'
  },
  {
    id: '4',
    type: 'snack',
    title: 'Protein Smoothie',
    calories: 220,
    protein: 20,
    carbs: 26,
    fat: 4,
    image: 'https://images.unsplash.com/photo-1502741224143-90386d7f8c82?q=80&w=400&auto=format'
  },
];

// Diet preference options
const DIET_OPTIONS = [
  { id: 'balanced', label: 'Balanced' },
  { id: 'low-carb', label: 'Low Carb' },
  { id: 'high-protein', label: 'High Protein' },
  { id: 'vegetarian', label: 'Vegetarian' },
  { id: 'vegan', label: 'Vegan' }
];

export default function MealPlanner() {
  const insets = useSafeAreaInsets();
  const router = useRouter();
  
  const [isGenerating, setIsGenerating] = useState(false);
  const [selectedDiet, setSelectedDiet] = useState('balanced');
  const [meals, setMeals] = useState<typeof SAMPLE_MEALS>([]);
  const [calorieTarget, setCalorieTarget] = useState('2000');
  const [expandedMealId, setExpandedMealId] = useState<string | null>(null);

  // Animation values
  const headerOpacity = useRef(new Animated.Value(0)).current;
  const headerTranslateY = useRef(new Animated.Value(-10)).current;
  const calorieTargetOpacity = useRef(new Animated.Value(0)).current;
  const calorieTargetTranslateY = useRef(new Animated.Value(15)).current;
  const dietPreferencesOpacity = useRef(new Animated.Value(0)).current;
  const dietPreferencesTranslateY = useRef(new Animated.Value(15)).current;
  const mealsContainerOpacity = useRef(new Animated.Value(0)).current;
  const mealsContainerTranslateY = useRef(new Animated.Value(15)).current;
  const loadingScale = useRef(new Animated.Value(0.8)).current;
  const loadingOpacity = useRef(new Animated.Value(0.3)).current;
  const loadingTextOpacity = useRef(new Animated.Value(0.6)).current;
  const loadingRotation = useRef(new Animated.Value(0)).current;

  // Calculate the tab bar height to ensure proper spacing
  const TAB_BAR_HEIGHT = 65 + 10 + insets.bottom;

  // Animate header when component mounts
  useEffect(() => {
    Animated.timing(headerOpacity, {
      toValue: 1,
      duration: 400,
      useNativeDriver: true,
    }).start();
    
    Animated.timing(headerTranslateY, {
      toValue: 0,
      duration: 400,
      useNativeDriver: true,
    }).start();

    // Animate calorie target section
    Animated.timing(calorieTargetOpacity, {
      toValue: 1,
      duration: 400,
      delay: 100,
      useNativeDriver: true,
    }).start();
    
    Animated.timing(calorieTargetTranslateY, {
      toValue: 0,
      duration: 400,
      delay: 100,
      useNativeDriver: true,
    }).start();

    // Animate diet preferences section
    Animated.timing(dietPreferencesOpacity, {
      toValue: 1,
      duration: 400,
      delay: 200,
      useNativeDriver: true,
    }).start();
    
    Animated.timing(dietPreferencesTranslateY, {
      toValue: 0,
      duration: 400,
      delay: 200,
      useNativeDriver: true,
    }).start();

    // Animate meals container section
    Animated.timing(mealsContainerOpacity, {
      toValue: 1,
      duration: 400,
      delay: 300,
      useNativeDriver: true,
    }).start();
    
    Animated.timing(mealsContainerTranslateY, {
      toValue: 0,
      duration: 400,
      delay: 300,
      useNativeDriver: true,
    }).start();
  }, []);

  // Animate loading icon when generating meals
  useEffect(() => {
    if (isGenerating) {
      // Create pulsing effect for loading icon
      Animated.loop(
        Animated.sequence([
          Animated.parallel([
            Animated.timing(loadingScale, {
              toValue: 1.1,
              duration: 700,
              easing: Easing.out(Easing.ease),
              useNativeDriver: true,
            }),
            Animated.timing(loadingOpacity, {
              toValue: 1,
              duration: 700,
              easing: Easing.out(Easing.ease),
              useNativeDriver: true,
            }),
          ]),
          Animated.parallel([
            Animated.timing(loadingScale, {
              toValue: 0.9,
              duration: 700,
              easing: Easing.in(Easing.ease),
              useNativeDriver: true,
            }),
            Animated.timing(loadingOpacity, {
              toValue: 0.8,
              duration: 700,
              easing: Easing.in(Easing.ease),
              useNativeDriver: true,
            }),
          ]),
          Animated.parallel([
            Animated.timing(loadingScale, {
              toValue: 1,
              duration: 600,
              easing: Easing.out(Easing.ease),
              useNativeDriver: true,
            }),
            Animated.timing(loadingOpacity, {
              toValue: 1,
              duration: 600,
              easing: Easing.out(Easing.ease),
              useNativeDriver: true,
            }),
          ]),
        ])
      ).start();

      // Create pulsing effect for loading text
      Animated.loop(
        Animated.sequence([
          Animated.timing(loadingTextOpacity, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(loadingTextOpacity, {
            toValue: 0.6,
            duration: 1000,
            useNativeDriver: true,
          }),
        ])
      ).start();

      // Create rotation effect for refresh icon
      Animated.loop(
        Animated.timing(loadingRotation, {
          toValue: 1,
          duration: 1500,
          useNativeDriver: true,
          easing: Easing.linear,
        })
      ).start();
    }
  }, [isGenerating]);

  // Create interpolated rotation value for refresh icon
  const spin = loadingRotation.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  // Simulate loading meals
  useEffect(() => {
    // Show empty state initially
    setMeals([]);
  }, []);

  const handleGenerateMeals = () => {
    setIsGenerating(true);
    
    // Simulate API call delay
    setTimeout(() => {
      setMeals(SAMPLE_MEALS);
      setIsGenerating(false);
    }, 2500);
  };

  const toggleMealExpand = (mealId: string) => {
    setExpandedMealId(expandedMealId === mealId ? null : mealId);
  };

  // Calorie input validation
  const handleCalorieChange = (text: string) => {
    // Only allow numbers
    const filtered = text.replace(/[^0-9]/g, '');
    setCalorieTarget(filtered);
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" />
      
      {/* Header with back button */}
      <LinearGradient
        colors={['#FFFFFF', '#F8F9FF']}
        style={[
          styles.header,
          { paddingTop: Math.max(insets.top, 40) }
        ]}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <Ionicons name="arrow-back" size={24} color="#333" />
          </TouchableOpacity>
          
          <Animated.View style={{ 
            opacity: headerOpacity, 
            transform: [{ translateY: headerTranslateY }]
          }}>
            <Text style={styles.headerTitle}>AI Meal Planner</Text>
          </Animated.View>
          
          <View style={{ width: 40 }} />
        </View>
      </LinearGradient>

      <ScrollView
        style={styles.content}
        contentContainerStyle={[
          styles.scrollContent,
          { paddingBottom: TAB_BAR_HEIGHT + 100 } // Add padding for tab bar and generate button
        ]}
        showsVerticalScrollIndicator={false}
      >
        {/* Daily calorie target */}
        <Animated.View
          style={[
            styles.section,
            {
              opacity: calorieTargetOpacity,
              transform: [{ translateY: calorieTargetTranslateY }]
            }
          ]}
        >
          <Text style={styles.sectionTitle}>Daily Calorie Target</Text>
          <View style={styles.calorieInputContainer}>
            <TextInput
              style={styles.calorieInput}
              value={calorieTarget}
              onChangeText={handleCalorieChange}
              keyboardType="numeric"
              maxLength={4}
            />
            <Text style={styles.calorieUnit}>kcal</Text>
          </View>
        </Animated.View>

        {/* Diet preferences */}
        <Animated.View
          style={[
            styles.section,
            {
              opacity: dietPreferencesOpacity,
              transform: [{ translateY: dietPreferencesTranslateY }]
            }
          ]}
        >
          <Text style={styles.sectionTitle}>Diet Preference</Text>
          <View style={styles.dietOptionsContainer}>
            {DIET_OPTIONS.map((option, index) => (
              <TouchableOpacity 
                key={option.id}
                style={[
                  styles.dietOption,
                  selectedDiet === option.id && styles.selectedDietOption,
                ]}
                onPress={() => setSelectedDiet(option.id)}
              >
                <LinearGradient
                  colors={
                    selectedDiet === option.id 
                      ? ['rgba(255,107,156,0.15)', 'rgba(255,107,156,0.05)']
                      : ['rgba(255,255,255,0.8)', 'rgba(248,249,255,0.8)']
                  }
                  style={styles.dietGradient}
                >
                  <Text style={[
                    styles.dietOptionText,
                    selectedDiet === option.id && styles.selectedDietText
                  ]}>
                    {option.label}
                  </Text>
                </LinearGradient>
              </TouchableOpacity>
            ))}
          </View>
        </Animated.View>

        {/* Generated meals section */}
        <Animated.View
          style={[
            styles.mealsContainer,
            {
              opacity: mealsContainerOpacity,
              transform: [{ translateY: mealsContainerTranslateY }]
            }
          ]}
        >
          {meals.length > 0 ? (
            <>
              <Text style={styles.mealsTitle}>Your AI-Generated Meal Plan</Text>
              
              {meals.map((meal, index) => {
                // Create animation values for each meal
                const mealItemOpacity = useRef(new Animated.Value(0)).current;
                const mealItemTranslateY = useRef(new Animated.Value(20)).current;
                const expandRotation = useRef(new Animated.Value(0)).current;
                const expandHeight = useRef(new Animated.Value(0)).current;
                const expandOpacity = useRef(new Animated.Value(0)).current;
                
                // Set up interpolated rotation value
                const arrowRotation = expandRotation.interpolate({
                  inputRange: [0, 1],
                  outputRange: ['0deg', '180deg'],
                });

                // Animate meal item when it appears
                useEffect(() => {
                  Animated.timing(mealItemOpacity, {
                    toValue: 1,
                    duration: 400,
                    delay: 400 + (index * 150),
                    useNativeDriver: true,
                  }).start();
                  
                  Animated.spring(mealItemTranslateY, {
                    toValue: 0,
                    tension: 50,
                    friction: 7,
                    delay: 400 + (index * 150),
                    useNativeDriver: true,
                  }).start();
                }, []);
                
                // Animate expand/collapse
                useEffect(() => {
                  if (expandedMealId === meal.id) {
                    Animated.timing(expandRotation, {
                      toValue: 1,
                      duration: 300,
                      useNativeDriver: true,
                    }).start();
                    
                    Animated.timing(expandHeight, {
                      toValue: 100,
                      duration: 300,
                      useNativeDriver: false,
                    }).start();
                    
                    Animated.timing(expandOpacity, {
                      toValue: 1,
                      duration: 300,
                      useNativeDriver: true,
                    }).start();
                  } else {
                    Animated.timing(expandRotation, {
                      toValue: 0,
                      duration: 300,
                      useNativeDriver: true,
                    }).start();
                    
                    Animated.timing(expandHeight, {
                      toValue: 0,
                      duration: 300,
                      useNativeDriver: false,
                    }).start();
                    
                    Animated.timing(expandOpacity, {
                      toValue: 0,
                      duration: 300,
                      useNativeDriver: true,
                    }).start();
                  }
                }, [expandedMealId]);
                
                return (
                  <Animated.View
                    key={meal.id}
                    style={[
                      styles.mealCard,
                      {
                        opacity: mealItemOpacity,
                        transform: [{ translateY: mealItemTranslateY }]
                      }
                    ]}
                  >
                    <TouchableOpacity
                      activeOpacity={0.9}
                      onPress={() => toggleMealExpand(meal.id)}
                      style={styles.mealCardInner}
                    >
                      <Image 
                        source={{ uri: meal.image }} 
                        style={styles.mealImage} 
                        resizeMode="cover"
                      />
                      
                      <LinearGradient
                        colors={['transparent', 'rgba(0,0,0,0.7)']}
                        style={styles.mealGradient}
                      />
                      
                      <View style={styles.mealTypeContainer}>
                        <BlurView intensity={70} tint="light" style={styles.mealTypeInner}>
                          <Text style={styles.mealType}>
                            {meal.type.charAt(0).toUpperCase() + meal.type.slice(1)}
                          </Text>
                        </BlurView>
                      </View>

                      <View style={styles.mealInfo}>
                        <Text style={styles.mealTitle}>{meal.title}</Text>
                        <Text style={styles.mealCalories}>{meal.calories} kcal</Text>
                      </View>

                      <Animated.View
                        style={[
                          styles.mealDetails,
                          {
                            height: expandHeight,
                            opacity: expandOpacity,
                          }
                        ]}
                      >
                        <BlurView intensity={70} tint="dark" style={styles.macrosContainer}>
                          <View style={styles.macroItem}>
                            <Text style={styles.macroValue}>{meal.protein}g</Text>
                            <Text style={styles.macroLabel}>Protein</Text>
                          </View>
                          <View style={styles.macroItem}>
                            <Text style={styles.macroValue}>{meal.carbs}g</Text>
                            <Text style={styles.macroLabel}>Carbs</Text>
                          </View>
                          <View style={styles.macroItem}>
                            <Text style={styles.macroValue}>{meal.fat}g</Text>
                            <Text style={styles.macroLabel}>Fat</Text>
                          </View>
                        </BlurView>
                      </Animated.View>

                      <Animated.View
                        style={[
                          styles.expandIcon,
                          {
                            transform: [{ rotate: arrowRotation }]
                          }
                        ]}
                      >
                        <BlurView intensity={70} tint="dark" style={styles.expandIconBlur}>
                          <Ionicons name="chevron-down" size={16} color="#fff" />
                        </BlurView>
                      </Animated.View>
                    </TouchableOpacity>
                  </Animated.View>
                );
              })}
            </>
          ) : (
            <View style={styles.emptyState}>
              {isGenerating ? (
                <View style={styles.loadingContainer}>
                  <Animated.View
                    style={[
                      styles.loadingIcon,
                      {
                        transform: [{ scale: loadingScale }],
                        opacity: loadingOpacity,
                      }
                    ]}
                  >
                    <Ionicons name="nutrition" size={50} color="#FF6B9C" />
                  </Animated.View>
                  <Animated.Text
                    style={[
                      styles.loadingText,
                      { opacity: loadingTextOpacity }
                    ]}
                  >
                    Creating your personalized meal plan...
                  </Animated.Text>
                </View>
              ) : (
                <>
                  <Ionicons name="restaurant-outline" size={60} color="#CCD0DB" />
                  <Text style={styles.emptyTitle}>No Meals Generated Yet</Text>
                  <Text style={styles.emptyDesc}>
                    Select your preferences and tap "Generate" to create your AI-powered meal plan.
                  </Text>
                </>
              )}
            </View>
          )}
        </Animated.View>
      </ScrollView>

      {/* Generate button - positioned above tab bar */}
      <View style={[
        styles.generateButtonContainer,
        {
          paddingBottom: Math.max(insets.bottom, 20),
          bottom: TAB_BAR_HEIGHT - insets.bottom // Position above tab bar
        }
      ]}>
        <TouchableOpacity
          style={styles.generateButton}
          onPress={handleGenerateMeals}
          disabled={isGenerating}
          activeOpacity={0.8}
        >
          <LinearGradient
            colors={['#FF6B9C', '#FF4B8C']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={styles.generateGradient}
          >
            <View style={styles.generateContent}>
              {isGenerating ? (
                <Animated.View
                  style={[
                    styles.loadingIconSmall,
                    { transform: [{ rotate: spin }] }
                  ]}
                >
                  <Ionicons name="refresh" size={22} color="#fff" />
                </Animated.View>
              ) : (
                <Ionicons name="restaurant" size={22} color="#fff" style={styles.generateIcon} />
              )}
              <Text style={styles.generateText}>
                {isGenerating ? 'Generating...' : 'Generate Meal Plan'}
              </Text>
            </View>
          </LinearGradient>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FF',
  },
  header: {
    paddingBottom: 15,
    borderBottomRightRadius: 20,
    borderBottomLeftRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 10,
    elevation: 5,
    zIndex: 10,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(0,0,0,0.03)',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#333',
    textAlign: 'center',
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    padding: 20,
    paddingTop: 25,
  },
  section: {
    marginBottom: 25,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 15,
  },
  calorieInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: 16,
    paddingHorizontal: 16,
    paddingVertical: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 5,
    elevation: 2,
  },
  calorieInput: {
    flex: 1,
    fontSize: 20,
    fontWeight: '600',
    color: '#333',
    padding: 10,
  },
  calorieUnit: {
    fontSize: 16,
    fontWeight: '500',
    color: '#999',
    marginLeft: 8,
  },
  dietOptionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -5,
  },
  dietOption: {
    marginHorizontal: 5,
    marginBottom: 10,
    borderRadius: 16,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.05)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 1,
  },
  dietGradient: {
    paddingVertical: 10,
    paddingHorizontal: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  selectedDietOption: {
    borderColor: 'rgba(255,107,156,0.2)',
  },
  dietOptionText: {
    fontSize: 15,
    fontWeight: '500',
    color: '#666',
  },
  selectedDietText: {
    color: '#FF558D',
    fontWeight: '600',
  },
  mealsContainer: {
    marginTop: 10,
  },
  mealsTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#333',
    marginBottom: 20,
  },
  mealCard: {
    marginBottom: 20,
    borderRadius: 20,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 5 },
    shadowOpacity: 0.1,
    shadowRadius: 10,
    elevation: 5,
    backgroundColor: 'white',
  },
  mealCardInner: {
    position: 'relative',
  },
  mealImage: {
    width: '100%',
    height: 180,
    backgroundColor: '#F0F0F0',
  },
  mealGradient: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    height: '50%',
  },
  mealTypeContainer: {
    position: 'absolute',
    top: 15,
    left: 15,
    borderRadius: 12,
    overflow: 'hidden',
  },
  mealTypeInner: {
    paddingVertical: 6,
    paddingHorizontal: 12,
  },
  mealType: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FF558D',
  },
  mealInfo: {
    position: 'absolute',
    bottom: 15,
    left: 15,
    right: 15,
  },
  mealTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: 'white',
    marginBottom: 4,
    textShadowColor: 'rgba(0,0,0,0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 3,
  },
  mealCalories: {
    fontSize: 15,
    fontWeight: '600',
    color: 'rgba(255,255,255,0.9)',
    textShadowColor: 'rgba(0,0,0,0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  mealDetails: {
    overflow: 'hidden',
  },
  macrosContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 20,
    paddingHorizontal: 10,
  },
  macroItem: {
    alignItems: 'center',
  },
  macroValue: {
    fontSize: 18,
    fontWeight: '700',
    color: 'white',
    marginBottom: 2,
  },
  macroLabel: {
    fontSize: 12,
    fontWeight: '500',
    color: 'rgba(255,255,255,0.8)',
  },
  expandIcon: {
    position: 'absolute',
    top: 15,
    right: 15,
    borderRadius: 12,
    overflow: 'hidden',
  },
  expandIconBlur: {
    padding: 6,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#666',
    marginTop: 20,
    marginBottom: 10,
  },
  emptyDesc: {
    fontSize: 14,
    fontWeight: '400',
    color: '#999',
    textAlign: 'center',
    maxWidth: '80%',
    lineHeight: 20,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  loadingIcon: {
    marginBottom: 20,
  },
  loadingIconSmall: {
    marginRight: 10,
  },
  loadingText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#FF6B9C',
  },
  generateButtonContainer: {
    position: 'absolute',
    left: 0,
    right: 0,
    paddingHorizontal: 20,
    paddingTop: 10,
    backgroundColor: '#F8F9FF',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.06,
    shadowRadius: 8,
    elevation: 10,
    zIndex: 10,
  },
  generateButton: {
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#FF6B9C',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 10,
    elevation: 8,
  },
  generateGradient: {
    paddingVertical: 18,
  },
  generateContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  generateIcon: {
    marginRight: 10,
  },
  generateText: {
    fontSize: 18,
    fontWeight: '600',
    color: 'white',
  },
});

// src/components/QuickAddModal.tsx
import React, { useState, useEffect } from "react";
import {
  Modal,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ActivityIndicator,
  FlatList,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { supabase } from "../../../lib/supabase";
import styles from "./styles";

const QuickAddModal = ({
  visible,
  onClose,
  onSelectExercise,
}: {
  visible: boolean;
  onClose: () => void;
  onSelectExercise?: (exercise: any) => void;
}) => {
  const [exerciseSearchQuery, setExerciseSearchQuery] = useState("");
  const [availableExercises, setAvailableExercises] = useState<any[]>([]);
  const [exerciseSearchLoading, setExerciseSearchLoading] = useState(false);

  useEffect(() => {
    if (visible) {
      fetchInitialExercises();
    }
  }, [visible]);

  const fetchInitialExercises = async () => {
    try {
      setExerciseSearchLoading(true);
      const { data, error } = await supabase
        .from("exercises")
        .select("*")
        .order("name")
        .limit(10);
      if (error) throw error;
      const uniqueExercises = Array.from(new Map(data.map((item: any) => [item.name, item])).values());
      setAvailableExercises(uniqueExercises);
    } catch (error) {
      console.error("Error fetching initial exercises:", error);
      Alert.alert("Error", "Übungen konnten nicht geladen werden.");
    } finally {
      setExerciseSearchLoading(false);
    }
  };

  const searchExercises = async (query: string) => {
    if (!query.trim()) {
      setAvailableExercises([]);
      return;
    }
    try {
      setExerciseSearchLoading(true);
      const { data: allExercises, error } = await supabase.from("exercises").select("*").limit(50);
      if (error) throw error;
      const filteredExercises = allExercises.filter((exercise: any) =>
        exercise.name && exercise.name.toLowerCase().includes(query.toLowerCase())
      );
      const uniqueExercises = Array.from(new Map(filteredExercises.map((item: any) => [item.name, item])).values());
      setAvailableExercises(uniqueExercises);
    } catch (error) {
      console.error("Error searching exercises:", error);
      Alert.alert("Error", "Suche nach Übungen fehlgeschlagen.");
      setAvailableExercises([]);
    } finally {
      setExerciseSearchLoading(false);
    }
  };

  useEffect(() => {
    const debounceTimeout = setTimeout(() => {
      if (exerciseSearchQuery) {
        searchExercises(exerciseSearchQuery);
      }
    }, 300);
    return () => clearTimeout(debounceTimeout);
  }, [exerciseSearchQuery]);

  const handleSelect = (exercise: any) => {
    onSelectExercise && onSelectExercise(exercise);
    onClose();
    setExerciseSearchQuery("");
    setAvailableExercises([]);
  };

  return (
    <Modal animationType="slide" transparent visible={visible} onRequestClose={onClose}>
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.centeredView}
        keyboardVerticalOffset={Platform.OS === "ios" ? 40 : 0}
      >
        <View style={styles.quickAddModalView}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Übung aus Datenbank hinzufügen</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Ionicons name="close" size={24} color="#333" />
            </TouchableOpacity>
          </View>
          <View style={styles.searchContainer}>
            <Ionicons name="search" size={20} color="#92A3FD" style={styles.searchIcon} />
            <TextInput
              style={styles.searchInput}
              placeholder="Übung suchen..."
              value={exerciseSearchQuery}
              onChangeText={setExerciseSearchQuery}
              placeholderTextColor="#A0A0A0"
              autoCapitalize="none"
              autoCorrect={false}
            />
            {exerciseSearchQuery.length > 0 && (
              <TouchableOpacity onPress={() => setExerciseSearchQuery("")} style={styles.clearButton}>
                <Ionicons name="close-circle" size={20} color="#8F9BB3" />
              </TouchableOpacity>
            )}
          </View>
          {exerciseSearchLoading ? (
            <View style={styles.searchLoadingContainer}>
              <ActivityIndicator size="large" color="#92A3FD" />
              <Text style={styles.searchLoadingText}>Übungen werden geladen...</Text>
            </View>
          ) : availableExercises.length > 0 ? (
            <FlatList
              data={availableExercises}
              keyExtractor={(item, index) => `${item?.id || index}`}
              renderItem={({ item }) => (
                <TouchableOpacity style={styles.exerciseSearchItem} onPress={() => handleSelect(item)}>
                  <View style={styles.exerciseSearchContent}>
                    <Text style={styles.exerciseSearchName}>{item.name}</Text>
                  </View>
                  <Ionicons name="add-circle" size={24} color="#92A3FD" />
                </TouchableOpacity>
              )}
              contentContainerStyle={styles.exerciseSearchList}
            />
          ) : (
            <View style={styles.noExercisesFoundContainer}>
              <Ionicons name="search" size={40} color="#C5CEE0" />
              <Text style={styles.noExercisesFoundText}>Keine Übungen gefunden</Text>
              <TouchableOpacity onPress={onClose} style={styles.createNewExerciseButton}>
                <Text style={styles.createNewExerciseText}>Neue Übung erstellen</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </KeyboardAvoidingView>
    </Modal>
  );
};

export default QuickAddModal;

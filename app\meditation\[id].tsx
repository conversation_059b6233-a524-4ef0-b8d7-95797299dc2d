import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Dimensions,
  Platform,
  StatusBar,
  ImageBackground,
  ActivityIndicator,
  Vibration,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Stack, useLocalSearchParams, router } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { Audio } from 'expo-av';
import * as Haptics from 'expo-haptics';

const { width, height } = Dimensions.get('window');

interface MeditationData {
  id: number;
  title: string;
  duration: number;
  description: string;
  imageUrl: string;
  audioUrl: string;
  benefits: string[];
  difficulty: string;
  category: string;
}

const getMeditationData = (id: string): MeditationData => {
  return {
    id: parseInt(id),
    title: "Morgenmeditation für innere Ruhe",
    duration: 600, // 10 minutes in seconds
    description: "Beginne deinen Tag mit dieser sanften Meditation, die dir hilft, dich zu zentrieren und deinen Geist auf den Tag vorzubereiten. Diese geführte Meditation fokussiert sich auf tiefe Atmung und bewusste Körperwahrnehmung.",
    imageUrl: "https://images.unsplash.com/photo-1545389336-cf090694435e?q=80&w=1000",
    audioUrl: "https://s3.amazonaws.com/exp-us-standard/audio/playlist-example/Comfort_Fit_-_03_-_Sorry.mp3",
    benefits: [
      "Reduziert Stress und Angstzustände",
      "Verbessert die Konzentration",
      "Fördert emotionales Wohlbefinden",
      "Hilft bei der Achtsamkeitspraxis"
    ],
    difficulty: "Anfänger",
    category: "Morgenroutine"
  };
};

export default function MeditationSession() {
  const { id } = useLocalSearchParams();
  const meditationData = getMeditationData(id as string);
  
  const [isPlaying, setIsPlaying] = useState(false);
  const [progress, setProgress] = useState(0);
  const [remainingTime, setRemainingTime] = useState(meditationData.duration);
  const [sound, setSound] = useState<Audio.Sound | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [imageLoaded, setImageLoaded] = useState(false);
  
  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.95)).current;
  const breatheAnim = useRef(new Animated.Value(1)).current;
  const progressAnim = useRef(new Animated.Value(0)).current;
  const rotateAnim = useRef(new Animated.Value(0)).current;
  const flashAnim = useRef(new Animated.Value(0)).current;
  
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const backgroundSoundRef = useRef<Audio.Sound | null>(null);

  useEffect(() => {
    setupAudio();
    startIntroAnimations();
    loadBackgroundSound();
    
    return () => {
      cleanupResources();
    };
  }, []);

  const setupAudio = async () => {
    try {
      // Request audio permissions
      await Audio.requestPermissionsAsync();
      await Audio.setAudioModeAsync({
        playsInSilentModeIOS: true,
        staysActiveInBackground: true,
        shouldDuckAndroid: true,
      });
      
      loadMeditationAudio();
    } catch (error) {
      console.error('Error setting up audio:', error);
    }
  };

  const loadBackgroundSound = async () => {
    try {
      const { sound } = await Audio.Sound.createAsync(
        require('../../assets/sounds/ambient.mp3'), // You'll need to add this file
        { volume: 0.2, isLooping: true }
      );
      backgroundSoundRef.current = sound;
    } catch (error) {
      console.error('Error loading background sound:', error);
    }
  };

  const startIntroAnimations = () => {
    // Fade in content
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1200,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 1200,
        useNativeDriver: true,
      }),
      Animated.timing(rotateAnim, {
        toValue: 1,
        duration: 1200,
        useNativeDriver: true,
      })
    ]).start();
    
    // Start breathing indicator animation
    startBreathingAnimation();
  };

  const loadMeditationAudio = async () => {
    try {
      setIsLoading(true);
      
      if (sound) {
        await sound.unloadAsync();
      }
      
      const { sound: newSound } = await Audio.Sound.createAsync(
        { uri: meditationData.audioUrl },
        { 
          shouldPlay: false, 
          progressUpdateIntervalMillis: 1000,
          positionMillis: 0,
          volume: 1.0,
        },
        onPlaybackStatusUpdate
      );
      
      setSound(newSound);
      setIsLoading(false);
    } catch (error) {
      console.error('Error loading meditation audio:', error);
      setIsLoading(false);
    }
  };

  const cleanupResources = async () => {
    // Stop and unload all audio resources
    if (sound) {
      await sound.stopAsync();
      await sound.unloadAsync();
    }
    
    if (backgroundSoundRef.current) {
      await backgroundSoundRef.current.stopAsync();
      await backgroundSoundRef.current.unloadAsync();
    }
    
    // Clear any intervals
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
    
    // Stop any ongoing animations
    breatheAnim.stopAnimation();
    fadeAnim.stopAnimation();
    scaleAnim.stopAnimation();
    rotateAnim.stopAnimation();
  };

  const onPlaybackStatusUpdate = (status: any) => {
    if (!status.isLoaded) return;
    
    if (status.isPlaying) {
      const currentProgress = status.positionMillis / (meditationData.duration * 1000);
      setProgress(currentProgress);
      setRemainingTime(Math.max(0, meditationData.duration - Math.floor(status.positionMillis / 1000)));
      
      // Update the progress animation
      progressAnim.setValue(currentProgress);
      
      // If meditation finished
      if (status.didJustFinish) {
        handleMeditationComplete();
      }
    }
  };

  const handleMeditationComplete = async () => {
    setIsPlaying(false);
    setProgress(1);
    
    // Vibrate to signal completion
    Vibration.vibrate([0, 500, 200, 500]);
    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    
    // Flash animation
    Animated.sequence([
      Animated.timing(flashAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(flashAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      })
    ]).start();
    
    // Reset after a short delay
    setTimeout(() => {
      resetMeditation();
    }, 2000);
  };

  const startBreathingAnimation = () => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(breatheAnim, {
          toValue: 1.15,
          duration: 4000,
          useNativeDriver: true,
        }),
        Animated.timing(breatheAnim, {
          toValue: 1,
          duration: 4000,
          useNativeDriver: true,
        }),
      ])
    ).start();
  };

  const togglePlayPause = async () => {
    if (isLoading || !sound) return;
    
    try {
      // Haptic feedback
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      
      if (isPlaying) {
        await sound.pauseAsync();
        if (backgroundSoundRef.current) {
          await backgroundSoundRef.current.pauseAsync();
        }
      } else {
        if (backgroundSoundRef.current) {
          await backgroundSoundRef.current.playAsync();
        }
        await sound.playAsync();
        
        // Small button animation
        Animated.sequence([
          Animated.timing(scaleAnim, {
            toValue: 0.98,
            duration: 100,
            useNativeDriver: true,
          }),
          Animated.timing(scaleAnim, {
            toValue: 1,
            duration: 100,
            useNativeDriver: true,
          }),
        ]).start();
      }
      
      setIsPlaying(!isPlaying);
    } catch (error) {
      console.error('Error toggling play/pause:', error);
    }
  };

  const resetMeditation = async () => {
    if (!sound) return;
    
    try {
      // Haptic feedback
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      
      await sound.stopAsync();
      await sound.setPositionAsync(0);
      setIsPlaying(false);
      setProgress(0);
      setRemainingTime(meditationData.duration);
      progressAnim.setValue(0);
      
      // Breathing animation
      Animated.sequence([
        Animated.timing(breatheAnim, {
          toValue: 0.9,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(breatheAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    } catch (error) {
      console.error('Error resetting meditation:', error);
    }
  };

  const handleBackButton = async () => {
    // Haptic feedback
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    
    // Fade out before navigating back
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 0.95,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start(() => {
      router.back();
    });
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Rotation animation for the progress circle
  const rotateInterpolation = progressAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  // Glow effect for the play button when playing
  const glowInterpolation = isPlaying ? breatheAnim.interpolate({
    inputRange: [1, 1.15],
    outputRange: [1, 1.2],
  }) : 1;

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" />
      
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />
      
      <ImageBackground
        source={{ uri: meditationData.imageUrl }}
        style={styles.backgroundImage}
        onLoadEnd={() => setImageLoaded(true)}
      >
        {!imageLoaded && (
          <View style={styles.loaderContainer}>
            <ActivityIndicator size="large" color="#fff" />
          </View>
        )}
        
        <LinearGradient
          colors={['rgba(0,0,0,0.3)', 'rgba(0,0,0,0.65)']}
          style={styles.gradient}
        >
          <SafeAreaView style={styles.safeArea}>
            {/* Header */}
            <Animated.View 
              style={[
                styles.header,
                { 
                  opacity: fadeAnim,
                  transform: [{ translateY: fadeAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [-20, 0]
                  })}]
                }
              ]}
            >
              <TouchableOpacity
                style={styles.backButton}
                onPress={handleBackButton}
              >
                <BlurView intensity={40} style={styles.blurButton}>
                  <Ionicons name="chevron-back" size={22} color="#fff" />
                </BlurView>
              </TouchableOpacity>
              
              <View style={styles.headerContent}>
                <Text style={styles.categoryLabel}>{meditationData.category}</Text>
                <Text style={styles.difficultyLabel}>{meditationData.difficulty}</Text>
              </View>
              
              <TouchableOpacity
                style={styles.infoButton}
              >
                <BlurView intensity={40} style={styles.blurButton}>
                  <Ionicons name="information-circle-outline" size={22} color="#fff" />
                </BlurView>
              </TouchableOpacity>
            </Animated.View>
            
            <Animated.View 
              style={[
                styles.contentContainer,
                { 
                  opacity: fadeAnim,
                  transform: [{ scale: scaleAnim }] 
                }
              ]}
            >
              {/* Title Section */}
              <View style={styles.titleSection}>
                <Text style={styles.title}>{meditationData.title}</Text>
                <Text style={styles.duration}>{formatTime(meditationData.duration)}</Text>
              </View>
              
              {/* Meditation Controls */}
              <View style={styles.meditationContainer}>
                <Animated.View 
                  style={[
                    styles.progressContainer,
                    {
                      transform: [
                        { scale: breatheAnim }
                      ]
                    }
                  ]}
                >
                  {/* Progress Ring */}
                  <View style={styles.progressRingContainer}>
                    <View style={styles.progressBackgroundRing} />
                    
                    <Animated.View 
                      style={[
                        styles.progressRing, 
                        {
                          transform: [
                            { rotate: rotateInterpolation }
                          ]
                        }
                      ]}
                    >
                      <View style={styles.progressIndicator} />
                    </Animated.View>
                    
                    <BlurView intensity={25} style={styles.progressBlur}>
                      <Animated.View 
                        style={[
                          styles.innerCircle,
                          {
                            opacity: flashAnim.interpolate({
                              inputRange: [0, 1],
                              outputRange: [1, 0.7]
                            })
                          }
                        ]}
                      >
                        <Text style={styles.remainingTime}>{formatTime(remainingTime)}</Text>
                        <Text style={styles.remainingLabel}>
                          {isPlaying ? "verbleibend" : "Gesamtzeit"}
                        </Text>
                      </Animated.View>
                    </BlurView>
                  </View>
                </Animated.View>
                
                {/* Control Buttons */}
                <View style={styles.controls}>
                  <TouchableOpacity
                    style={styles.controlButton}
                    onPress={resetMeditation}
                    disabled={isLoading}
                  >
                    <BlurView intensity={25} style={styles.controlBlur}>
                      <Ionicons name="refresh-outline" size={24} color="#fff" />
                    </BlurView>
                  </TouchableOpacity>
                  
                  <Animated.View 
                    style={[
                      styles.playButtonContainer,
                      {
                        transform: [{ scale: glowInterpolation }]
                      }
                    ]}
                  >
                    <TouchableOpacity
                      style={styles.playPauseButton}
                      onPress={togglePlayPause}
                      disabled={isLoading}
                    >
                      <LinearGradient
                        colors={isPlaying ? ['#92A3FD', '#9DCEFF'] : ['rgba(255,255,255,0.2)', 'rgba(255,255,255,0.3)']}
                        style={styles.playButtonGradient}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 1 }}
                      >
                        {isLoading ? (
                          <ActivityIndicator color="#fff" size="small" />
                        ) : (
                          <Ionicons
                            name={isPlaying ? "pause" : "play"}
                            size={36}
                            color="#fff"
                            style={isPlaying ? {} : { marginLeft: 4 }}
                          />
                        )}
                      </LinearGradient>
                    </TouchableOpacity>
                  </Animated.View>
                  
                  <TouchableOpacity
                    style={styles.controlButton}
                    onPress={handleBackButton}
                  >
                    <BlurView intensity={25} style={styles.controlBlur}>
                      <Ionicons name="close-outline" size={24} color="#fff" />
                    </BlurView>
                  </TouchableOpacity>
                </View>
              </View>
              
              {/* Info Panel */}
              <View style={styles.infoContainer}>
                <BlurView intensity={25} style={styles.infoBlur}>
                  <View style={styles.descriptionContainer}>
                    <Text style={styles.descriptionTitle}>Über diese Meditation</Text>
                    <Text style={styles.description}>{meditationData.description}</Text>
                  </View>
                  
                  <View style={styles.benefitsContainer}>
                    <Text style={styles.benefitsTitle}>Vorteile</Text>
                    <View style={styles.benefitsList}>
                      {meditationData.benefits.map((benefit, index) => (
                        <View key={index} style={styles.benefitItem}>
                          <LinearGradient
                            colors={['#92A3FD', '#9DCEFF']}
                            style={styles.checkmarkCircle}
                            start={{ x: 0, y: 0 }}
                            end={{ x: 1, y: 1 }}
                          >
                            <Ionicons name="checkmark" size={12} color="#fff" />
                          </LinearGradient>
                          <Text style={styles.benefitText}>{benefit}</Text>
                        </View>
                      ))}
                    </View>
                  </View>
                </BlurView>
              </View>
            </Animated.View>
          </SafeAreaView>
        </LinearGradient>
      </ImageBackground>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  safeArea: {
    flex: 1,
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
  },
  backgroundImage: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  loaderContainer: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  gradient: {
    flex: 1,
    justifyContent: 'space-between',
    padding: 20,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
    paddingTop: 10,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 10,
  },
  categoryLabel: {
    fontSize: 14,
    color: '#fff',
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
    overflow: 'hidden',
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  difficultyLabel: {
    fontSize: 14,
    color: '#fff',
    backgroundColor: 'rgba(146, 163, 253, 0.4)',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
    overflow: 'hidden',
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  backButton: {
    borderRadius: 20,
    overflow: 'hidden',
  },
  infoButton: {
    borderRadius: 20,
    overflow: 'hidden',
  },
  blurButton: {
    width: 38,
    height: 38,
    borderRadius: 19,
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'space-between',
  },
  titleSection: {
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    textAlign: 'center',
    marginBottom: 8,
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 3,
  },
  duration: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  meditationContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 20,
  },
  progressContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 40,
  },
  progressRingContainer: {
    width: 230,
    height: 230,
    justifyContent: 'center',
    alignItems: 'center',
  },
  progressBackgroundRing: {
    position: 'absolute',
    width: 230,
    height: 230,
    borderRadius: 115,
    borderWidth: 5,
    borderColor: 'rgba(255, 255, 255, 0.15)',
  },
  progressRing: {
    position: 'absolute',
    width: 230,
    height: 230,
    borderRadius: 115,
    borderWidth: 5,
    borderColor: 'transparent',
    borderLeftColor: '#92A3FD',
    borderTopColor: '#92A3FD',
    borderRightColor: '#92A3FD',
  },
  progressBlur: {
    width: 210,
    height: 210,
    borderRadius: 105,
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  innerCircle: {
    width: 210,
    height: 210,
    borderRadius: 105,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
  },
  progressIndicator: {
    position: 'absolute',
    top: -2,
    right: -2,
    width: 14,
    height: 14,
    borderRadius: 7,
    backgroundColor: '#92A3FD',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.5,
    shadowRadius: 4,
    elevation: 5,
  },
  remainingTime: {
    fontSize: 44,
    fontWeight: 'bold',
    color: '#fff',
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 3,
  },
  remainingLabel: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  controls: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
  },
  controlButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 20,
    overflow: 'hidden',
  },
  controlBlur: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 24,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  playButtonContainer: {
    width: 88,
    height: 88,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#92A3FD',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.5,
    shadowRadius: 10,
    elevation: 10,
  },
  playPauseButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
  },
  playButtonGradient: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 40,
  },
  infoContainer: {
    width: '100%',
    borderRadius: 24,
    overflow: 'hidden',
    marginTop: 20,
    marginBottom: Platform.OS === 'ios' ? 0 : 20,
  },
  infoBlur: {
    padding: 20,
    borderRadius: 24,
  },
  descriptionContainer: {
    marginBottom: 20,
  },
  descriptionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 12,
  },
  description: {
    fontSize: 15,
    color: 'rgba(255, 255, 255, 0.8)',
    lineHeight: 22,
  },
  benefitsContainer: {
    marginBottom: 10,
  },
  benefitsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 12,
  },
  benefitsList: {
    gap: 10,
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  checkmarkCircle: {
    width: 22,
    height: 22,
    borderRadius: 11,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  benefitText: {
    fontSize: 15,
    color: 'rgba(255, 255, 255, 0.8)',
    flex: 1,
  },
});

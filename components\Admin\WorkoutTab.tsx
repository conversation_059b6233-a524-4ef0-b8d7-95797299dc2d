import React from "react";
import { View, StyleSheet } from "react-native";
import WorkoutManager from "./WorkoutManager";

interface WorkoutTabProps {
  refreshWorkouts: () => Promise<void>;
}

const WorkoutTab: React.FC<WorkoutTabProps> = ({ refreshWorkouts }) => {
  return (
    <View style={styles.container}>
      <WorkoutManager refreshWorkouts={refreshWorkouts} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default WorkoutTab;

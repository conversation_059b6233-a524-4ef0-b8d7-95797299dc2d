// src/components/ExerciseModal.tsx
import React, { useState, useEffect } from "react";
import {
  Modal,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Image,
  Alert,
  ActivityIndicator,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  StatusBar,
  Dimensions,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import * as ImagePicker from "expo-image-picker";
import { supabase } from "../../../lib/supabase"; // Correct import path
import styles from "./styles";
import { LinearGradient } from "expo-linear-gradient";
import { BlurView } from "expo-blur";

const { width, height } = Dimensions.get('window');

type ExerciseModalProps = {
  visible: boolean;
  onClose: () => void;
  onSave: (exercise: any) => void;
  exercise: any | null;
};

const ExerciseModal = ({ visible, onClose, onSave, exercise }: ExerciseModalProps) => {
  const [exerciseName, setExerciseName] = useState("");
  const [exerciseDuration, setExerciseDuration] = useState("");
  const [exerciseReps, setExerciseReps] = useState("");
  const [exerciseImageUri, setExerciseImageUri] = useState<string | null>(null);
  const [exerciseVideoUri, setExerciseVideoUri] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);

  useEffect(() => {
    if (exercise) {
      setExerciseName(exercise.name);
      setExerciseDuration(exercise.duration?.toString() || "");
      setExerciseReps(exercise.reps?.toString() || "");
      setExerciseImageUri(exercise.image_url);
      setExerciseVideoUri(exercise.video_url);
    } else {
      resetForm();
    }
  }, [exercise, visible]);

  const resetForm = () => {
    setExerciseName("");
    setExerciseDuration("");
    setExerciseReps("");
    setExerciseImageUri(null);
    setExerciseVideoUri(null);
  };

  const pickImage = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.8,
    });

    if (!result.canceled && result.assets && result.assets.length > 0) {
      try {
        setIsUploading(true);
        const uri = result.assets[0].uri;

        // If the URI is already a Supabase URL, use it directly
        if (uri && (uri.includes('supabase.co') || uri.startsWith('https://'))) {
          setExerciseImageUri(uri);
          setIsUploading(false);
          return;
        }

        // Process and upload the image
        const fileExt = uri.split(".").pop()?.toLowerCase() || "jpg";
        const fileName = `exercise_${Date.now()}.${fileExt}`;

        const response = await fetch(uri);
        if (!response.ok) {
          throw new Error(`Failed to fetch image: ${response.status}`);
        }
        
        const blob = await response.blob();
        
        const { error: uploadError } = await supabase.storage
          .from("workout-images")
          .upload(fileName, blob, {
            contentType: `image/${fileExt === 'jpg' ? 'jpeg' : fileExt}`,
            upsert: true
          });
          
        if (uploadError) {
          throw uploadError;
        }
        
        const { data } = supabase.storage
          .from("workout-images")
          .getPublicUrl(fileName);
          
        setExerciseImageUri(data.publicUrl);
        
      } catch (error) {
        Alert.alert(
          "Fehler beim Hochladen",
          "Das Bild konnte nicht hochgeladen werden. Bitte versuche es später erneut."
        );
      } finally {
        setIsUploading(false);
      }
    }
  };

  const pickVideo = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Videos,
      allowsEditing: true,
      aspect: [16, 9],
      quality: 0.8,
    });
    if (!result.canceled && result.assets && result.assets.length > 0) {
      setExerciseVideoUri(result.assets[0].uri);
    }
  };

  const validateExerciseForm = () => {
    if (!exerciseName.trim()) {
      return "Bitte gib einen Namen für die Übung ein";
    }
    return null;
  };

  const handleSave = () => {
    const validationError = validateExerciseForm();
    if (validationError) {
      Alert.alert("Fehler", validationError);
      return;
    }

    onSave({
      name: exerciseName,
      duration: exerciseDuration || null,
      reps: exerciseReps || null,
      image_url: exerciseImageUri,
      video_url: exerciseVideoUri,
    });
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
      statusBarTranslucent
    >
      <View style={styles.exerciseModalFullscreen}>
        <StatusBar barStyle="light-content" translucent backgroundColor="rgba(0,0,0,0.6)" />
        
        {/* Full screen gradient background */}
        <LinearGradient
          colors={['#92A3FD', '#9DCEFF']}
          start={[0, 0]}
          end={[1, 1]}
          style={styles.exerciseModalGradientBackground}
        >
          <BlurView intensity={20} tint="light" style={styles.exerciseModalBlurOverlay}>
            <KeyboardAvoidingView
              behavior={Platform.OS === "ios" ? "padding" : "height"}
              style={styles.exerciseModalContainer}
              keyboardVerticalOffset={Platform.OS === "ios" ? 40 : 0}
            >
              <View style={styles.exerciseModalCard}>
                {/* Modal Header with Gradient */}
                <LinearGradient
                  colors={['#92A3FD', '#9DCEFF']}
                  start={[0, 0]}
                  end={[1, 0]}
                  style={styles.exerciseModalHeader}
                >
                  <Text style={styles.exerciseModalTitle}>
                    {exercise ? "Übung bearbeiten" : "Neue Übung erstellen"}
                  </Text>
                  <TouchableOpacity 
                    onPress={onClose} 
                    style={[styles.closeButton, { backgroundColor: 'rgba(255,255,255,0.2)' }]}
                  >
                    <Ionicons name="close-outline" size={26} color="#fff" />
                  </TouchableOpacity>
                </LinearGradient>

                <ScrollView
                  style={styles.exerciseModalScrollView}
                  contentContainerStyle={styles.exerciseModalContent}
                  showsVerticalScrollIndicator={false}
                >
                  {/* Exercise Name with Icon */}
                  <View style={styles.inputWithIcon}>
                    <Ionicons name="barbell-outline" size={22} color="#92A3FD" style={styles.inputIcon} />
                    <TextInput
                      style={styles.inputWithIconField}
                      placeholder="Übungsname"
                      value={exerciseName}
                      onChangeText={setExerciseName}
                      placeholderTextColor="#A0A0A0"
                    />
                  </View>

                  {/* Exercise Details Row */}
                  <View style={styles.exerciseDetailRow}>
                    {/* Duration Input */}
                    <View style={[styles.inputWithIcon, { flex: 1, marginRight: 8 }]}>
                      <Ionicons name="time-outline" size={22} color="#92A3FD" style={styles.inputIcon} />
                      <TextInput
                        style={styles.inputWithIconField}
                        placeholder="Dauer (s)"
                        value={exerciseDuration}
                        onChangeText={setExerciseDuration}
                        keyboardType="numeric"
                        placeholderTextColor="#A0A0A0"
                      />
                    </View>

                    {/* Reps Input */}
                    <View style={[styles.inputWithIcon, { flex: 1, marginLeft: 8 }]}>
                      <Ionicons name="repeat-outline" size={22} color="#92A3FD" style={styles.inputIcon} />
                      <TextInput
                        style={styles.inputWithIconField}
                        placeholder="Wiederholungen"
                        value={exerciseReps}
                        onChangeText={setExerciseReps}
                        keyboardType="numeric"
                        placeholderTextColor="#A0A0A0"
                      />
                    </View>
                  </View>

                  {/* Info Card */}
                  <View style={styles.infoCard}>
                    <Ionicons name="information-circle" size={24} color="#92A3FD" style={{ marginRight: 12 }} />
                    <Text style={styles.infoCardText}>
                      Füge ein Bild hinzu, um die Übung zu visualisieren und optional ein Video für detaillierte Anleitungen.
                    </Text>
                  </View>

                  {/* Exercise Image Picker */}
                  <Text style={styles.sectionLabelText}>Übungsbild</Text>
                  <TouchableOpacity 
                    onPress={pickImage} 
                    style={[
                      styles.mediaPicker, 
                      isUploading && styles.mediaPickerDisabled
                    ]}
                    disabled={isUploading}
                  >
                    {isUploading ? (
                      <View style={styles.mediaPickerLoading}>
                        <ActivityIndicator size="large" color="#92A3FD" />
                        <Text style={styles.mediaPickerLoadingText}>Wird hochgeladen...</Text>
                      </View>
                    ) : exerciseImageUri ? (
                      <Image 
                        source={{ uri: exerciseImageUri }} 
                        style={styles.mediaPreview} 
                        resizeMode="cover"
                      />
                    ) : (
                      <View style={styles.mediaPickerPlaceholder}>
                        <LinearGradient
                          colors={['rgba(146, 163, 253, 0.2)', 'rgba(157, 206, 255, 0.2)']}
                          style={styles.mediaPickerGradient}
                        >
                          <Ionicons name="image-outline" size={40} color="#92A3FD" />
                          <Text style={styles.mediaPickerText}>Bild auswählen</Text>
                        </LinearGradient>
                      </View>
                    )}
                  </TouchableOpacity>

                  {/* Exercise Video Picker */}
                  <Text style={styles.sectionLabelText}>Übungsvideo (optional)</Text>
                  <TouchableOpacity onPress={pickVideo} style={styles.mediaPicker}>
                    {exerciseVideoUri ? (
                      <View style={styles.videoPickerSelected}>
                        <Ionicons name="videocam" size={30} color="#92A3FD" />
                        <Text style={styles.videoPickerSelectedText}>Video ausgewählt</Text>
                      </View>
                    ) : (
                      <View style={styles.mediaPickerPlaceholder}>
                        <LinearGradient
                          colors={['rgba(146, 163, 253, 0.2)', 'rgba(157, 206, 255, 0.2)']}
                          style={styles.mediaPickerGradient}
                        >
                          <Ionicons name="videocam-outline" size={40} color="#92A3FD" />
                          <Text style={styles.mediaPickerText}>Video auswählen</Text>
                        </LinearGradient>
                      </View>
                    )}
                  </TouchableOpacity>
                </ScrollView>

                {/* Button Actions */}
                <View style={styles.exerciseModalActions}>
                  <TouchableOpacity 
                    onPress={onClose} 
                    style={styles.exerciseModalCancelButton}
                  >
                    <Text style={styles.exerciseModalCancelButtonText}>Abbrechen</Text>
                  </TouchableOpacity>
                  
                  <TouchableOpacity
                    onPress={handleSave}
                    style={[
                      styles.exerciseModalSaveButton, 
                      isUploading && styles.disabledButton
                    ]}
                    disabled={isUploading}
                  >
                    <LinearGradient
                      colors={['#92A3FD', '#9DCEFF']}
                      start={[0, 0]}
                      end={[1, 0]}
                      style={styles.exerciseModalSaveGradient}
                    >
                      {isUploading ? (
                        <ActivityIndicator size="small" color="#fff" />
                      ) : (
                        <Text style={styles.exerciseModalSaveButtonText}>Speichern</Text>
                      )}
                    </LinearGradient>
                  </TouchableOpacity>
                </View>
              </View>
            </KeyboardAvoidingView>
          </BlurView>
        </LinearGradient>
      </View>
    </Modal>
  );
};

export default ExerciseModal;

// src/components/WorkoutForm.tsx
import React, { useState, useEffect } from "react";
import {
  Modal,
  View,
  Text,
  ScrollView,
  TextInput,
  TouchableOpacity,
  ActivityIndicator,
  Image,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import * as ImagePicker from "expo-image-picker";
import { supabase } from "../../../lib/supabase";
import ExerciseModal from "./ExerciseModal";
import styles from "./styles";

const workoutTypes = [
  "Cardio",
  "Strength",
  "Flexibility",
  "Balance",
  "HIIT",
  "Push",
  "Pull",
  "Legs",
  "Core",
  "Full Body",
];

const WorkoutForm = ({
  visible,
  onClose,
  workout,
  isEditing,
}: {
  visible: boolean;
  onClose: () => void;
  workout: any;
  isEditing: boolean;
}) => {
  const [name, setName] = useState("");
  const [type, setType] = useState("Strength");
  const [difficulty, setDifficulty] = useState("beginner");
  const [duration, setDuration] = useState("");
  const [description, setDescription] = useState("");
  const [caloriesBurned, setCaloriesBurned] = useState("");
  const [imageUri, setImageUri] = useState<string | null>(null);
  const [exercises, setExercises] = useState<any[]>([]);
  const [createLoading, setCreateLoading] = useState(false);
  const [typeDropdownVisible, setTypeDropdownVisible] = useState(false);

  // Modal zum Hinzufügen/Bearbeiten einer Übung
  const [exerciseModalVisible, setExerciseModalVisible] = useState(false);
  const [currentExerciseIndex, setCurrentExerciseIndex] = useState<number | null>(null);

  // Added for existing exercises selection
  const [existingExercises, setExistingExercises] = useState<any[]>([]);
  const [showExercisePickerModal, setShowExercisePickerModal] = useState(false);
  const [isLoadingExercises, setIsLoadingExercises] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  // Custom delete confirmation popup state
  const [deleteConfirmVisible, setDeleteConfirmVisible] = useState(false);

  useEffect(() => {
    if (isEditing && workout) {
      setName(workout.name);
      setType(workout.type);
      setDifficulty(workout.difficulty);
      setDuration(workout.duration?.toString() || "");
      setDescription(workout.description);
      setCaloriesBurned(workout.calories_burned?.toString() || "");
      setImageUri(workout.icon);
      setExercises(workout.exercises || []);
    } else {
      resetForm();
    }
  }, [workout, isEditing, visible]);

  // Load existing exercises when the component mounts
  useEffect(() => {
    if (visible) {
      loadExistingExercises();
    }
  }, [visible]);

  const loadExistingExercises = async () => {
    try {
      setIsLoadingExercises(true);

      // Get all unique exercises from the database
      const { data, error } = await supabase
        .from("exercises")
        .select("*")
        .order("name");

      if (error) {
        throw error;
      }

      // Create a map to deduplicate exercises by name
      const uniqueExercisesMap = new Map();

      if (data) {
        data.forEach((exercise) => {
          // If we don't have this exercise yet or this one has an image when the previous didn't
          if (
            !uniqueExercisesMap.has(exercise.name) ||
            (!uniqueExercisesMap.get(exercise.name).image_url && exercise.image_url)
          ) {
            uniqueExercisesMap.set(exercise.name, exercise);
          }
        });
      }

      // Convert map values to array
      const uniqueExercises = Array.from(uniqueExercisesMap.values());
      setExistingExercises(uniqueExercises);
    } catch (error) {
      console.error("Error loading existing exercises:", error);
      Alert.alert("Fehler", "Übungen konnten nicht geladen werden.");
    } finally {
      setIsLoadingExercises(false);
    }
  };

  const resetForm = () => {
    setName("");
    setType("Strength");
    setDifficulty("beginner");
    setDuration("");
    setDescription("");
    setCaloriesBurned("");
    setImageUri(null);
    setExercises([]);
  };

  const pickImage = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [16, 9],
      quality: 0.8,
    });
    if (!result.canceled && result.assets && result.assets.length > 0) {
      setImageUri(result.assets[0].uri);
    }
  };

  const uploadFile = async (uri: string, folder: string): Promise<string> => {
    try {
      console.log(
        `Starting upload process for URI type: ${
          uri ? uri.substring(0, 30) : "null/undefined"
        }...`
      );

      // Safety check - if URI is null/undefined/empty, throw error
      if (!uri) {
        throw new Error("No valid image URI provided");
      }

      // If the URI is already a Supabase URL, return it directly
      if (uri && (uri.includes("supabase.co") || uri.startsWith("https://"))) {
        console.log("Using existing URL", uri);
        return uri;
      }

      // Base64-Daten erkennen und verarbeiten
      if (uri && uri.startsWith("data:image")) {
        console.log("Detected base64 image data");
        try {
          // Base64-Daten extrahieren
          const matches = uri.match(/^data:([A-Za-z-+\/]+);base64,(.+)$/);

          if (!matches || matches.length !== 3) {
            throw new Error("Invalid base64 image format");
          }

          const mimeType = matches[1];
          const base64Data = matches[2];
          const fileExt = mimeType.split("/")[1] || "jpg";

          // Dateiname generieren
          const fileName = `workout_${Date.now()}.${fileExt}`;
          const filePath = `${fileName}`;

          console.log(`Will upload base64 image to: ${folder}/${filePath}`);

          // Base64 zu Blob konvertieren
          const byteCharacters = atob(base64Data);
          const byteArrays = [];

          for (let offset = 0; offset < byteCharacters.length; offset += 512) {
            const slice = byteCharacters.slice(offset, offset + 512);
            const byteNumbers = new Array(slice.length);

            for (let i = 0; i < slice.length; i++) {
              byteNumbers[i] = slice.charCodeAt(i);
            }

            const byteArray = new Uint8Array(byteNumbers);
            byteArrays.push(byteArray);
          }

          const blob = new Blob(byteArrays, { type: mimeType });
          console.log(
            `Created blob from base64 data, size: ${blob.size} bytes, type: ${mimeType}`
          );

          // Upload zu Supabase
          const { error: uploadError } = await supabase.storage
            .from("workout-images")
            .upload(filePath, blob, {
              contentType: mimeType,
              upsert: true,
            });

          if (uploadError) {
            console.error("Base64 upload error:", uploadError);
            throw uploadError;
          }

          // Öffentliche URL abrufen
          const { data } = supabase.storage
            .from("workout-images")
            .getPublicUrl(filePath);

          console.log("Base64 image uploaded successfully");
          return data.publicUrl;
        } catch (base64Error) {
          console.error("Error processing base64 image:", base64Error);
          throw new Error("Failed to process base64 image data");
        }
      }

      // Normale Datei-URI
      if (!uri) throw new Error("No URI provided");

      const fileExt = uri.split(".").pop()?.toLowerCase() || "jpg";
      const fileName = `workout_${Date.now()}.${fileExt}`;
      const filePath = `${fileName}`;

      console.log(`Uploading to: ${folder}/${filePath}`);

      // Bild von der URI als Blob laden
      const response = await fetch(uri);
      if (!response.ok) {
        throw new Error(`Failed to fetch image: ${response.status}`);
      }

      const blob = await response.blob();
      console.log(
        `Blob created with size: ${blob.size} bytes and type: ${blob.type}`
      );

      // Upload zu Supabase
      const { error: uploadError } = await supabase.storage
        .from("workout-images")
        .upload(filePath, blob, {
          contentType: `image/${fileExt === "jpg" ? "jpeg" : fileExt}`,
          upsert: true,
        });

      if (uploadError) {
        console.error("Upload error details:", uploadError);
        throw uploadError;
      }

      // Öffentliche URL des hochgeladenen Bildes abrufen
      const { data } = supabase.storage
        .from("workout-images")
        .getPublicUrl(filePath);

      console.log("Image uploaded successfully");
      return data.publicUrl;
    } catch (error) {
      console.error("Error during image upload:", error);
      throw error;
    }
  };

  const validateWorkoutForm = () => {
    if (!name.trim()) return "Workout name is required";
    if (!type.trim()) return "Type is required";
    if (!difficulty.trim()) return "Difficulty is required";
    if (!duration.trim() || isNaN(parseInt(duration)))
      return "Valid duration is required";
    if (!description.trim()) return "Description is required";
    if (!caloriesBurned.trim() || isNaN(parseInt(caloriesBurned)))
      return "Valid calories burned is required";
    if (exercises.length === 0) return "At least one exercise is required";
    return null;
  };

  const handleSubmit = async () => {
    const errorMsg = validateWorkoutForm();
    if (errorMsg) {
      Alert.alert("Validation Error", errorMsg);
      return;
    }

    try {
      setCreateLoading(true);
      let imageUrl = null;

      // If we're editing and the imageUri is the same as the existing icon, use it directly
      if (isEditing && imageUri === workout.icon) {
        imageUrl = workout.icon;
      }
      // Otherwise if we have an imageUri, try to upload it
      else if (imageUri) {
        try {
          console.log("Starting image upload...");
          imageUrl = await uploadFile(imageUri, "workout-images");
          console.log("Image upload successful:", imageUrl);
        } catch (imageError) {
          console.error("Image upload failed with error:", imageError);

          // If editing and we have an existing image, use that as fallback
          if (isEditing && workout.icon) {
            console.log("Using existing image as fallback");
            imageUrl = workout.icon;
          } else {
            const continueWithoutImage = await new Promise<boolean>((resolve) => {
              Alert.alert(
                "Bildupload fehlgeschlagen",
                "Das Bild konnte nicht hochgeladen werden. Möchten Sie das Workout trotzdem speichern?",
                [
                  { text: "Abbrechen", style: "cancel", onPress: () => resolve(false) },
                  { text: "Ohne Bild fortfahren", onPress: () => resolve(true) },
                ]
              );
            });

            if (!continueWithoutImage) {
              setCreateLoading(false);
              return;
            }
          }
        }
      } else if (isEditing && workout.icon) {
        // If no new image was selected but we have an existing one, keep it
        imageUrl = workout.icon;
      }

      // Workout-Daten vorbereiten
      const workoutData: any = {
        name,
        type,
        difficulty,
        duration: parseInt(duration) || 0,
        description,
        calories_burned: parseInt(caloriesBurned) || 0,
        exercises_count: exercises.length,
      };

      // Only set icon if we have one to prevent null overwrites
      if (imageUrl) {
        workoutData.icon = imageUrl;
      }

      // Don't update schedule_time when editing
      if (!isEditing) {
        workoutData.schedule_time = new Date().toISOString();
      }

      console.log("Saving workout data:", workoutData);

      // Workout speichern/aktualisieren
      if (isEditing) {
        const { error } = await supabase
          .from("workouts")
          .update(workoutData)
          .eq("id", workout.id);
        if (error) throw error;

        try {
          // Delete existing exercises and insert updated ones
          await supabase.from("exercises").delete().eq("workout_id", workout.id);

          // Handle exercises in smaller batches to avoid timeouts or limits
          for (const exercise of exercises) {
            await supabase.from("exercises").insert({
              workout_id: workout.id,
              name: exercise.name,
              duration: exercise.duration,
              reps: exercise.reps,
              set_number: exercise.set_number,
              image_url: exercise.image_url || "",
              video_url: exercise.video_url || "",
            });
          }

          Alert.alert("Success", "Workout updated successfully!");
        } catch (exerciseError) {
          console.error("Error updating exercises:", exerciseError);
          Alert.alert(
            "Hinweis",
            "Workout aktualisiert, aber es gab Probleme beim Speichern der Übungen."
          );
        }
      } else {
        const { data: newWorkout, error } = await supabase
          .from("workouts")
          .insert(workoutData)
          .select("id")
          .single();
        if (error) throw error;
        for (const exercise of exercises) {
          await supabase.from("exercises").insert({
            workout_id: newWorkout.id,
            name: exercise.name,
            duration: exercise.duration,
            reps: exercise.reps,
            set_number: exercise.set_number,
            image_url: exercise.image_url || "",
            video_url: exercise.video_url,
          });
        }
        Alert.alert("Success", "Workout created successfully!");
      }
      onClose();
    } catch (error) {
      console.error("Error submitting workout:", error);
      Alert.alert("Error", "Workout konnte nicht gespeichert werden.");
    } finally {
      setCreateLoading(false);
    }
  };

  const handleDelete = () => {
    if (!workout || !workout.id) {
      Alert.alert("Fehler", "Workout ID fehlt. Bitte versuche es erneut.");
      return;
    }

    // Show custom delete confirmation popup instead of Alert
    setDeleteConfirmVisible(true);
  };

  // Separate function to handle the actual deletion process
  const deleteWorkout = async (workoutId: number) => {
    try {
      setCreateLoading(true);

      // 1. Delete from workout_equipment table
      const { error: equipmentError } = await supabase
        .from("workout_equipment")
        .delete()
        .eq("workout_id", workoutId);

      if (equipmentError) {
        // Continue despite this error
      }

      // 2. Delete from assigned_workouts table
      const { error: assignedError } = await supabase
        .from("assigned_workouts")
        .delete()
        .eq("workout_id", workoutId);

      if (assignedError) {
        // Continue despite this error
      }

      // 3. Delete from workout_history table
      const { error: historyError } = await supabase
        .from("workout_history")
        .delete()
        .eq("workout_id", workoutId);

      if (historyError) {
        // Continue despite this error
      }

      // 4. Delete from exercises table
      const { error: exercisesError } = await supabase
        .from("exercises")
        .delete()
        .eq("workout_id", workoutId);

      if (exercisesError) {
        // Continue despite this error
      }

      // 5. Finally delete the workout itself
      const { error: workoutError } = await supabase
        .from("workouts")
        .delete()
        .eq("id", workoutId);

      if (workoutError) {
        throw workoutError;
      }

      // Close the form immediately and return to the list
      onClose();
    } catch (error) {
      Alert.alert(
        "Fehler",
        "Workout konnte nicht gelöscht werden. Bitte versuche es später erneut."
      );
    } finally {
      setCreateLoading(false);
      setDeleteConfirmVisible(false);
    }
  };

  // Custom Delete Confirmation Popup Component
  const DeleteConfirmationPopup = () => {
    if (!deleteConfirmVisible) return null;

    return (
      <View style={styles.customPopupOverlay}>
        <View style={styles.customPopupContainer}>
          <Text style={styles.customPopupTitle}>Workout löschen</Text>
          <Text style={styles.customPopupMessage}>
            Bist du sicher, dass du dieses Workout löschen möchtest? Diese Aktion kann nicht rückgängig gemacht werden.
          </Text>
          <View style={styles.customPopupButtonsContainer}>
            <TouchableOpacity
              style={styles.customPopupCancelButton}
              onPress={() => setDeleteConfirmVisible(false)}
            >
              <Text style={styles.customPopupCancelButtonText}>Abbrechen</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.customPopupDeleteButton}
              onPress={() => deleteWorkout(workout.id)}
            >
              <Text style={styles.customPopupDeleteButtonText}>Löschen</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  };

  const addExercise = (exercise: any) => {
    setExercises([...exercises, { ...exercise, set_number: exercises.length + 1 }]);
  };

  const handleEditExercise = (index: number) => {
    setCurrentExerciseIndex(index);
    setExerciseModalVisible(true);
  };

  const handleSaveExercise = (exercise: any) => {
    if (currentExerciseIndex !== null) {
      const updated = [...exercises];
      updated[currentExerciseIndex] = { ...exercise, set_number: currentExerciseIndex + 1 };
      setExercises(updated);
    } else {
      addExercise(exercise);
    }
    setExerciseModalVisible(false);
    setCurrentExerciseIndex(null);
  };

  const handleRemoveExercise = (index: number) => {
    const updated = exercises
      .filter((_, i) => i !== index)
      .map((ex, i) => ({ ...ex, set_number: i + 1 }));
    setExercises(updated);
  };

  const openExercisePicker = () => {
    setSearchQuery("");
    setShowExercisePickerModal(true);
  };

  const selectExistingExercise = (exercise: any) => {
    setShowExercisePickerModal(false);

    // Create a copy of the selected exercise for this workout
    const exerciseCopy = {
      name: exercise.name,
      duration: exercise.duration,
      reps: exercise.reps,
      image_url: exercise.image_url || "",
      video_url: exercise.video_url || "",
      set_number: exercises.length + 1,
    };

    setExercises([...exercises, exerciseCopy]);
  };

  const createNewExercise = () => {
    setShowExercisePickerModal(false);
    setExerciseModalVisible(true);
  };

  const filteredExercises =
    searchQuery.trim() === ""
      ? existingExercises
      : existingExercises.filter((exercise) =>
          exercise.name.toLowerCase().includes(searchQuery.toLowerCase())
        );

  // Exercise Picker Modal Component
  const ExercisePickerModal = () => {
    return (
      <Modal
        visible={showExercisePickerModal}
        transparent
        animationType="slide"
        onRequestClose={() => setShowExercisePickerModal(false)}
      >
        <View style={styles.centeredView}>
          <View style={styles.modalView}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Übung auswählen</Text>
              <TouchableOpacity
                onPress={() => setShowExercisePickerModal(false)}
                style={styles.closeButton}
              >
                <Ionicons name="close" size={24} color="#333" />
              </TouchableOpacity>
            </View>

            <TextInput
              style={styles.searchInput}
              placeholder="Suche nach Übungen..."
              value={searchQuery}
              onChangeText={setSearchQuery}
              placeholderTextColor="#A0A0A0"
            />

            <ScrollView style={styles.exerciseList}>
              {isLoadingExercises ? (
                <View style={styles.loadingContainer}>
                  <ActivityIndicator size="large" color="#92A3FD" />
                  <Text style={styles.loadingText}>Übungen werden geladen...</Text>
                </View>
              ) : filteredExercises.length > 0 ? (
                filteredExercises.map((exercise, index) => (
                  <TouchableOpacity
                    key={`${exercise.id}-${index}`}
                    style={styles.exerciseListItem}
                    onPress={() => selectExistingExercise(exercise)}
                  >
                    <View style={styles.exerciseListItemContent}>
                      {exercise.image_url ? (
                        <Image
                          source={{ uri: exercise.image_url }}
                          style={styles.exerciseThumb}
                        />
                      ) : (
                        <View
                          style={[
                            styles.exerciseThumb,
                            styles.exerciseThumbPlaceholder,
                          ]}
                        >
                          <Ionicons
                            name="fitness-outline"
                            size={24}
                            color="#92A3FD"
                          />
                        </View>
                      )}
                      <View style={styles.exerciseListItemText}>
                        <Text style={styles.exerciseListItemTitle}>
                          {exercise.name}
                        </Text>
                        <Text style={styles.exerciseListItemDetails}>
                          {exercise.duration ? `${exercise.duration}s` : ""}
                          {exercise.duration && exercise.reps ? " • " : ""}
                          {exercise.reps ? `${exercise.reps} Wdh.` : ""}
                        </Text>
                      </View>
                    </View>
                    <Ionicons
                      name="chevron-forward"
                      size={20}
                      color="#92A3FD"
                    />
                  </TouchableOpacity>
                ))
              ) : (
                <View style={styles.noExercisesContainer}>
                  <Ionicons name="search-outline" size={40} color="#C5CEE0" />
                  <Text style={styles.noExercisesText}>
                    {searchQuery.trim() !== ""
                      ? "Keine Übungen gefunden"
                      : "Keine Übungen verfügbar"}
                  </Text>
                </View>
              )}
            </ScrollView>

            <TouchableOpacity
              style={styles.createNewExerciseButton}
              onPress={createNewExercise}
            >
              <Ionicons name="add-circle" size={20} color="white" />
              <Text style={styles.createNewExerciseButtonText}>
                Neue Übung erstellen
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    );
  };

  return (
    <Modal animationType="slide" transparent visible={visible} onRequestClose={onClose}>
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.centeredView}
        keyboardVerticalOffset={Platform.OS === "ios" ? 40 : 0}
      >
        {/* Custom delete confirmation popup */}
        <DeleteConfirmationPopup />

        {/* Exercise Picker Modal */}
        <ExercisePickerModal />

        <View style={styles.modalView}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>
              {isEditing ? "Workout bearbeiten" : "Neues Workout erstellen"}
            </Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Ionicons name="close" size={24} color="#333" />
            </TouchableOpacity>
          </View>
          <ScrollView
            style={styles.formScrollView}
            contentContainerStyle={styles.formScrollViewContent}
          >
            {/* Grundinformationen */}
            <View style={styles.formSection}>
              <Text style={styles.sectionTitle}>Grundinformationen</Text>
              <TextInput
                style={styles.textInput}
                value={name}
                onChangeText={setName}
                placeholder="Workout Name"
                placeholderTextColor="#A0A0A0"
              />
              <TouchableOpacity
                onPress={() => setTypeDropdownVisible(!typeDropdownVisible)}
                style={styles.dropdownButton}
              >
                <Text style={styles.dropdownButtonText}>{type}</Text>
                <Ionicons
                  name={typeDropdownVisible ? "chevron-up" : "chevron-down"}
                  size={20}
                  color="#666"
                />
              </TouchableOpacity>
              {typeDropdownVisible && (
                <View style={styles.dropdownList}>
                  {workoutTypes.map((wt) => (
                    <TouchableOpacity
                      key={wt}
                      onPress={() => {
                        setType(wt);
                        setTypeDropdownVisible(false);
                      }}
                      style={styles.dropdownItem}
                    >
                      <Text style={styles.dropdownItemText}>{wt}</Text>
                    </TouchableOpacity>
                  ))}
                </View>
              )}
              <View style={styles.difficultySelector}>
                {["beginner", "intermediate", "advanced"].map((diff) => (
                  <TouchableOpacity
                    key={diff}
                    onPress={() => setDifficulty(diff)}
                    style={[
                      styles.difficultyOption,
                      difficulty === diff && styles.difficultyOptionSelected,
                    ]}
                  >
                    <Text
                      style={[
                        styles.difficultyOptionText,
                        difficulty === diff &&
                          styles.difficultyOptionTextSelected,
                      ]}
                    >
                      {diff.charAt(0).toUpperCase() + diff.slice(1)}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
              <TextInput
                style={styles.textInput}
                value={duration}
                onChangeText={setDuration}
                placeholder="Dauer (Min)"
                keyboardType="numeric"
                placeholderTextColor="#A0A0A0"
              />
              <TextInput
                style={[styles.textInput, styles.textAreaInput]}
                value={description}
                onChangeText={setDescription}
                placeholder="Beschreibung"
                multiline
                placeholderTextColor="#A0A0A0"
              />
              <TextInput
                style={styles.textInput}
                value={caloriesBurned}
                onChangeText={setCaloriesBurned}
                placeholder="Kalorien"
                keyboardType="numeric"
                placeholderTextColor="#A0A0A0"
              />
              <TouchableOpacity onPress={pickImage} style={styles.imagePicker}>
                {imageUri ? (
                  <Image source={{ uri: imageUri }} style={styles.previewImage} />
                ) : (
                  <View style={styles.imagePickerPlaceholder}>
                    <Ionicons name="image-outline" size={40} color="#92A3FD" />
                    <Text style={styles.imagePickerText}>Bild auswählen</Text>
                  </View>
                )}
              </TouchableOpacity>
            </View>
            {/* Übungen */}
            <View style={styles.formSection}>
              <View style={styles.sectionHeaderRow}>
                <Text style={styles.sectionTitle}>Übungen</Text>
                <View style={styles.exerciseButtonsRow}>
                  <TouchableOpacity
                    onPress={openExercisePicker}
                    style={styles.addExerciseButton}
                  >
                    <Ionicons name="add-circle" size={18} color="#92A3FD" />
                    <Text style={styles.addExerciseText}>Übung hinzufügen</Text>
                  </TouchableOpacity>
                </View>
              </View>

              {exercises.length > 0 ? (
                exercises.map((exercise, index) => (
                  <View key={index} style={styles.exerciseItem}>
                    <Text>
                      {index + 1}. {exercise.name}
                    </Text>
                    <TouchableOpacity onPress={() => handleEditExercise(index)}>
                      <Ionicons name="pencil" size={16} color="#92A3FD" />
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => handleRemoveExercise(index)}>
                      <Ionicons name="trash" size={16} color="#FF4B4B" />
                    </TouchableOpacity>
                  </View>
                ))
              ) : (
                <View style={styles.noExercisesContainer}>
                  <Ionicons name="fitness-outline" size={40} color="#C5CEE0" />
                  <Text>Keine Übungen hinzugefügt</Text>
                </View>
              )}
            </View>

            {/* Delete Workout Option */}
            {isEditing && (
              <View style={styles.deleteSection}>
                <Text style={styles.deleteSectionText}>
                  Möchtest du dieses Workout löschen?
                </Text>
                <TouchableOpacity
                  style={styles.deleteButton}
                  onPress={() => handleDelete()}
                  activeOpacity={0.7}
                >
                  <Text style={styles.deleteButtonText}>Workout löschen</Text>
                </TouchableOpacity>
              </View>
            )}
          </ScrollView>

          <View style={styles.modalActions}>
            <TouchableOpacity onPress={onClose} style={styles.cancelButton}>
              <Text style={styles.cancelButtonText}>Abbrechen</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={handleSubmit}
              style={[
                styles.saveButton,
                createLoading && styles.disabledButton,
              ]}
              disabled={createLoading}
            >
              {createLoading ? (
                <ActivityIndicator size="small" color="#fff" />
              ) : (
                <Text style={styles.saveButtonText}>
                  {isEditing ? "Aktualisieren" : "Speichern"}
                </Text>
              )}
            </TouchableOpacity>
          </View>
        </View>
      </KeyboardAvoidingView>

      <ExerciseModal
        visible={exerciseModalVisible}
        onClose={() => {
          setExerciseModalVisible(false);
          setCurrentExerciseIndex(null);
        }}
        onSave={handleSaveExercise}
        exercise={
          currentExerciseIndex !== null ? exercises[currentExerciseIndex] : null
        }
      />
    </Modal>
  );
};

export default WorkoutForm;

import { useEffect, useRef } from 'react';
import { StyleSheet, Animated, Easing } from 'react-native';

import { ThemedText } from '@/components/ThemedText';

export function HelloWave() {
  const rotationValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Create a sequence of animations
    const sequence = Animated.sequence([
      Animated.timing(rotationValue, {
        toValue: 25,
        duration: 150,
        easing: Easing.linear,
        useNativeDriver: true
      }),
      Animated.timing(rotationValue, {
        toValue: 0,
        duration: 150,
        easing: Easing.linear,
        useNativeDriver: true
      })
    ]);
    
    // Run the animation sequence 4 times
    Animated.loop(sequence, { iterations: 4 }).start();
  }, []);

  const animatedStyle = {
    transform: [
      {
        rotate: rotationValue.interpolate({
          inputRange: [0, 25],
          outputRange: ['0deg', '25deg']
        })
      }
    ]
  };

  return (
    <Animated.View style={animatedStyle}>
      <ThemedText style={styles.text}>👋</ThemedText>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  text: {
    fontSize: 28,
    lineHeight: 32,
    marginTop: -6,
  },
});

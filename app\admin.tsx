import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  SafeAreaView,
  FlatList,
  Modal,
  TextInput,
  Dimensions,
  Alert
} from "react-native";
import { Stack, router } from "expo-router";
import { supabase } from "../lib/supabase";
import { Ionicons } from "@expo/vector-icons";
import { Calendar, DateData } from 'react-native-calendars';
import { format } from 'date-fns';
// Import types from our types file
import { Workout } from "../types/workoutTypes";
// Import our new components
import WorkoutTab from "../components/Admin/WorkoutTab";
import UserTab from "../components/Admin/UserTab";

// Types
interface User {
  id: string;
  email: string;
  full_name: string;
  profile_picture_url: string | null;
  is_admin: boolean;
  created_at: string;
  last_sign_in_at: string;
  height?: number;
  weight?: number;
  birth_date?: string | null;
  subscription_type: 'basic' | 'premium' | null;
}

const { width } = Dimensions.get('window');

const AdminPage = () => {
  const [activeTab, setActiveTab] = useState("users");
  const [workouts, setWorkouts] = useState<Workout[]>([]);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [userWorkouts, setUserWorkouts] = useState<Workout[]>([]);
  const [availableWorkouts, setAvailableWorkouts] = useState<Workout[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedDate, setSelectedDate] = useState<string>(format(new Date(), 'yyyy-MM-dd'));
  const [calendarMarkedDates, setCalendarMarkedDates] = useState<{[key: string]: any}>({});
  const [dateWorkouts, setDateWorkouts] = useState<{[key: string]: Workout[]}>({});
  const [userStats, setUserStats] = useState({
    completedCount: 0,
    streakDays: 0,
    totalAssigned: 0,
    completionRate: 0,
  });

  useEffect(() => {
    if (activeTab === "workouts") {
      fetchWorkouts();
    }
  }, [activeTab]);

  useEffect(() => {
    if (selectedUser) {
      fetchUserWorkouts(selectedUser.id);
    }
  }, [selectedUser]);

  const fetchWorkouts = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from("workouts")
        .select(`
          *,
          exercises (*)
        `)
        .order("name");

      if (error) throw error;
      setWorkouts(data || []);
    } catch (error) {
      console.error("Error fetching workouts:", error);
    } finally {
      setLoading(false);
    }
  };

  const fetchUserWorkouts = async (userId: string) => {
    try {
      setLoading(true);
      // Fetch all assigned workouts for this user
      const { data: assignedData, error: assignedError } = await supabase
        .from("assigned_workouts")
        .select(`
          *,
          workouts (*)
        `)
        .eq("user_id", userId);

      if (assignedError) throw assignedError;
      
      // Fetch workout history for this user
      const { data: historyData, error: historyError } = await supabase
        .from("workout_history")
        .select(`
          *,
          workouts (*)
        `)
        .eq("user_id", userId);
        
      if (historyError) throw historyError;
      
      // Process assigned workouts by date
      const workoutsByDate: {[key: string]: Workout[]} = {};
      const markedDates: {[key: string]: any} = {};
      
      if (assignedData && assignedData.length > 0) {
        assignedData.forEach(item => {
          if (!item.workouts) return;
          
          const date = item.assigned_date;
          if (!workoutsByDate[date]) workoutsByDate[date] = [];
          
          workoutsByDate[date].push({
            ...item.workouts,
            completed: item.completed
          });
          
          // Mark dates with workouts on calendar
          markedDates[date] = {
            marked: true,
            dotColor: item.completed ? '#4CAF50' : '#FFA07A',
            selected: date === selectedDate,
            selectedColor: date === selectedDate ? 'rgba(107, 140, 255, 0.15)' : undefined,
          };
        });
      }
      
      // Update current selected date to have selected styling
      if (!markedDates[selectedDate]) {
        markedDates[selectedDate] = {
          selected: true,
          selectedColor: 'rgba(107, 140, 255, 0.15)',
        };
      }
      
      // Calculate user stats
      const completedWorkouts = assignedData ? assignedData.filter(w => w.completed).length : 0;
      const totalAssigned = assignedData ? assignedData.length : 0;
      
      // Determine streak (consecutive days with completed workouts)
      // This is a simplified approach - for production you'd want more robust calculation
      let streakDays = 0;
      if (historyData && historyData.length > 0) {
        const sortedHistory = [...historyData]
          .filter(h => h.status === 'completed')
          .sort((a, b) => new Date(b.start_time).getTime() - new Date(a.start_time).getTime());
          
        // Calculate streak (this is simplified)
        if (sortedHistory.length > 0) {
          streakDays = 1;
          for (let i = 1; i < sortedHistory.length; i++) {
            const prevDate = new Date(sortedHistory[i-1].start_time);
            const currDate = new Date(sortedHistory[i].start_time);
            
            // Check if dates are consecutive
            const diffTime = prevDate.getTime() - currDate.getTime();
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            
            if (diffDays === 1) {
              streakDays++;
            } else {
              break;
            }
          }
        }
      }
      
      setUserStats({
        completedCount: completedWorkouts,
        totalAssigned,
        streakDays,
        completionRate: totalAssigned > 0 ? Math.round((completedWorkouts / totalAssigned) * 100) : 0
      });
      
      // Set the processed workout data
      setDateWorkouts(workoutsByDate);
      setCalendarMarkedDates(markedDates);
      
      // Get all available workouts minus those already assigned for today
      const assignedIdsForSelectedDate = new Set(
        (workoutsByDate[selectedDate] || []).map(w => w.id)
      );
      
      const { data: allWorkouts } = await supabase
        .from("workouts")
        .select("*")
        .order("name");
        
      const availableForToday = (allWorkouts || [])
        .filter(w => !assignedIdsForSelectedDate.has(w.id))
        .map(w => ({...w, selected: false}));
        
      setAvailableWorkouts(availableForToday);
      setUserWorkouts(workoutsByDate[selectedDate] || []);
    } catch (error) {
      console.error("Error fetching user workouts:", error);
    } finally {
      setLoading(false);
    }
  };

  const toggleWorkoutSelection = (workoutId: number) => {
    setAvailableWorkouts(prev => 
      prev.map(w => w.id === workoutId ? {...w, selected: !w.selected} : w)
    );
  };

  const assignWorkouts = async () => {
    try {
      if (!selectedUser) return;
      
      const selectedWorkouts = availableWorkouts.filter(w => w.selected);
      if (selectedWorkouts.length === 0) {
        Alert.alert("Keine Workouts ausgewählt", "Bitte wähle mindestens ein Workout aus.");
        return;
      }
      
      setLoading(true);
      
      // Create entries for assigned_workouts table with specified date
      const workoutAssignments = selectedWorkouts.map(workout => ({
        user_id: selectedUser.id,
        workout_id: workout.id,
        assigned_date: selectedDate,  // Use selected date from calendar
        completed: false
      }));
      
      const { error } = await supabase
        .from("assigned_workouts")
        .insert(workoutAssignments);

      if (error) throw error;
      
      Alert.alert(
        "Workouts zugewiesen", 
        `${selectedWorkouts.length} Workout(s) wurden ${selectedUser.full_name} für den ${new Date(selectedDate).toLocaleDateString()} erfolgreich zugewiesen.`
      );
      
      // Refresh user workouts for this date
      await fetchUserWorkouts(selectedUser.id);
    } catch (error) {
      console.error("Error assigning workouts:", error);
      Alert.alert("Fehler", "Workouts konnten nicht zugewiesen werden.");
    } finally {
      setLoading(false);
    }
  };

  const toggleWorkoutCompletion = async (workout: Workout) => {
    if (!selectedUser) return;
    
    try {
      setLoading(true);
      
      const newCompletionStatus = !workout.completed;
      
      const { error } = await supabase
        .from("assigned_workouts")
        .update({ completed: newCompletionStatus })
        .eq("user_id", selectedUser.id)
        .eq("workout_id", workout.id)
        .eq("assigned_date", selectedDate);
      
      if (error) throw error;
      
      // Also update workout_history if needed for completed workouts
      if (newCompletionStatus) {
        await supabase.from("workout_history").insert({
          user_id: selectedUser.id,
          workout_id: workout.id,
          start_time: new Date().toISOString(),
          end_time: new Date().toISOString(),
          status: 'completed'
        });
      }
      
      // Refresh user workouts
      await fetchUserWorkouts(selectedUser.id);
      
    } catch (error) {
      console.error("Error toggling workout completion:", error);
      Alert.alert("Fehler", "Status konnte nicht aktualisiert werden.");
    } finally {
      setLoading(false);
    }
  };

  const removeWorkout = async (workoutId: number) => {
    if (!selectedUser) return;
    
    try {
      setLoading(true);
      
      const { error } = await supabase
        .from("assigned_workouts")
        .delete()
        .eq("user_id", selectedUser.id)
        .eq("workout_id", workoutId)
        .eq("assigned_date", selectedDate);
      
      if (error) throw error;
      
      // Refresh user workouts
      await fetchUserWorkouts(selectedUser.id);
      
      Alert.alert("Workout entfernt", "Das Workout wurde erfolgreich entfernt.");
    } catch (error) {
      console.error("Error removing workout:", error);
      Alert.alert("Fehler", "Workout konnte nicht entfernt werden.");
    } finally {
      setLoading(false);
    }
  };

  const handleDateSelect = (day: DateData) => {
    const dateString = day.dateString;
    setSelectedDate(dateString);
    
    // Update marked dates to show selection
    const updatedMarkedDates = {...calendarMarkedDates};
    
    // Remove old selection
    Object.keys(updatedMarkedDates).forEach(date => {
      if (updatedMarkedDates[date].selected) {
        if (updatedMarkedDates[date].marked) {
          updatedMarkedDates[date] = {
            ...updatedMarkedDates[date],
            selected: false,
          };
        } else {
          delete updatedMarkedDates[date];
        }
      }
    });
    
    // Add new selection
    if (updatedMarkedDates[dateString]) {
      updatedMarkedDates[dateString] = {
        ...updatedMarkedDates[dateString],
        selected: true,
        selectedColor: 'rgba(107, 140, 255, 0.15)',
      };
    } else {
      updatedMarkedDates[dateString] = {
        selected: true,
        selectedColor: 'rgba(107, 140, 255, 0.15)',
      };
    }
    
    setCalendarMarkedDates(updatedMarkedDates);
    
    // Update workouts for selected date
    setUserWorkouts(dateWorkouts[dateString] || []);
    
    // Update available workouts for selected date
    const assignedIdsForSelectedDate = new Set(
      (dateWorkouts[dateString] || []).map(w => w.id)
    );
    
    // Filter out workouts that are already assigned for this date
    setAvailableWorkouts(prev => 
      prev
        .filter(w => !assignedIdsForSelectedDate.has(w.id))
        .map(w => ({...w, selected: false}))
    );
  };

  // Render assigned workout item with completion toggle and remove option
  const renderAssignedWorkoutItem = ({ item }: { item: Workout }) => (
    <View style={styles.assignedWorkout}>
      <TouchableOpacity 
        style={[
          styles.completionCheckbox, 
          item.completed && styles.completionCheckboxChecked
        ]}
        onPress={() => toggleWorkoutCompletion(item)}
      >
        {item.completed && <Ionicons name="checkmark" size={16} color="#fff" />}
      </TouchableOpacity>
      
      <View style={styles.assignedWorkoutContent}>
        <Text style={[
          styles.assignedWorkoutName,
          item.completed && styles.completedWorkoutText
        ]}>
          {item.name}
        </Text>
        <View style={styles.assignedWorkoutDetails}>
          <View style={styles.assignedDetailItem}>
            <Ionicons name="time-outline" size={16} color="#666" />
            <Text style={styles.assignedDetailText}>{item.duration} Min</Text>
          </View>
          <View
            style={[
              styles.assignedDifficultyBadge,
              { backgroundColor: getDifficultyColor(item.difficulty) },
            ]}
          >
            <Text style={styles.assignedDifficultyText}>{item.difficulty}</Text>
          </View>
        </View>
      </View>
      
      <TouchableOpacity
        style={styles.removeButton}
        onPress={() => removeWorkout(item.id)}
      >
        <Ionicons name="trash-outline" size={20} color="#FF4B4B" />
      </TouchableOpacity>
    </View>
  );

  // Render workout item for selection
  const renderSelectableWorkoutItem = ({ item }: { item: Workout }) => (
    <TouchableOpacity
      style={[
        styles.selectableWorkout,
        item.selected && styles.selectedWorkout
      ]}
      onPress={() => toggleWorkoutSelection(item.id)}
      activeOpacity={0.7}
    >
      <View style={styles.selectableWorkoutContent}>
        <Text style={styles.selectableWorkoutName}>{item.name}</Text>
        <View style={styles.selectableWorkoutDetails}>
          <View style={styles.selectableDetailItem}>
            <Ionicons name="time-outline" size={16} color="#666" />
            <Text style={styles.selectableDetailText}>{item.duration} Min</Text>
          </View>
          <View
            style={[
              styles.selectableDifficultyBadge,
              { backgroundColor: getDifficultyColor(item.difficulty) },
            ]}
          >
            <Text style={styles.selectableDifficultyText}>{item.difficulty}</Text>
          </View>
        </View>
      </View>
      <View style={[styles.checkBox, item.selected && styles.checkBoxSelected]}>
        {item.selected && <Ionicons name="checkmark" size={16} color="#fff" />}
      </View>
    </TouchableOpacity>
  );

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty.toLowerCase()) {
      case "beginner":
        return "#92A3FD";
      case "intermediate":
        return "#FFA07A";
      case "advanced":
        return "#FF4B4B";
      default:
        return "#92A3FD";
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen
        options={{
          title: "Admin Dashboard",
          headerStyle: {
            backgroundColor: "#fff",
          },
          headerShadowVisible: false,
          headerLeft: () => (
            <TouchableOpacity
              style={styles.headerBackButton}
              onPress={() => router.back()}
            >
              <Ionicons name="chevron-back" size={24} color="#6B8CFF" />
            </TouchableOpacity>
          ),
          // Add header right button when on workouts tab for quick access
          headerRight: () => (
            activeTab === "workouts" ? (
              <TouchableOpacity
                style={styles.headerActionButton}
                onPress={() => {
                  // This event will be caught by the WorkoutManager component
                  const event = new CustomEvent('createWorkout');
                  document.dispatchEvent(event);
                }}
              >
                <Ionicons name="add-circle" size={28} color="#92A3FD" />
              </TouchableOpacity>
            ) : null
          ),
        }}
      />

      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[
            styles.tabButton,
            activeTab === "users" && styles.activeTabButton,
          ]}
          onPress={() => setActiveTab("users")}
        >
          <Ionicons
            name="people"
            size={24}
            color={activeTab === "users" ? "#6B8CFF" : "#8F9BB3"}
          />
          <Text
            style={[
              styles.tabText,
              activeTab === "users" && styles.activeTabText,
            ]}
          >
            Users
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.tabButton,
            activeTab === "workouts" && styles.activeTabButton,
          ]}
          onPress={() => setActiveTab("workouts")}
        >
          <Ionicons
            name="fitness"
            size={24}
            color={activeTab === "workouts" ? "#6B8CFF" : "#8F9BB3"}
          />
          <Text
            style={[
              styles.tabText,
              activeTab === "workouts" && styles.activeTabText,
            ]}
          >
            Workouts
          </Text>
        </TouchableOpacity>
      </View>

      {/* Content Area - Show appropriate tab based on selection */}
      {activeTab === "users" ? (
        <UserTab 
          setSelectedUser={setSelectedUser} 
          setModalVisible={setModalVisible} 
        />
      ) : (
        <WorkoutTab refreshWorkouts={fetchWorkouts} />
      )}

      {/* Enhanced Modal for workout assignment with calendar */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.enhancedModalContainer}>
            <View style={styles.modalHeader}>
              <View>
                <Text style={styles.modalTitle}>
                  Trainingsplan für {selectedUser?.full_name || 'Benutzer'}
                </Text>
                <Text style={styles.modalSubtitle}>
                  Plane und tracke die Workouts
                </Text>
              </View>
              <TouchableOpacity
                onPress={() => setModalVisible(false)}
                style={styles.closeButton}
              >
                <Ionicons name="close" size={24} color="#333" />
              </TouchableOpacity>
            </View>

            {/* User Stats Section */}
            <View style={styles.statsContainer}>
              <View style={styles.statItem}>
                <Text style={styles.statValue}>{userStats.totalAssigned}</Text>
                <Text style={styles.statLabel}>Zugewiesen</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statValue}>{userStats.completedCount}</Text>
                <Text style={styles.statLabel}>Absolviert</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statValue}>{userStats.streakDays}</Text>
                <Text style={styles.statLabel}>Streak</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statValue}>{userStats.completionRate}%</Text>
                <Text style={styles.statLabel}>Quote</Text>
              </View>
            </View>

            {/* Calendar for date selection */}
            <View style={styles.calendarContainer}>
              <Calendar
                theme={{
                  backgroundColor: '#ffffff',
                  calendarBackground: '#ffffff',
                  textSectionTitleColor: '#b6c1cd',
                  selectedDayBackgroundColor: '#6B8CFF',
                  selectedDayTextColor: '#ffffff',
                  todayTextColor: '#6B8CFF',
                  dayTextColor: '#2d4150',
                  textDisabledColor: '#d9e1e8',
                  dotColor: '#6B8CFF',
                  selectedDotColor: '#ffffff',
                  arrowColor: '#6B8CFF',
                  monthTextColor: '#2d4150',
                  indicatorColor: '#6B8CFF',
                  textDayFontFamily: 'System',
                  textMonthFontFamily: 'System',
                  textDayHeaderFontFamily: 'System',
                  textDayFontWeight: '300',
                  textMonthFontWeight: 'bold',
                  textDayHeaderFontWeight: '300',
                  textDayFontSize: 16,
                  textMonthFontSize: 16,
                  textDayHeaderFontSize: 13
                }}
                markedDates={calendarMarkedDates}
                onDayPress={handleDateSelect}
                enableSwipeMonths={true}
                hideExtraDays={false}
              />
            </View>

            {/* Selected Date Header */}
            <View style={styles.selectedDateHeader}>
              <Ionicons name="calendar" size={20} color="#6B8CFF" />
              <Text style={styles.selectedDateText}>
                {new Date(selectedDate).toLocaleDateString('de-DE', {
                  weekday: 'long',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}
              </Text>
            </View>

            {/* Assigned Workouts for selected date */}
            <View style={styles.modalSection}>
              <Text style={styles.sectionTitle}>Zugewiesene Workouts</Text>
              {userWorkouts.length > 0 ? (
                <FlatList
                  data={userWorkouts}
                  renderItem={renderAssignedWorkoutItem}
                  keyExtractor={(item) => `${item.id}-${selectedDate}`}
                  style={styles.assignedList}
                  showsVerticalScrollIndicator={false}
                />
              ) : (
                <View style={styles.noAssignedWorkouts}>
                  <Ionicons name="fitness-outline" size={40} color="#C5CEE0" />
                  <Text style={styles.noWorkoutsText}>
                    Keine Workouts für diesen Tag zugewiesen
                  </Text>
                </View>
              )}
            </View>

            {/* Available workouts for assignment */}
            <View style={styles.modalSection}>
              <Text style={styles.sectionTitle}>Verfügbare Workouts</Text>
              {availableWorkouts.length > 0 ? (
                <FlatList
                  data={availableWorkouts}
                  renderItem={renderSelectableWorkoutItem}
                  keyExtractor={(item) => item.id.toString()}
                  style={styles.availableList}
                  showsVerticalScrollIndicator={false}
                />
              ) : (
                <View style={styles.noAvailableWorkouts}>
                  <Text style={styles.noWorkoutsText}>
                    Alle Workouts wurden diesem Tag bereits zugewiesen
                  </Text>
                </View>
              )}
            </View>

            {/* Action buttons */}
            <View style={styles.modalActions}>
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={() => setModalVisible(false)}
              >
                <Text style={styles.cancelButtonText}>Schließen</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.assignButton,
                  !availableWorkouts.some(w => w.selected) && styles.disabledButton
                ]}
                onPress={assignWorkouts}
                disabled={!availableWorkouts.some(w => w.selected)}
              >
                <Text style={styles.assignButtonText}>Zuweisen</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F5F7FA",
  },
  headerBackButton: {
    padding: 8,
  },
  headerActionButton: {
    padding: 8,
    marginRight: 8,
  },
  tabContainer: {
    flexDirection: "row",
    backgroundColor: "#FFFFFF",
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: "#EDF1F7",
  },
  tabButton: {
    flex: 1,
    alignItems: "center",
    paddingVertical: 12,
    flexDirection: "row",
    justifyContent: "center",
  },
  activeTabButton: {
    borderBottomWidth: 2,
    borderBottomColor: "#6B8CFF",
  },
  tabText: {
    fontSize: 16,
    fontWeight: "500",
    color: "#8F9BB3",
    marginLeft: 8,
  },
  activeTabText: {
    color: "#6B8CFF",
  },
  modalOverlay: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  enhancedModalContainer: {
    width: width * 0.95,
    maxHeight: '90%', 
    backgroundColor: "#fff",
    borderRadius: 16,
    padding: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 10,
    elevation: 10,
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#2E3A59",
  },
  closeButton: {
    padding: 8,
  },
  modalSubtitle: {
    fontSize: 14,
    color: "#8F9BB3",
    marginTop: 4,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
    backgroundColor: '#f5f7fa',
    borderRadius: 12,
    padding: 12,
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#6B8CFF',
  },
  statLabel: {
    fontSize: 12,
    color: '#8F9BB3',
    marginTop: 4,
  },
  calendarContainer: {
    marginBottom: 16,
    borderRadius: 12,
    overflow: 'hidden',
    backgroundColor: '#fff',
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  selectedDateHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    paddingHorizontal: 4,
    paddingVertical: 8,
    backgroundColor: '#f5f7fa',
    borderRadius: 8,
  },
  selectedDateText: {
    marginLeft: 8,
    fontSize: 16,
    color: "#2E3A59",
    fontWeight: '500',
  },
  modalSection: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#2E3A59",
    marginBottom: 8,
  },
  assignedList: {
    maxHeight: 200,
  },
  noAssignedWorkouts: {
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  noWorkoutsText: {
    marginTop: 10,
    fontSize: 14,
    color: "#8F9BB3",
  },
  availableList: {
    maxHeight: 200,
  },
  noAvailableWorkouts: {
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  modalActions: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  cancelButton: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: "#E4E9F2",
    borderRadius: 12,
  },
  cancelButtonText: {
    fontSize: 14,
    fontWeight: "500",
    color: "#6B8CFF",
  },
  assignButton: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: "#6B8CFF",
    borderRadius: 12,
  },
  assignButtonText: {
    fontSize: 14,
    fontWeight: "500",
    color: "#fff",
  },
  disabledButton: {
    backgroundColor: '#C5CEE0',
  },
  selectableWorkout: {
    flexDirection: "row",
    alignItems: "center",
    padding: 12,
    borderRadius: 12,
    backgroundColor: "#fff",
    marginBottom: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  selectedWorkout: {
    backgroundColor: "#E4E9F2",
  },
  selectableWorkoutContent: {
    flex: 1,
  },
  selectableWorkoutName: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#2E3A59",
  },
  selectableWorkoutDetails: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 4,
  },
  selectableDetailItem: {
    flexDirection: "row",
    alignItems: "center",
    marginRight: 8,
  },
  selectableDetailText: {
    fontSize: 14,
    color: "#666",
    marginLeft: 4,
  },
  selectableDifficultyBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  selectableDifficultyText: {
    fontSize: 12,
    fontWeight: "500",
    color: "#fff",
  },
  checkBox: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: "#8F9BB3",
    justifyContent: "center",
    alignItems: "center",
  },
  checkBoxSelected: {
    backgroundColor: "#6B8CFF",
    borderColor: "#6B8CFF",
  },
  assignedWorkout: {
    flexDirection: "row",
    alignItems: "center",
    padding: 12,
    borderRadius: 12,
    backgroundColor: "#fff",
    marginBottom: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  assignedWorkoutContent: {
    flex: 1,
  },
  assignedWorkoutName: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#2E3A59",
  },
  assignedWorkoutDetails: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 4,
  },
  assignedDetailItem: {
    flexDirection: "row",
    alignItems: "center",
    marginRight: 8,
  },
  assignedDetailText: {
    fontSize: 14,
    color: "#666",
    marginLeft: 4,
  },
  assignedDifficultyBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  assignedDifficultyText: {
    fontSize: 12,
    fontWeight: "500",
    color: "#fff",
  },
  removeButton: {
    padding: 8,
  },
  completionCheckbox: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#8F9BB3',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  completionCheckboxChecked: {
    backgroundColor: '#4CAF50',
    borderColor: '#4CAF50',
  },
  completedWorkoutText: {
    color: '#8F9BB3',
    textDecorationLine: 'line-through',
  },
});

export default AdminPage;

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Switch,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import { useSettings } from '../context/SettingsContext';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { appConfig, countryInfo } from '../config/app.config';
import CountryFlag from "react-native-country-flag";

type LanguageOption = {
  code: 'en' | 'ro';
};

const Settings = () => {
  const { hapticEnabled, toggleHaptic, language, setLanguage } = useSettings();
  const router = useRouter();

  const languages: LanguageOption[] = [
    { code: 'en' },
    { code: 'ro' },
  ];

  const translations = {
    en: {
      settings: 'Settings',
      language: 'Language',
      feedback: 'Feedback',
      hapticFeedback: 'Haptic Feedback',
      hapticDescription: 'Feel a subtle vibration when interacting with buttons',
      about: 'About',
      version: 'Version',
      back: 'Back',
    },
    ro: {
      settings: 'Set<PERSON><PERSON>',
      language: 'Limbă',
      feedback: 'Feedback',
      hapticFeedback: 'Feedback Haptic',
      hapticDescription: 'Simțiți o vibrație subtilă când interacționați cu butoanele',
      about: 'Despre',
      version: 'Versiune',
      back: 'Înapoi',
    }
  };

  const t = translations[language];

  // Flag Component für einheitliches Styling
  const FlagIcon = ({ countryCode, size = 18 }: { countryCode: string, size?: number }) => (
    <View style={styles.flagContainer}>
      <CountryFlag isoCode={countryCode} size={size} />
    </View>
  );

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <Ionicons name="chevron-back" size={24} color="#000" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{t.settings}</Text>
        <View style={{ width: 24 }} />
      </View>

      {/* Language Section */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <FlagIcon countryCode={countryInfo[language].code} />
          <Text style={styles.sectionTitle}>{t.language}</Text>
        </View>
        <View style={styles.languageContainer}>
          {languages.map((lang) => (
            <TouchableOpacity
              key={lang.code}
              style={[
                styles.languageOption,
                language === lang.code && styles.languageOptionActive
              ]}
              onPress={() => setLanguage(lang.code)}
            >
              <View style={styles.languageContent}>
                <FlagIcon countryCode={countryInfo[lang.code].code} />
                <Text style={styles.languageName}>{countryInfo[lang.code].name}</Text>
              </View>
              {language === lang.code && (
                <View style={styles.checkmark}>
                  <Ionicons name="checkmark" size={22} color="#007AFF" />
                </View>
              )}
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Feedback Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>{t.feedback}</Text>
        <View style={styles.settingItem}>
          <View>
            <Text style={styles.settingTitle}>{t.hapticFeedback}</Text>
            <Text style={styles.settingDescription}>
              {t.hapticDescription}
            </Text>
          </View>
          <Switch
            value={hapticEnabled}
            onValueChange={toggleHaptic}
            trackColor={{ false: '#D1D1D6', true: '#6B8CFF' }}
            thumbColor={'#FFFFFF'}
          />
        </View>
      </View>

      {/* About Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>{t.about}</Text>
        <TouchableOpacity style={styles.settingItem}>
          <Text style={styles.settingTitle}>{t.version}</Text>
          <Text style={styles.versionText}>
            {`${appConfig.version} (${appConfig.buildNumber})`}
          </Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F7FF',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  section: {
    backgroundColor: '#FFFFFF',
    marginTop: 16,
    marginHorizontal: 16,
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  sectionTitle: {
    fontSize: 14,
    color: '#666',
    paddingHorizontal: 16,
    paddingVertical: 12,
    textTransform: 'uppercase',
    letterSpacing: 1,
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  settingTitle: {
    fontSize: 16,
    color: '#000',
    marginBottom: 4,
  },
  settingDescription: {
    fontSize: 14,
    color: '#666',
    maxWidth: '80%',
  },
  versionText: {
    fontSize: 16,
    color: '#666',
  },
  languageContainer: {
    paddingBottom: 8,
  },
  languageOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 12,
    paddingHorizontal: 16,
    backgroundColor: '#FFFFFF',
  },
  languageOptionActive: {
    backgroundColor: '#F8F8F8',
  },
  languageContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  languageName: {
    fontSize: 16,
    color: '#333',
  },
  checkmark: {
    width: 22,
    height: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  currentFlag: {
    fontSize: 24,
    marginRight: 12,
  },
  optionFlag: {
    fontSize: 24,
    marginRight: 12,
  },
  flagContainer: {
    width: 32,
    height: 24,
    marginRight: 12,
    borderRadius: 4,
    overflow: 'hidden',
  },
});

export default Settings;

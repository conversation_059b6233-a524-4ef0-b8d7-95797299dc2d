import React, { ReactNode } from 'react';
import { View, StyleSheet, StatusBar, Platform } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

interface SafeAreaLayoutProps {
  children: ReactNode;
  style?: any;
  noBottomInset?: boolean;
  backgroundColor?: string;
}

/**
 * A layout component that automatically handles safe area insets for devices with notches,
 * dynamic islands, or other system UI elements that may overlap with content.
 */
export default function SafeAreaLayout({
  children,
  style,
  noBottomInset = false,
  backgroundColor = '#fff',
}: SafeAreaLayoutProps) {
  const insets = useSafeAreaInsets();
  
  return (
    <View
      style={[
        styles.container,
        {
          paddingTop: insets.top > 0 ? insets.top : Platform.OS === 'android' ? StatusBar.currentHeight || 0 : 0,
          paddingBottom: noBottomInset ? 0 : insets.bottom,
          paddingLeft: insets.left,
          paddingRight: insets.right,
          backgroundColor,
        },
        style,
      ]}
    >
      {children}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

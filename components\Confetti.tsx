import React, { useRef, useImperativeHandle, forwardRef } from 'react';
import { View, Platform } from 'react-native';
import LottieView from 'lottie-react-native';

export interface ConfettiRef {
  play: () => void;
  pause: () => void;
  reset: () => void;
}

const Confetti = forwardRef<ConfettiRef>((props, ref) => {
  const animation = useRef<LottieView>(null);

  useImperativeHandle(ref, () => ({
    play: () => {
      animation.current?.play();
    },
    pause: () => {
      animation.current?.pause();
    },
    reset: () => {
      animation.current?.reset();
    },
  }));

  return (
    <View style={{ position: 'absolute', top: 0, left: 0, right: 0, bottom: 0 }}>
      <LottieView
        ref={animation}
        source={require('../assets/animations/confetti.json')}
        autoPlay={false}
        loop={false}
        style={{ flex: 1 }}
      />
    </View>
  );
});

export default Confetti;

// src/components/styles.ts
import { StyleSheet, Dimensions, Platform } from "react-native";

const { width, height } = Dimensions.get('window');
const isIOS = Platform.OS === 'ios';

const styles = StyleSheet.create({
  centeredView: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0,0,0,0.6)",
  },
  // WorkoutManager Styles
  createButton: {
    position: "absolute",
    bottom: 24,
    right: 24,
    borderRadius: 30,
    overflow: "hidden",
    elevation: 10,
    shadowColor: "#92A3FD",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 10,
  },
  createButtonGradient: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 16,
    paddingHorizontal: 24,
  },
  createButtonText: {
    color: "#fff",
    fontWeight: "700",
    marginLeft: 10,
    fontSize: 16,
  },
  // WorkoutCard Styles
  workoutCard: {
    marginBottom: 24,
    borderRadius: 20,
    elevation: 8,
    shadowColor: "#92A3FD",
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.15,
    shadowRadius: 16,
    overflow: 'hidden',
  },
  cardGradient: {
    borderRadius: 20,
    overflow: "hidden",
  },
  workoutImage: {
    width: "100%",
    height: 200,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  contentContainer: {
    padding: 20,
  },
  workoutHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  workoutName: {
    fontSize: 20,
    fontWeight: "800",
    color: "#fff",
    flex: 1,
    marginRight: 10,
  },
  workoutBadges: {
    flexDirection: "row",
  },
  difficultyBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  difficultyText: {
    color: "#fff",
    fontSize: 13,
    fontWeight: "700",
  },
  workoutDescription: {
    color: "rgba(255, 255, 255, 0.9)",
    marginBottom: 16,
    fontSize: 15,
    lineHeight: 22,
  },
  workoutDetails: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 12,
  },
  detailItem: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
  },
  detailText: {
    color: "#fff",
    marginLeft: 6,
    fontSize: 14,
    fontWeight: "600",
  },
  // WorkoutForm & Modal Styles
  modalView: {
    width: width * 0.92,
    maxHeight: "88%",
    backgroundColor: "white",
    borderRadius: 24,
    overflow: 'hidden',
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.15,
    shadowRadius: 20,
    elevation: 15,
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 18,
    borderBottomWidth: isIOS ? 0.5 : 1,
    borderBottomColor: isIOS ? 'rgba(0,0,0,0.1)' : '#EAEAEA',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: "700",
    color: "#1D1617",
  },
  closeButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(0,0,0,0.05)',
  },
  formScrollView: {
    paddingHorizontal: 20,
  },
  formScrollViewContent: {
    paddingTop: 15,
    paddingBottom: 30,
  },
  formSection: {
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "700",
    marginBottom: 16,
    color: "#1D1617",
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#EAEAEA',
    borderRadius: 16,
    padding: 16,
    fontSize: 16,
    marginBottom: 14,
    backgroundColor: '#FAFAFA',
    color: '#333',
  },
  textAreaInput: {
    height: 120,
    textAlignVertical: "top",
    marginBottom: 14,
  },
  dropdownButton: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    borderWidth: 1,
    borderColor: '#EAEAEA',
    borderRadius: 16,
    padding: 16,
    marginBottom: 14,
    backgroundColor: '#FAFAFA',
  },
  dropdownButtonText: {
    fontSize: 16,
    color: "#333",
  },
  dropdownList: {
    borderWidth: 1,
    borderColor: '#EAEAEA',
    borderRadius: 16,
    marginBottom: 14,
    backgroundColor: "#fff",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    overflow: 'hidden',
  },
  dropdownItem: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F5F5F5',
  },
  dropdownItemText: {
    fontSize: 16,
    color: "#333",
  },
  difficultySelector: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 14,
  },
  difficultyOption: {
    flex: 1,
    alignItems: "center",
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: '#EAEAEA',
    borderRadius: 16,
    marginHorizontal: 4,
    backgroundColor: '#FAFAFA',
  },
  difficultyOptionSelected: {
    borderColor: "#92A3FD",
    backgroundColor: "rgba(146, 163, 253, 0.08)",
  },
  difficultyOptionText: {
    fontSize: 15,
    color: "#666",
  },
  difficultyOptionTextSelected: {
    fontWeight: "700",
    color: "#92A3FD",
  },
  imagePicker: {
    borderWidth: 1,
    borderColor: '#EAEAEA',
    borderRadius: 20,
    padding: 0,
    alignItems: "center",
    justifyContent: "center",
    height: 200,
    marginBottom: 14,
    overflow: 'hidden',
    backgroundColor: '#F8F8F8',
  },
  previewImage: {
    width: "100%",
    height: "100%",
    borderRadius: 20,
  },
  imagePickerPlaceholder: {
    alignItems: "center",
    justifyContent: "center",
    width: '100%',
    height: '100%',
  },
  imagePickerText: {
    marginTop: 12,
    fontSize: 16,
    color: "#92A3FD",
    fontWeight: '600',
  },
  // Video picker styles with modern look
  videoPicker: {
    borderWidth: 1,
    borderColor: '#EAEAEA',
    borderRadius: 20,
    padding: 16,
    alignItems: "center",
    justifyContent: "center",
    height: 120,
    marginBottom: 14,
    backgroundColor: '#F8F8F8',
  },
  videoPickerSelected: {
    width: '100%',
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(146, 163, 253, 0.1)',
  },
  videoPickerSelectedText: {
    marginLeft: 10,
    fontSize: 16,
    color: "#92A3FD",
    fontWeight: "600",
  },
  videoPickerPlaceholder: {
    alignItems: "center",
    justifyContent: "center",
  },
  videoPickerText: {
    marginTop: 12,
    fontSize: 16,
    color: "#92A3FD",
    fontWeight: '600',
  },
  sectionHeaderRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  exerciseButtonsRow: {
    flexDirection: "row",
    alignItems: "center",
  },
  addExerciseButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(146, 163, 253, 0.1)",
    paddingHorizontal: 14,
    paddingVertical: 10,
    borderRadius: 12,
  },
  addExerciseText: {
    marginLeft: 6,
    fontSize: 15,
    color: "#92A3FD",
    fontWeight: '600',
  },
  exerciseItem: {
    backgroundColor: "rgba(146, 163, 253, 0.05)",
    borderRadius: 16,
    padding: 18,
    marginBottom: 12,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    borderWidth: 1,
    borderColor: "rgba(146, 163, 253, 0.2)",
  },
  noExercisesContainer: {
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 40,
    backgroundColor: '#F8F8F8',
    borderRadius: 16,
  },
  modalActions: {
    flexDirection: "row",
    justifyContent: "space-between",
    padding: 20,
    borderTopWidth: isIOS ? 0.5 : 1,
    borderTopColor: isIOS ? 'rgba(0,0,0,0.1)' : '#EAEAEA',
  },
  cancelButton: {
    paddingVertical: 14,
    paddingHorizontal: 24,
    borderRadius: 16,
    backgroundColor: '#F0F0F0',
    minWidth: 120,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#555",
  },
  saveButton: {
    paddingVertical: 14,
    paddingHorizontal: 28,
    borderRadius: 16,
    backgroundColor: "#92A3FD",
    minWidth: 120,
    alignItems: 'center',
    shadowColor: "#92A3FD",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 5,
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: "700",
    color: "#fff",
  },
  disabledButton: {
    backgroundColor: "#C5CEE0",
    shadowOpacity: 0,
    elevation: 0,
  },
  // Exercise picker styles with premium design
  searchInput: {
    backgroundColor: "#F5F6FA",
    borderRadius: 16,
    padding: 16,
    marginHorizontal: 20,
    marginBottom: 16,
    marginTop: 10,
    fontSize: 16,
    color: '#333',
    borderWidth: 1,
    borderColor: '#EAEAEA',
  },
  exerciseList: {
    maxHeight: 400,
    width: "100%",
    paddingHorizontal: 20,
  },
  exerciseListItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
    borderRadius: 16,
    marginBottom: 8,
    backgroundColor: 'rgba(146, 163, 253, 0.04)',
  },
  exerciseListItemContent: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  exerciseThumb: {
    width: 60,
    height: 60,
    borderRadius: 12,
    marginRight: 16,
  },
  exerciseThumbPlaceholder: {
    backgroundColor: "rgba(146, 163, 253, 0.15)",
    justifyContent: "center",
    alignItems: "center",
  },
  exerciseListItemText: {
    flex: 1,
  },
  exerciseListItemTitle: {
    fontSize: 17,
    fontWeight: "700",
    color: "#1D1617",
    marginBottom: 6,
  },
  exerciseListItemDetails: {
    fontSize: 15,
    color: "#666",
  },
  createNewExerciseButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#92A3FD",
    borderRadius: 16,
    padding: 16,
    margin: 20,
    shadowColor: "#92A3FD",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 5,
  },
  createNewExerciseButtonText: {
    color: "white",
    fontWeight: "700",
    fontSize: 16,
    marginLeft: 10,
  },
  loadingContainer: {
    alignItems: "center",
    justifyContent: "center",
    padding: 40,
  },
  loadingText: {
    marginTop: 12,
    color: "#666",
    fontSize: 16,
  },
  noExercisesText: {
    marginTop: 10,
    color: "#666",
    textAlign: "center",
    fontSize: 16,
  },
  // Delete Workout Section with improved UI
  deleteSection: {
    marginTop: 20,
    marginBottom: 30,
    padding: 24,
    borderWidth: 1,
    borderColor: "rgba(255, 75, 75, 0.2)",
    backgroundColor: "rgba(255, 75, 75, 0.05)",
    borderRadius: 16,
    alignItems: "center",
  },
  deleteSectionText: {
    fontSize: 16,
    color: "#FF4B4B",
    marginBottom: 16,
    textAlign: 'center',
    fontWeight: '500',
  },
  deleteButton: {
    backgroundColor: "#FF4B4B",
    paddingVertical: 14,
    paddingHorizontal: 24,
    borderRadius: 16,
    shadowColor: "#FF4B4B",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },
  deleteButtonText: {
    color: "#FFFFFF",
    fontWeight: "700",
    fontSize: 16,
  },
  // Custom Popup Styles with premium feel
  customPopupOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 9999,
  },
  customPopupContainer: {
    width: width * 0.85,
    backgroundColor: 'white',
    borderRadius: 24,
    padding: 24,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.25,
    shadowRadius: 16,
    elevation: 15,
  },
  customPopupTitle: {
    fontSize: 22,
    fontWeight: '800',
    marginBottom: 16,
    color: '#1D1617',
  },
  customPopupMessage: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 24,
  },
  customPopupButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginTop: 8,
  },
  customPopupCancelButton: {
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderRadius: 16,
    backgroundColor: '#F5F5F5',
    flex: 1,
    marginRight: 12,
    alignItems: 'center',
  },
  customPopupCancelButtonText: {
    fontSize: 16,
    fontWeight: '700',
    color: '#555',
  },
  customPopupDeleteButton: {
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderRadius: 16,
    backgroundColor: '#FF4B4B',
    flex: 1,
    marginLeft: 12,
    alignItems: 'center',
    shadowColor: "#FF4B4B",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },
  customPopupDeleteButtonText: {
    fontSize: 16,
    fontWeight: '700',
    color: '#fff',
  },
  // Enhanced Exercise Modal Styles
  exerciseModalFullscreen: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.6)',
  },
  exerciseModalGradientBackground: {
    flex: 1,
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  exerciseModalBlurOverlay: {
    flex: 1,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  exerciseModalContainer: {
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  exerciseModalCard: {
    width: width * 0.92,
    maxHeight: height * 0.85,
    backgroundColor: '#fff',
    borderRadius: 24,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.2,
    shadowRadius: 15,
    elevation: 10,
  },
  exerciseModalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 18,
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
  },
  exerciseModalTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#fff',
  },
  exerciseModalScrollView: {
    maxHeight: height * 0.6,
  },
  exerciseModalContent: {
    padding: 20,
    paddingBottom: 30,
  },
  exerciseModalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 20,
    paddingTop: 15,
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
  },
  exerciseModalCancelButton: {
    paddingVertical: 14,
    paddingHorizontal: 24,
    borderRadius: 16,
    backgroundColor: '#F0F0F0',
    minWidth: 120,
    alignItems: 'center',
  },
  exerciseModalCancelButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#555',
  },
  exerciseModalSaveButton: {
    borderRadius: 16,
    minWidth: 140,
    overflow: 'hidden',
  },
  exerciseModalSaveGradient: {
    paddingVertical: 14,
    paddingHorizontal: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  exerciseModalSaveButtonText: {
    fontSize: 16,
    fontWeight: '700',
    color: '#fff',
  },
  // Exercise form input styling
  inputWithIcon: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#EAEAEA',
    borderRadius: 16,
    backgroundColor: '#FAFAFA',
    marginBottom: 16,
    height: 56,
    paddingHorizontal: 16,
  },
  inputIcon: {
    marginRight: 12,
  },
  inputWithIconField: {
    flex: 1,
    fontSize: 16,
    color: '#333',
    height: '100%',
  },
  exerciseDetailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  sectionLabelText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1D1617',
    marginBottom: 8,
    marginTop: 8,
  },
  mediaPicker: {
    borderWidth: 1,
    borderColor: '#EAEAEA',
    borderRadius: 20,
    padding: 0,
    alignItems: 'center',
    justifyContent: 'center',
    height: 180,
    marginBottom: 20,
    overflow: 'hidden',
    backgroundColor: '#F8F8F8',
  },
  mediaPickerDisabled: {
    opacity: 0.7,
  },
  mediaPickerLoading: {
    width: '100%',
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
  },
  mediaPickerLoadingText: {
    marginTop: 12,
    color: '#666',
    fontSize: 14,
  },
  mediaPreview: {
    width: '100%',
    height: '100%',
    borderRadius: 20,
  },
  mediaPickerPlaceholder: {
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    height: '100%',
  },
  mediaPickerGradient: {
    width: '100%',
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  mediaPickerText: {
    marginTop: 12,
    fontSize: 16,
    color: '#92A3FD',
    fontWeight: '600',
  },
  infoCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(146, 163, 253, 0.08)',
    borderRadius: 16,
    padding: 16,
    marginBottom: 24,
    marginTop: 8,
  },
  infoCardText: {
    flex: 1,
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  // Picker styles
  gradientHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 18,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
  },
  gradientHeaderTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#fff',
  },
  exercisePickerContent: {
    paddingHorizontal: 20,
    paddingTop: 16,
  },
  searchBarContainer: {
    marginVertical: 16,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F5F6FA',
    borderRadius: 16,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: '#EAEAEA',
  },
  exerciseCountText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 12,
  },
  exerciseScrollList: {
    flex: 1,
  },
  exerciseCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    backgroundColor: '#fff',
    borderRadius: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#EAEAEA',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  exerciseDetailBadges: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 4,
  },
  exerciseDetailBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(146, 163, 253, 0.1)',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    marginRight: 8,
    marginTop: 4,
  },
  exerciseDetailBadgeText: {
    fontSize: 12,
    color: '#92A3FD',
    fontWeight: '600',
    marginLeft: 4,
  },
  clearSearchButton: {
    marginTop: 16,
    padding: 10,
  },
  clearSearchButtonText: {
    color: '#92A3FD',
    fontWeight: '600',
    fontSize: 15,
  },
  createButtonContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
  },
  createExerciseGradientButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 16,
    padding: 16,
    shadowColor: "#92A3FD",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 5,
  },
  createExerciseButtonText: {
    color: "white",
    fontWeight: "700",
    fontSize: 16,
    marginLeft: 10,
  },
});

export default styles;

import React from "react";
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { User } from "../../types";

interface UserCardProps {
  user: User;
  onPress: (user: User) => void;
}

export const UserCard = ({ user, onPress }: UserCardProps) => {
  return (
    <TouchableOpacity 
      style={styles.userCard}
      onPress={() => onPress(user)}
      activeOpacity={0.7}
    >
      <View style={styles.userAvatarContainer}>
        {user.profile_picture_url ? (
          <Image 
            source={{ uri: user.profile_picture_url }} 
            style={styles.userAvatar} 
          />
        ) : (
          <View style={[styles.userAvatar, styles.placeholderAvatar]}>
            <Text style={styles.userInitial}>
              {user.full_name?.charAt(0) || "U"}
            </Text>
          </View>
        )}
        {user.is_admin && (
          <View style={styles.adminBadge}>
            <Ionicons name="shield-checkmark" size={12} color="#fff" />
          </View>
        )}
        {user.subscription_type === 'premium' && (
          <View style={styles.premiumBadge}>
            <Ionicons name="star" size={10} color="#fff" />
          </View>
        )}
      </View>

      <View style={styles.userInfo}>
        <Text style={styles.userName}>{user.full_name}</Text>
        <Text style={styles.userEmail}>{user.email}</Text>
        <View style={styles.userMetaData}>
          {user.height && (
            <View style={styles.metaItem}>
              <Ionicons name="resize-outline" size={14} color="#666" />
              <Text style={styles.metaText}>{user.height} cm</Text>
            </View>
          )}
          {user.weight && (
            <View style={styles.metaItem}>
              <Ionicons name="barbell-outline" size={14} color="#666" />
              <Text style={styles.metaText}>{user.weight} kg</Text>
            </View>
          )}
          {user.birth_date && (
            <View style={styles.metaItem}>
              <Ionicons name="calendar-outline" size={14} color="#666" />
              <Text style={styles.metaText}>
                {new Date(user.birth_date).toLocaleDateString()}
              </Text>
            </View>
          )}
          <View style={[styles.subscriptionBadge, {
            backgroundColor: user.subscription_type === 'premium' 
              ? '#FFCB66' 
              : '#C5CEE0'
          }]}>
            <Text style={styles.subscriptionText}>
              {user.subscription_type === 'premium' ? 'Premium' : 'Basic'}
            </Text>
          </View>
        </View>
      </View>
      
      <View style={styles.userActions}>
        <Ionicons name="fitness" size={20} color="#92A3FD" />
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  userCard: {
    backgroundColor: "#FFFFFF",
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: "row",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  userAvatarContainer: {
    position: "relative",
  },
  userAvatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
  },
  placeholderAvatar: {
    backgroundColor: "#E4E9F2",
    justifyContent: "center",
    alignItems: "center",
  },
  userInitial: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#8F9BB3",
  },
  adminBadge: {
    position: "absolute",
    bottom: 0,
    right: 0,
    backgroundColor: "#6B8CFF",
    borderRadius: 10,
    width: 20,
    height: 20,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 2,
    borderColor: "#FFFFFF",
  },
  premiumBadge: {
    position: "absolute",
    top: 0,
    right: 0,
    backgroundColor: "#FFCB66",
    borderRadius: 10,
    width: 20,
    height: 20,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 2,
    borderColor: "#FFFFFF",
  },
  userInfo: {
    marginLeft: 16,
    flex: 1,
  },
  userName: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#2E3A59",
    marginBottom: 4,
  },
  userEmail: {
    fontSize: 14,
    color: "#8F9BB3",
    marginBottom: 6,
  },
  userMetaData: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 8,
  },
  metaItem: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#F7F9FC",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  metaText: {
    fontSize: 12,
    color: "#666",
    marginLeft: 4,
  },
  subscriptionBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginLeft: 8,
  },
  subscriptionText: {
    fontSize: 12,
    fontWeight: "500",
    color: "#fff",
  },
  userActions: {
    justifyContent: "center",
    alignItems: "center",
  },
});

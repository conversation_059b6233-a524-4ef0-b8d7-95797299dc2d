import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  StatusBar,
  Dimensions,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';

const { width } = Dimensions.get('window');

type WorkoutLevel = 'beginner' | 'intermediate' | 'advanced';
type WorkoutGoal = 'strength' | 'hypertrophy' | 'endurance' | 'weight-loss';
type WorkoutDuration = '20' | '30' | '45' | '60';

export default function WorkoutGenerator() {
  const insets = useSafeAreaInsets();
  const router = useRouter();
  
  const [selectedLevel, setSelectedLevel] = useState<WorkoutLevel>('beginner');
  const [selectedGoal, setSelectedGoal] = useState<WorkoutGoal>('strength');
  const [selectedDuration, setSelectedDuration] = useState<WorkoutDuration>('30');
  const [isGenerating, setIsGenerating] = useState(false);

  // Calculate the tab bar height to ensure proper spacing
  const TAB_BAR_HEIGHT = 65 + 10 + insets.bottom;

  const generateWorkout = () => {
    setIsGenerating(true);
    
    // Simulate API call
    setTimeout(() => {
      setIsGenerating(false);
      router.push('/ai/workout-result');
    }, 2000);
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" />
      
      {/* Header with back button */}
      <LinearGradient
        colors={['#FFFFFF', '#F8F9FF']}
        style={[
          styles.header,
          { paddingTop: Math.max(insets.top, 40) }
        ]}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <Ionicons name="arrow-back" size={24} color="#333" />
          </TouchableOpacity>
          
          <View
            from={{ opacity: 0, translateY: -10 }}
            animate={{ opacity: 1, translateY: 0 }}
          >
            <Text style={styles.headerTitle}>AI Workout Generator</Text>
          </View>
          
          <View style={{ width: 40 }} />
        </View>
      </LinearGradient>

      <ScrollView 
        style={styles.content}
        contentContainerStyle={[
          styles.scrollContent,
          { paddingBottom: TAB_BAR_HEIGHT + 100 } // Add padding for tab bar and generate button
        ]}
        showsVerticalScrollIndicator={false}
      >
        <View
          from={{ opacity: 0, translateY: 20 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={{ delay: 100 }}
        >
          <Text style={styles.sectionTitle}>Workout Level</Text>
          <View style={styles.optionsContainer}>
            {['beginner', 'intermediate', 'advanced'].map((level) => (
              <TouchableOpacity
                key={level}
                style={[
                  styles.optionButton,
                  selectedLevel === level && styles.selectedOption
                ]}
                onPress={() => setSelectedLevel(level as WorkoutLevel)}
              >
                <Text style={[
                  styles.optionText,
                  selectedLevel === level && styles.selectedOptionText
                ]}>
                  {level.charAt(0).toUpperCase() + level.slice(1)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <View
          from={{ opacity: 0, translateY: 20 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={{ delay: 200 }}
        >
          <Text style={styles.sectionTitle}>Training Goal</Text>
          <View style={styles.optionsContainer}>
            {[
              { value: 'strength', label: 'Strength' },
              { value: 'hypertrophy', label: 'Muscle Growth' },
              { value: 'endurance', label: 'Endurance' },
              { value: 'weight-loss', label: 'Weight Loss' }
            ].map((goal) => (
              <TouchableOpacity
                key={goal.value}
                style={[
                  styles.optionButton,
                  styles.goalOption,
                  selectedGoal === goal.value && styles.selectedOption
                ]}
                onPress={() => setSelectedGoal(goal.value as WorkoutGoal)}
              >
                <Text style={[
                  styles.optionText,
                  selectedGoal === goal.value && styles.selectedOptionText
                ]}>
                  {goal.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <View
          from={{ opacity: 0, translateY: 20 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={{ delay: 300 }}
        >
          <Text style={styles.sectionTitle}>Workout Duration</Text>
          <View style={styles.optionsContainer}>
            {['20', '30', '45', '60'].map((duration) => (
              <TouchableOpacity
                key={duration}
                style={[
                  styles.optionButton,
                  selectedDuration === duration && styles.selectedOption
                ]}
                onPress={() => setSelectedDuration(duration as WorkoutDuration)}
              >
                <Text style={[
                  styles.optionText,
                  selectedDuration === duration && styles.selectedOptionText
                ]}>
                  {duration} min
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </ScrollView>
      
      {/* Generate Button - positioned above tab bar */}
      <View style={[
        styles.generateButtonContainer, 
        { paddingBottom: insets.bottom + 10, bottom: TAB_BAR_HEIGHT - insets.bottom } // Position above tab bar
      ]}>
        <TouchableOpacity
          onPress={generateWorkout}
          disabled={isGenerating}
          style={styles.generateButton}
          activeOpacity={0.8}
        >
          <LinearGradient
            colors={['#FF6B9C', '#FF4B8C']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={styles.generateGradient}
          >
            {isGenerating ? (
              <View
                from={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ type: 'timing', duration: 200 }}
              >
                <Text style={styles.generateText}>Creating your perfect workout...</Text>
              </View>
            ) : (
              <View style={styles.generateContent}>
                <Ionicons name="fitness" size={20} color="#fff" style={{ marginRight: 8 }} />
                <Text style={styles.generateText}>Generate Workout</Text>
              </View>
            )}
          </LinearGradient>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FF',
  },
  header: {
    paddingBottom: 15,
    borderBottomRightRadius: 20,
    borderBottomLeftRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 10,
    elevation: 5,
    zIndex: 10,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(0,0,0,0.03)',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#333',
    textAlign: 'center',
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    padding: 20,
    paddingTop: 30,
    paddingBottom: 100,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
    marginTop: 10,
  },
  optionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 25,
  },
  optionButton: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    marginRight: 10,
    marginBottom: 10,
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.08)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
  },
  goalOption: {
    width: (width - 60) / 2,
  },
  selectedOption: {
    backgroundColor: '#ff558d15',
    borderColor: '#ff558d30',
  },
  optionText: {
    fontSize: 15,
    color: '#666',
    fontWeight: '500',
    textAlign: 'center',
  },
  selectedOptionText: {
    color: '#FF558D',
    fontWeight: '600',
  },
  generateButtonContainer: {
    position: 'absolute',
    left: 0,
    right: 0,
    paddingHorizontal: 20,
    paddingTop: 10,
    paddingBottom: 20,
    backgroundColor: '#F8F9FF',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.06,
    shadowRadius: 8,
    elevation: 10,
    zIndex: 10,
  },
  generateButton: {
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#FF6B9C',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 10,
    elevation: 8,
  },
  generateGradient: {
    paddingVertical: 18,
    alignItems: 'center',
    justifyContent: 'center',
  },
  generateText: {
    fontSize: 18,
    fontWeight: '600',
    color: 'white',
  },
  generateContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
});

import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Image,
  ActivityIndicator,
  TextInput,
  Alert,
} from "react-native";
import { router } from "expo-router";
import { supabase } from "../../lib/supabase";
import { Ionicons } from "@expo/vector-icons";

// Types
interface User {
  id: string;
  email: string;
  full_name: string;
  profile_picture_url: string | null;
  is_admin: boolean;
  created_at: string;
  last_sign_in_at: string;
  height?: number;
  weight?: number;
  birth_date?: string | null;
  subscription_type: 'basic' | 'premium' | null;
}

interface UserTabProps {
  setSelectedUser: (user: User) => void;
  setModalVisible: (visible: boolean) => void;
}

const UserTab: React.FC<UserTabProps> = ({ setSelectedUser, setModalVisible }) => {
  const [activeUserType, setActiveUserType] = useState<'all' | 'basic' | 'premium'>('all');
  const [users, setUsers] = useState<User[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    fetchUsers();
  }, []);

  useEffect(() => {
    if (users.length > 0) {
      filterUsersByType();
    }
  }, [activeUserType, users, searchQuery]);

  const filterUsersByType = () => {
    let filtered = [...users];
    
    // Filter by subscription type
    if (activeUserType !== 'all') {
      filtered = filtered.filter(user => user.subscription_type === activeUserType);
    }
    
    // Filter by search query if it exists
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(user => 
        user.full_name?.toLowerCase().includes(query) || 
        user.email?.toLowerCase().includes(query)
      );
    }
    
    setFilteredUsers(filtered);
  };

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from("users")
        .select("*")
        .order("created_at", { ascending: false });

      if (error) throw error;
      setUsers(data || []);
      setFilteredUsers(data || []);
    } catch (error) {
      console.error("Error fetching users:", error);
      Alert.alert("Error", "Failed to load users");
    } finally {
      setLoading(false);
    }
  };

  const handleUserPress = (user: User) => {
    router.push({
      pathname: `/user/[id]`,
      params: { id: user.id }
    } as any);
  };

  const handleOpenWorkoutModal = (user: User) => {
    setSelectedUser(user);
    setModalVisible(true);
  };

  // Render a user item
  const renderUserItem = ({ item }: { item: User }) => (
    <TouchableOpacity 
      style={styles.userCard}
      onPress={() => handleUserPress(item)}
      activeOpacity={0.7}
    >
      <View style={styles.userAvatarContainer}>
        {item.profile_picture_url ? (
          <Image 
            source={{ uri: item.profile_picture_url }} 
            style={styles.userAvatar} 
          />
        ) : (
          <View style={[styles.userAvatar, styles.placeholderAvatar]}>
            <Text style={styles.userInitial}>
              {item.full_name?.charAt(0) || "U"}
            </Text>
          </View>
        )}
        {item.is_admin && (
          <View style={styles.adminBadge}>
            <Ionicons name="shield-checkmark" size={12} color="#fff" />
          </View>
        )}
        {item.subscription_type === 'premium' && (
          <View style={styles.premiumBadge}>
            <Ionicons name="star" size={10} color="#fff" />
          </View>
        )}
      </View>

      <View style={styles.userInfo}>
        <Text style={styles.userName}>{item.full_name}</Text>
        <Text style={styles.userEmail}>{item.email}</Text>
        <View style={styles.userMetaData}>
          {item.height && (
            <View style={styles.metaItem}>
              <Ionicons name="resize-outline" size={14} color="#666" />
              <Text style={styles.metaText}>{item.height} cm</Text>
            </View>
          )}
          {item.weight && (
            <View style={styles.metaItem}>
              <Ionicons name="barbell-outline" size={14} color="#666" />
              <Text style={styles.metaText}>{item.weight} kg</Text>
            </View>
          )}
          {item.birth_date && (
            <View style={styles.metaItem}>
              <Ionicons name="calendar-outline" size={14} color="#666" />
              <Text style={styles.metaText}>
                {new Date(item.birth_date).toLocaleDateString()}
              </Text>
            </View>
          )}
          <View style={[styles.subscriptionBadge, {
            backgroundColor: item.subscription_type === 'premium' 
              ? '#FFCB66' 
              : '#C5CEE0'
          }]}>
            <Text style={styles.subscriptionText}>
              {item.subscription_type === 'premium' ? 'Premium' : 'Basic'}
            </Text>
          </View>
        </View>
      </View>
      
      <TouchableOpacity 
        style={styles.userActions} 
        onPress={() => handleOpenWorkoutModal(item)}
      >
        <Ionicons name="fitness" size={20} color="#92A3FD" />
      </TouchableOpacity>
    </TouchableOpacity>
  );

  return (
    <>
      <View style={styles.subTabContainer}>
        <TouchableOpacity
          style={[
            styles.subTabButton,
            activeUserType === "all" && styles.activeSubTabButton,
          ]}
          onPress={() => setActiveUserType("all")}
        >
          <Text
            style={[
              styles.subTabText,
              activeUserType === "all" && styles.activeSubTabText,
            ]}
          >
            All
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.subTabButton,
            activeUserType === "basic" && styles.activeSubTabButton,
          ]}
          onPress={() => setActiveUserType("basic")}
        >
          <Text
            style={[
              styles.subTabText,
              activeUserType === "basic" && styles.activeSubTabText,
            ]}
          >
            Basic
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.subTabButton,
            activeUserType === "premium" && styles.activeSubTabButton,
          ]}
          onPress={() => setActiveUserType("premium")}
        >
          <Text
            style={[
              styles.subTabText,
              activeUserType === "premium" && styles.activeSubTabText,
            ]}
          >
            Premium
          </Text>
        </TouchableOpacity>
      </View>

      <View style={styles.searchContainer}>
        <Ionicons name="search" size={20} color="#8F9BB3" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Suche nach Benutzern..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholderTextColor="#8F9BB3"
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity 
            onPress={() => setSearchQuery('')}
            style={styles.clearButton}
          >
            <Ionicons name="close-circle" size={20} color="#8F9BB3" />
          </TouchableOpacity>
        )}
      </View>

      <View style={styles.contentContainer}>
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#92A3FD" />
            <Text style={styles.loadingText}>Loading users...</Text>
          </View>
        ) : filteredUsers.length > 0 ? (
          <FlatList
            data={filteredUsers}
            renderItem={renderUserItem}
            keyExtractor={(item) => item.id}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.listContent}
          />
        ) : (
          <View style={styles.emptyState}>
            <Ionicons name="people-outline" size={64} color="#C5CEE0" />
            <Text style={styles.emptyStateText}>Keine Benutzer gefunden</Text>
            {activeUserType !== 'all' && (
              <Text style={styles.emptyStateSubText}>
                Es gibt keine {activeUserType === 'basic' ? 'Basic' : 'Premium'} Benutzer
              </Text>
            )}
            {searchQuery.length > 0 && (
              <TouchableOpacity
                style={styles.clearSearchButton}
                onPress={() => setSearchQuery('')}
              >
                <Text style={styles.clearSearchText}>Suche zurücksetzen</Text>
              </TouchableOpacity>
            )}
          </View>
        )}
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  contentContainer: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: "#8F9BB3",
  },
  subTabContainer: {
    flexDirection: "row",
    backgroundColor: "#F7F9FC",
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderBottomWidth: 1,
    borderBottomColor: "#EDF1F7",
  },
  subTabButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginHorizontal: 4,
  },
  activeSubTabButton: {
    backgroundColor: "#E4E9F2",
  },
  subTabText: {
    fontSize: 14,
    fontWeight: "500",
    color: "#8F9BB3",
  },
  activeSubTabText: {
    color: "#6B8CFF",
    fontWeight: "600",
  },
  searchContainer: {
    flexDirection: "row",
    backgroundColor: "#FFFFFF",
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#EDF1F7",
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: "#333",
  },
  clearButton: {
    marginLeft: 8,
  },
  listContent: {
    padding: 16,
  },
  emptyState: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  emptyStateText: {
    marginTop: 10,
    fontSize: 18,
    fontWeight: "600",
    color: "#8F9BB3",
  },
  emptyStateSubText: {
    marginTop: 8,
    fontSize: 14,
    color: "#8F9BB3",
    textAlign: "center",
  },
  clearSearchButton: {
    marginTop: 16,
    paddingVertical: 8,
    paddingHorizontal: 16,
    backgroundColor: "#E4E9F2",
    borderRadius: 20,
  },
  clearSearchText: {
    fontSize: 14,
    fontWeight: "500",
    color: "#6B8CFF",
  },
  userCard: {
    backgroundColor: "#FFFFFF",
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: "row",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  userAvatarContainer: {
    position: "relative",
  },
  userAvatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
  },
  placeholderAvatar: {
    backgroundColor: "#E4E9F2",
    justifyContent: "center",
    alignItems: "center",
  },
  userInitial: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#8F9BB3",
  },
  adminBadge: {
    position: "absolute",
    bottom: 0,
    right: 0,
    backgroundColor: "#6B8CFF",
    borderRadius: 10,
    width: 20,
    height: 20,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 2,
    borderColor: "#FFFFFF",
  },
  premiumBadge: {
    position: "absolute",
    top: 0,
    right: 0,
    backgroundColor: "#FFCB66",
    borderRadius: 10,
    width: 20,
    height: 20,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 2,
    borderColor: "#FFFFFF",
  },
  userInfo: {
    marginLeft: 16,
    flex: 1,
  },
  userName: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#2E3A59",
    marginBottom: 4,
  },
  userEmail: {
    fontSize: 14,
    color: "#8F9BB3",
    marginBottom: 6,
  },
  userMetaData: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 8,
  },
  metaItem: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#F7F9FC",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  metaText: {
    fontSize: 12,
    color: "#666",
    marginLeft: 4,
  },
  subscriptionBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginLeft: 8,
  },
  subscriptionText: {
    fontSize: 12,
    fontWeight: "500",
    color: "#fff",
  },
  userActions: {
    justifyContent: "center",
    alignItems: "center",
    padding: 10,
  },
});

export default UserTab;

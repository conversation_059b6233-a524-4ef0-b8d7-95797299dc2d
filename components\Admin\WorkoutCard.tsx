import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  ActivityIndicator
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { Ionicons } from "@expo/vector-icons";
import { Workout } from "../../types";
import { router } from "expo-router";

interface WorkoutCardProps {
  workout: Workout;
}

export const WorkoutCard = ({ workout }: WorkoutCardProps) => {
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty.toLowerCase()) {
      case "beginner":
        return "#92A3FD";
      case "intermediate":
        return "#FFA07A";
      case "advanced":
        return "#FF4B4B";
      default:
        return "#92A3FD";
    }
  };

  const LoadingImage = ({ source, style }: { source: { uri: string }, style: any }) => {
    const [isLoading, setIsLoading] = useState(true);

    return (
      <View style={[style, { justifyContent: "center", alignItems: "center" }]}>
        <Image
          source={source}
          style={[style, { position: isLoading ? "absolute" : "relative" }]}
          onLoadStart={() => setIsLoading(true)}
          onLoadEnd={() => setIsLoading(false)}
        />
        {isLoading && <ActivityIndicator size="small" color="#fff" />}
      </View>
    );
  };

  return (
    <TouchableOpacity
      style={styles.workoutCard}
      onPress={() => router.push(`/workout/${workout.id}`)}
    >
      <LinearGradient
        colors={["#92A3FD", "#9DCEFF"]}
        style={styles.cardGradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        {workout.icon && (
          <LoadingImage source={{ uri: workout.icon }} style={styles.workoutImage} />
        )}
        <View style={styles.workoutHeader}>
          <Text style={styles.workoutName}>{workout.name}</Text>
          <View
            style={[
              styles.difficultyBadge,
              { backgroundColor: getDifficultyColor(workout.difficulty) },
            ]}
          >
            <Text style={styles.difficultyText}>{workout.difficulty}</Text>
          </View>
        </View>

        <View style={styles.workoutDetails}>
          <View style={styles.detailItem}>
            <Ionicons name="time-outline" size={20} color="#fff" />
            <Text style={styles.detailText}>{workout.duration} Min</Text>
          </View>
          <View style={styles.detailItem}>
            <Ionicons name="fitness-outline" size={20} color="#fff" />
            <Text style={styles.detailText}>{workout.exercise_count} Übungen</Text>
          </View>
          <View style={styles.detailItem}>
            <Ionicons name="flame-outline" size={20} color="#fff" />
            <Text style={styles.detailText}>{workout.calories_burned} kcal</Text>
          </View>
        </View>

        <Text style={styles.workoutDescription} numberOfLines={2}>
          {workout.description}
        </Text>
      </LinearGradient>
    </TouchableOpacity>
  );
};

export const getDifficultyColor = (difficulty: string) => {
  switch (difficulty.toLowerCase()) {
    case "beginner":
      return "#92A3FD";
    case "intermediate":
      return "#FFA07A";
    case "advanced":
      return "#FF4B4B";
    default:
      return "#92A3FD";
  }
};

const styles = StyleSheet.create({
  workoutCard: {
    marginBottom: 16,
    borderRadius: 16,
    overflow: "hidden",
    elevation: 3,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  cardGradient: {
    padding: 16,
  },
  workoutHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  workoutName: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#fff",
    flex: 1,
  },
  difficultyBadge: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
    marginLeft: 8,
  },
  difficultyText: {
    color: "#fff",
    fontSize: 12,
    fontWeight: "500",
  },
  workoutDetails: {
    flexDirection: "row",
    justifyContent: "flex-start",
    marginBottom: 12,
    gap: 16,
  },
  detailItem: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
  },
  detailText: {
    color: "#fff",
    fontSize: 14,
  },
  workoutImage: {
    width: "100%",
    height: 150,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  workoutDescription: {
    color: "#fff",
    fontSize: 14,
    opacity: 0.9,
  },
});

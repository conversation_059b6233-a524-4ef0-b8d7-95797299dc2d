import React, { useState, useEffect, useRef } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Image,
  ActivityIndicator,
  SafeAreaView,
  ScrollView,
  Dimensions,
  Alert,
  TextInput,
  Animated,
  RefreshControl,
  Platform,
  PanResponder,
  useWindowDimensions,
} from "react-native";
import { Stack, useLocalSearchParams, router } from "expo-router";
import { supabase } from "../../lib/supabase";
import { Ionicons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import { Calendar, DateData } from 'react-native-calendars';
import { format, addDays, isSameDay, parseISO, addMonths, startOfMonth, endOfMonth, eachDayOfInterval } from 'date-fns';
import { Workout } from "../../types/workoutTypes";

const { width, height } = Dimensions.get("window");
const HEADER_MAX_HEIGHT = 250;
const HEADER_MIN_HEIGHT = Platform.OS === "ios" ? 90 : 70;
const HEADER_SCROLL_DISTANCE = HEADER_MAX_HEIGHT - HEADER_MIN_HEIGHT;

// Types
interface User {
  id: string;
  email: string;
  full_name: string;
  profile_picture_url: string | null;
  is_admin: boolean;
  created_at: string;
  last_sign_in_at: string | null;
  height?: number;
  weight?: number;
  birth_date?: string | null;
  subscription_type: 'basic' | 'premium' | null;
  goal?: string | null;
  gender?: string | null;
  daily_water_goal_ml?: number | null;
}

interface UserStats {
  completedCount: number;
  streakDays: number;
  totalAssigned: number;
  completionRate: number;
  lastActivity?: string | null;
}

// Weekly schedule types
interface WeeklyScheduleDay {
  dayName: string;
  dayIndex: number;
  workouts: Workout[];
}

// Day name translations
const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

interface WeekdayWorkout extends Workout {
  dayIndex: number;
}

const UserDetailPage = () => {
  const { id } = useLocalSearchParams();
  const userId = typeof id === 'string' ? id : '';
  const { width } = useWindowDimensions();
  
  const [user, setUser] = useState<User | null>(null);
  const [userStats, setUserStats] = useState<UserStats>({
    completedCount: 0,
    streakDays: 0,
    totalAssigned: 0,
    completionRate: 0,
    lastActivity: null,
  });
  
  // States
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState<"training" | "stats" | "history" | "schedule">("training");
  
  const [selectedDate, setSelectedDate] = useState<string>(format(new Date(), "yyyy-MM-dd"));
  const [calendarMarkedDates, setCalendarMarkedDates] = useState<{[key: string]: any}>({});
  
  const [dateWorkouts, setDateWorkouts] = useState<{[key: string]: Workout[]}>({});
  const [userWorkouts, setUserWorkouts] = useState<Workout[]>([]);
  const [availableWorkouts, setAvailableWorkouts] = useState<Workout[]>([]);
  const [allWorkouts, setAllWorkouts] = useState<Workout[]>([]);
  
  const [searchQuery, setSearchQuery] = useState("");
  const [showWorkoutSelector, setShowWorkoutSelector] = useState(false);
  const [weightHistory, setWeightHistory] = useState<{date: string, weight: number}[]>([]);
  const [activityHistory, setActivityHistory] = useState<any[]>([]);
  
  // Weekly schedule states
  const [weeklySchedule, setWeeklySchedule] = useState<WeeklyScheduleDay[]>(
    Array.from({ length: 7 }, (_, i) => ({
      dayName: dayNames[i],
      dayIndex: i,
      workouts: [],
    }))
  );
  const [showScheduleWorkoutPicker, setShowScheduleWorkoutPicker] = useState(false);
  const [selectedDayForWorkout, setSelectedDayForWorkout] = useState<number>(-1);
  const [isApplyingSchedule, setIsApplyingSchedule] = useState(false);

  // For the collapsible header animation
  const scrollY = useRef(new Animated.Value(0)).current;

  const headerHeight = scrollY.interpolate({
    inputRange: [0, HEADER_SCROLL_DISTANCE],
    outputRange: [HEADER_MAX_HEIGHT, HEADER_MIN_HEIGHT],
    extrapolate: "clamp",
  });

  const headerOpacity = scrollY.interpolate({
    inputRange: [0, HEADER_SCROLL_DISTANCE / 2, HEADER_SCROLL_DISTANCE],
    outputRange: [1, 0.5, 0],
    extrapolate: "clamp",
  });

  const headerTitleOpacity = scrollY.interpolate({
    inputRange: [0, HEADER_SCROLL_DISTANCE / 2, HEADER_SCROLL_DISTANCE],
    outputRange: [0, 0.5, 1],
    extrapolate: "clamp",
  });

  useEffect(() => {
    fetchData();
  }, [userId]);

  const fetchData = async () => {
    setLoading(true);
    try {
      await Promise.all([
        fetchUserDetails(),
        fetchWorkouts(),
        fetchWeightHistory(),
        fetchActivityHistory(),
        fetchWeeklySchedule(),
      ]);
    } finally {
      setLoading(false);
    }
  };

  // Fetch weekly recurring workouts
  const fetchWeeklySchedule = async () => {
    try {
      const { data, error } = await supabase
        .from("weekly_workout_schedule")
        .select("day_of_week, workout_id, workouts(id, name, duration, difficulty, type, icon)")
        .eq("user_id", userId);

      if (error) throw error;

      if (data && data.length > 0) {
        // Transform into our weekly schedule format
        const scheduleDays = Array.from({ length: 7 }, (_, i) => ({
          dayName: dayNames[i],
          dayIndex: i,
          workouts: [],
        }));

        data.forEach(scheduleItem => {
          const dayIndex = scheduleItem.day_of_week;
          if (dayIndex >= 0 && dayIndex <= 6 && scheduleItem.workouts) {
            scheduleDays[dayIndex].workouts.push(scheduleItem.workouts);
          }
        });

        setWeeklySchedule(scheduleDays);
      }
    } catch (error) {
      console.error("Error fetching weekly schedule:", error);
    }
  };

  const fetchUserDetails = async () => {
    try {
      // Fetch user details
      const { data: userData, error: userError } = await supabase
        .from("users")
        .select("*")
        .eq("id", userId)
        .single();

      if (userError) throw userError;
      setUser(userData);

      // Fetch assigned workouts
      await fetchUserWorkouts(userId);

      // Fetch last activity
      const { data: lastActivity } = await supabase
        .from("workout_history")
        .select("start_time")
        .eq("user_id", userId)
        .order("start_time", { ascending: false })
        .limit(1);

      // Update user stats with last activity
      if (lastActivity && lastActivity.length > 0) {
        setUserStats(prev => ({
          ...prev,
          lastActivity: lastActivity[0].start_time
        }));
      }

    } catch (error) {
      console.error("Error fetching user details:", error);
      Alert.alert("Error", "Failed to load user details");
    }
  };

  const fetchWorkouts = async () => {
    try {
      const { data, error } = await supabase
        .from("workouts")
        .select("*")
        .order("name");

      if (error) throw error;
      setAllWorkouts(data || []);
    } catch (error) {
      console.error("Error fetching workouts:", error);
    }
  };
  
  const fetchWeightHistory = async () => {
    try {
      const { data, error } = await supabase
        .from("weight_tracking")
        .select("*")
        .eq("user_id", userId)
        .order("date", { ascending: false });
        
      if (error) throw error;
      
      const formattedData = (data || []).map(entry => ({
        date: entry.date,
        weight: entry.weight
      }));
      
      setWeightHistory(formattedData);
    } catch (error) {
      console.error("Error fetching weight history:", error);
    }
  };
  
  const fetchActivityHistory = async () => {
    try {
      const { data, error } = await supabase
        .from("user_activities")
        .select("*")
        .eq("user_id", userId)
        .order("activity_date", { ascending: false })
        .limit(30);
        
      if (error) throw error;
      setActivityHistory(data || []);
    } catch (error) {
      console.error("Error fetching activity history:", error);
    }
  };

  const fetchUserWorkouts = async (userId: string) => {
    try {
      // Fetch all assigned workouts for this user
      const { data: assignedData, error: assignedError } = await supabase
        .from("assigned_workouts")
        .select(`
          *,
          workouts (*)
        `)
        .eq("user_id", userId);

      if (assignedError) throw assignedError;
      
      // Fetch workout history for this user
      const { data: historyData, error: historyError } = await supabase
        .from("workout_history")
        .select(`
          *,
          workouts (*)
        `)
        .eq("user_id", userId);
        
      if (historyError) throw historyError;
      
      // Process assigned workouts by date
      const workoutsByDate: {[key: string]: Workout[]} = {};
      const markedDates: {[key: string]: any} = {};
      
      if (assignedData && assignedData.length > 0) {
        assignedData.forEach(item => {
          if (!item.workouts) return;
          
          const date = item.assigned_date;
          if (!workoutsByDate[date]) workoutsByDate[date] = [];
          
          workoutsByDate[date].push({
            ...item.workouts,
            completed: item.completed
          });
          
          // Mark dates with workouts on calendar
          markedDates[date] = {
            marked: true,
            dotColor: item.completed ? '#4CAF50' : '#FFA07A',
            selected: date === selectedDate,
            selectedColor: date === selectedDate ? 'rgba(107, 140, 255, 0.15)' : undefined,
          };
        });
      }
      
      // Update current selected date to have selected styling
      if (!markedDates[selectedDate]) {
        markedDates[selectedDate] = {
          selected: true,
          selectedColor: 'rgba(107, 140, 255, 0.15)',
        };
      }
      
      // Calculate user stats
      const completedWorkouts = assignedData ? assignedData.filter(w => w.completed).length : 0;
      const totalAssigned = assignedData ? assignedData.length : 0;
      
      // Determine streak (consecutive days with completed workouts)
      let streakDays = 0;
      if (historyData && historyData.length > 0) {
        const sortedHistory = [...historyData]
          .filter(h => h.status === 'completed')
          .sort((a, b) => new Date(b.start_time).getTime() - new Date(a.start_time).getTime());
          
        // Calculate streak
        if (sortedHistory.length > 0) {
          streakDays = 1;
          for (let i = 1; i < sortedHistory.length; i++) {
            const prevDate = new Date(sortedHistory[i-1].start_time);
            const currDate = new Date(sortedHistory[i].start_time);
            
            // Check if dates are consecutive
            const diffTime = prevDate.getTime() - currDate.getTime();
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            
            if (diffDays === 1) {
              streakDays++;
            } else {
              break;
            }
          }
        }
      }
      
      setUserStats({
        completedCount: completedWorkouts,
        totalAssigned,
        streakDays,
        completionRate: totalAssigned > 0 ? Math.round((completedWorkouts / totalAssigned) * 100) : 0,
        lastActivity: historyData && historyData.length > 0 ? historyData[0].start_time : null
      });
      
      // Set the processed workout data
      setDateWorkouts(workoutsByDate);
      setCalendarMarkedDates(markedDates);
      
      // Get all available workouts minus those already assigned for today
      const assignedIdsForSelectedDate = new Set(
        (workoutsByDate[selectedDate] || []).map(w => w.id)
      );
      
      const { data: allWorkoutsData } = await supabase
        .from("workouts")
        .select("*")
        .order("name");
        
      const availableForToday = (allWorkoutsData || [])
        .filter(w => !assignedIdsForSelectedDate.has(w.id))
        .map(w => ({...w, selected: false}));
        
      setAvailableWorkouts(availableForToday);
      setUserWorkouts(workoutsByDate[selectedDate] || []);
    } catch (error) {
      console.error("Error fetching user workouts:", error);
    }
  };

  const handleDateSelect = (day: DateData) => {
    const dateString = day.dateString;
    setSelectedDate(dateString);
    
    // Update marked dates to show selection
    const updatedMarkedDates = {...calendarMarkedDates};
    
    // Remove old selection
    Object.keys(updatedMarkedDates).forEach(date => {
      if (updatedMarkedDates[date].selected) {
        if (updatedMarkedDates[date].marked) {
          updatedMarkedDates[date] = {
            ...updatedMarkedDates[date],
            selected: false,
          };
        } else {
          delete updatedMarkedDates[date];
        }
      }
    });
    
    // Add new selection
    if (updatedMarkedDates[dateString]) {
      updatedMarkedDates[dateString] = {
        ...updatedMarkedDates[dateString],
        selected: true,
        selectedColor: 'rgba(107, 140, 255, 0.15)',
      };
    } else {
      updatedMarkedDates[dateString] = {
        selected: true,
        selectedColor: 'rgba(107, 140, 255, 0.15)',
      };
    }
    
    setCalendarMarkedDates(updatedMarkedDates);
    
    // Update workouts for selected date
    setUserWorkouts(dateWorkouts[dateString] || []);
    
    // Update available workouts for selected date
    const assignedIdsForSelectedDate = new Set(
      (dateWorkouts[dateString] || []).map(w => w.id)
    );
    
    // Filter out workouts that are already assigned for this date
    const filteredWorkouts = allWorkouts.filter(w => !assignedIdsForSelectedDate.has(w.id))
      .map(w => ({...w, selected: false}));
    
    setAvailableWorkouts(filteredWorkouts);
  };

  const toggleWorkoutSelection = (workoutId: number) => {
    setAvailableWorkouts(prev => 
      prev.map(w => w.id === workoutId ? {...w, selected: !w.selected} : w)
    );
  };

  const assignWorkouts = async () => {
    try {
      if (!user) return;
      
      const selectedWorkouts = availableWorkouts.filter(w => w.selected);
      if (selectedWorkouts.length === 0) {
        Alert.alert("Keine Workouts ausgewählt", "Bitte wähle mindestens ein Workout aus.");
        return;
      }
      
      setLoading(true);
      
      // Create entries for assigned_workouts table with specified date
      const workoutAssignments = selectedWorkouts.map(workout => ({
        user_id: user.id,
        workout_id: workout.id,
        assigned_date: selectedDate,
        completed: false
      }));
      
      const { error } = await supabase
        .from("assigned_workouts")
        .insert(workoutAssignments);

      if (error) throw error;
      
      Alert.alert(
        "Workouts zugewiesen", 
        `${selectedWorkouts.length} Workout(s) wurden ${user.full_name} für den ${new Date(selectedDate).toLocaleDateString()} erfolgreich zugewiesen.`
      );
      
      // Refresh user workouts for this date
      await fetchUserWorkouts(user.id);
      setShowWorkoutSelector(false);
    } catch (error) {
      console.error("Error assigning workouts:", error);
      Alert.alert("Fehler", "Workouts konnten nicht zugewiesen werden.");
    } finally {
      setLoading(false);
    }
  };

  const toggleWorkoutCompletion = async (workout: Workout) => {
    if (!user) return;
    
    try {
      setLoading(true);
      
      const newCompletionStatus = !workout.completed;
      
      const { error } = await supabase
        .from("assigned_workouts")
        .update({ completed: newCompletionStatus })
        .eq("user_id", user.id)
        .eq("workout_id", workout.id)
        .eq("assigned_date", selectedDate);
      
      if (error) throw error;
      
      // Also update workout_history if needed for completed workouts
      if (newCompletionStatus) {
        await supabase.from("workout_history").insert({
          user_id: user.id,
          workout_id: workout.id,
          start_time: new Date().toISOString(),
          end_time: new Date().toISOString(),
          status: 'completed'
        });
      }
      
      // Refresh user workouts
      await fetchUserWorkouts(user.id);
      
    } catch (error) {
      console.error("Error toggling workout completion:", error);
      Alert.alert("Fehler", "Status konnte nicht aktualisiert werden.");
    } finally {
      setLoading(false);
    }
  };

  const removeWorkout = async (workoutId: number) => {
    if (!user) return;
    
    try {
      setLoading(true);
      
      const { error } = await supabase
        .from("assigned_workouts")
        .delete()
        .eq("user_id", user.id)
        .eq("workout_id", workoutId)
        .eq("assigned_date", selectedDate);
      
      if (error) throw error;
      
      // Refresh user workouts
      await fetchUserWorkouts(user.id);
      
      Alert.alert("Workout entfernt", "Das Workout wurde erfolgreich entfernt.");
    } catch (error) {
      console.error("Error removing workout:", error);
      Alert.alert("Fehler", "Workout konnte nicht entfernt werden.");
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await fetchData();
    setRefreshing(false);
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty.toLowerCase()) {
      case "beginner":
        return "#92A3FD";
      case "intermediate":
        return "#FFA07A";
      case "advanced":
        return "#FF4B4B";
      default:
        return "#92A3FD";
    }
  };

  const filteredAvailableWorkouts = searchQuery.length > 0
    ? availableWorkouts.filter(
        w => w.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
             w.type.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : availableWorkouts;

  // Weekly schedule functions
  const openDayWorkoutPicker = (dayIndex: number) => {
    setSelectedDayForWorkout(dayIndex);
    setShowScheduleWorkoutPicker(true);
  };

  const addWorkoutToDay = (workout: Workout) => {
    if (selectedDayForWorkout === -1) return;

    setWeeklySchedule(prev => 
      prev.map(day => {
        if (day.dayIndex === selectedDayForWorkout) {
          // Check if workout already exists for this day
          const workoutExists = day.workouts.some(w => w.id === workout.id);
          if (!workoutExists) {
            return {
              ...day,
              workouts: [...day.workouts, workout]
            };
          }
        }
        return day;
      })
    );
    
    // Show a brief toast or alert
    Alert.alert(
      "Workout Added", 
      `"${workout.name}" added to ${dayNames[selectedDayForWorkout]}.`
    );
    
    // Automatically return to weekly schedule view
    setShowScheduleWorkoutPicker(false);
  };

  const removeWorkoutFromDay = (dayIndex: number, workoutId: number) => {
    setWeeklySchedule(prev => 
      prev.map(day => {
        if (day.dayIndex === dayIndex) {
          return {
            ...day,
            workouts: day.workouts.filter(w => w.id !== workoutId)
          };
        }
        return day;
      })
    );
  };

  const moveWorkout = (fromDayIndex: number, workoutId: number, toDayIndex: number) => {
    setWeeklySchedule(prev => {
      // Find the workout to move
      const fromDay = prev.find(d => d.dayIndex === fromDayIndex);
      if (!fromDay) return prev;

      const workoutToMove = fromDay.workouts.find(w => w.id === workoutId);
      if (!workoutToMove) return prev;

      // Create a new schedule with the workout removed from the original day
      const updatedSchedule = prev.map(day => {
        if (day.dayIndex === fromDayIndex) {
          return {
            ...day,
            workouts: day.workouts.filter(w => w.id !== workoutId)
          };
        }
        return day;
      });

      // Add the workout to the target day
      return updatedSchedule.map(day => {
        if (day.dayIndex === toDayIndex) {
          // Check if workout already exists
          const workoutExists = day.workouts.some(w => w.id === workoutId);
          if (!workoutExists) {
            return {
              ...day,
              workouts: [...day.workouts, workoutToMove]
            };
          }
        }
        return day;
      });
    });
  };

  const clearAllWorkouts = () => {
    Alert.alert(
      "Remove All Workouts", 
      "Are you sure you want to remove all workouts from the weekly schedule?",
      [
        {
          text: "Cancel",
          style: "cancel"
        },
        {
          text: "Remove",
          style: "destructive",
          onPress: () => {
            setWeeklySchedule(prev => 
              prev.map(day => ({
                ...day,
                workouts: []
              }))
            );
          }
        }
      ]
    );
  };

  const saveWeeklySchedule = async () => {
    try {
      setLoading(true);
      
      // Delete all existing weekly schedule entries for this user
      await supabase
        .from("weekly_workout_schedule")
        .delete()
        .eq("user_id", userId);
      
      // Create schedule items for all workouts in the weekly schedule
      const scheduleItems = [];
      weeklySchedule.forEach(day => {
        day.workouts.forEach(workout => {
          scheduleItems.push({
            user_id: userId,
            day_of_week: day.dayIndex,
            workout_id: workout.id
          });
        });
      });

      if (scheduleItems.length > 0) {
        const { error } = await supabase
          .from("weekly_workout_schedule")
          .insert(scheduleItems);

        if (error) throw error;
      }

      Alert.alert("Success", "Weekly schedule has been saved");
    } catch (error) {
      console.error("Error saving weekly schedule:", error);
      Alert.alert("Error", "Failed to save weekly schedule");
    } finally {
      setLoading(false);
    }
  };

  // Apply the weekly schedule to the current month
  const applyScheduleToMonth = async () => {
    try {
      setIsApplyingSchedule(true);
      
      // Get all workout IDs from the weekly schedule
      const weeklyWorkoutIds = new Set<number>();
      weeklySchedule.forEach(day => {
        day.workouts.forEach(workout => {
          weeklyWorkoutIds.add(workout.id);
        });
      });
      
      if (weeklyWorkoutIds.size === 0) {
        Alert.alert("No Workouts", "Please add workouts to your weekly schedule first");
        setIsApplyingSchedule(false);
        return;
      }
      
      // Calculate date range for current month and next month
      const currentDate = new Date();
      const startDate = startOfMonth(currentDate);
      const endDate = endOfMonth(addMonths(currentDate, 1)); // Apply for 2 months
      
      // Get all dates in the range
      const dateRange = eachDayOfInterval({ start: startDate, end: endDate });
      
      // Delete all existing assignments in this range
      const formattedStartDate = format(startDate, "yyyy-MM-dd");
      const formattedEndDate = format(endDate, "yyyy-MM-dd");
      
      await supabase
        .from("assigned_workouts")
        .delete()
        .eq("user_id", userId)
        .gte("assigned_date", formattedStartDate)
        .lte("assigned_date", formattedEndDate);
      
      // Create assignments based on the weekly schedule
      const assignments = [];
      
      dateRange.forEach(date => {
        const dayOfWeek = date.getDay(); // 0 = Sunday, 1 = Monday, etc.
        const daySchedule = weeklySchedule.find(day => day.dayIndex === dayOfWeek);
        
        if (daySchedule && daySchedule.workouts.length > 0) {
          daySchedule.workouts.forEach(workout => {
            assignments.push({
              user_id: userId,
              workout_id: workout.id,
              assigned_date: format(date, "yyyy-MM-dd"),
              completed: false,
            });
          });
        }
      });
      
      if (assignments.length > 0) {
        // Insert in batches of 50 to avoid payload issues
        const batchSize = 50;
        for (let i = 0; i < assignments.length; i += batchSize) {
          const batch = assignments.slice(i, i + batchSize);
          const { error } = await supabase
            .from("assigned_workouts")
            .insert(batch);
            
          if (error) throw error;
        }
      }
      
      // Refresh user workouts and marked dates
      await fetchUserWorkouts(userId);
      
      Alert.alert(
        "Schedule Applied", 
        `Weekly schedule has been applied to the next 60 days (${assignments.length} workout assignments created).`
      );
      
    } catch (error) {
      console.error("Error applying weekly schedule:", error);
      Alert.alert("Error", "Failed to apply weekly schedule");
    } finally {
      setIsApplyingSchedule(false);
    }
  };
  
  // Move a workout to the previous or next day
  const moveWorkoutToDay = async (workout: Workout, direction: "previous" | "next") => {
    try {
      setLoading(true);
      
      // Calculate the new date
      const currentDate = parseISO(selectedDate);
      const newDate = direction === "next" 
        ? addDays(currentDate, 1) 
        : addDays(currentDate, -1);
      const formattedNewDate = format(newDate, "yyyy-MM-dd");
      
      // Check if workout is already assigned to the target date
      const { data } = await supabase
        .from("assigned_workouts")
        .select("id")
        .eq("user_id", userId)
        .eq("workout_id", workout.id)
        .eq("assigned_date", formattedNewDate);
        
      if (data && data.length > 0) {
        Alert.alert("Workout bereits zugewiesen", 
          `Dieses Workout ist bereits für den ${formattedNewDate} zugewiesen.`);
        return;
      }
      
      // Delete workout from current date
      await supabase
        .from("assigned_workouts")
        .delete()
        .eq("user_id", userId)
        .eq("workout_id", workout.id)
        .eq("assigned_date", selectedDate);
        
      // Create assignment for the new date
      await supabase
        .from("assigned_workouts")
        .insert([{
          user_id: userId,
          workout_id: workout.id,
          assigned_date: formattedNewDate,
          completed: false,
        }]);
        
      // Update the UI - refresh workouts
      await fetchUserWorkouts(userId);
      
      Alert.alert("Workout verschoben", 
        `Workout wurde auf den ${formattedNewDate} verschoben.`);
        
    } catch (error) {
      console.error("Error moving workout:", error);
      Alert.alert("Fehler", "Das Workout konnte nicht verschoben werden");
    } finally {
      setLoading(false);
    }
  };

  // Render assigned workout item with completion toggle and remove option
  const renderAssignedWorkoutItem = ({ item }: { item: Workout }) => (
    <View style={styles.assignedWorkout}>
      <TouchableOpacity 
        style={[
          styles.completionCheckbox, 
          item.completed && styles.completionCheckboxChecked
        ]}
        onPress={() => toggleWorkoutCompletion(item)}
      >
        {item.completed && <Ionicons name="checkmark" size={16} color="#fff" />}
      </TouchableOpacity>
      
      <View style={styles.assignedWorkoutContent}>
        <Text style={[
          styles.assignedWorkoutName,
          item.completed && styles.completedWorkoutText
        ]}>
          {item.name}
        </Text>
        <View style={styles.assignedWorkoutDetails}>
          <View style={styles.assignedDetailItem}>
            <Ionicons name="time-outline" size={16} color="#666" />
            <Text style={styles.assignedDetailText}>{item.duration} Min</Text>
          </View>
          <View
            style={[
              styles.assignedDifficultyBadge,
              { backgroundColor: getDifficultyColor(item.difficulty) },
            ]}
          >
            <Text style={styles.assignedDifficultyText}>{item.difficulty}</Text>
          </View>
        </View>
      </View>
      
      <View style={styles.workoutActions}>
        {/* Move to previous day button */}
        <TouchableOpacity
          style={styles.moveButton}
          onPress={() => moveWorkoutToDay(item, "previous")}
        >
          <Ionicons name="arrow-back" size={18} color="#6B8CFF" />
        </TouchableOpacity>
        
        {/* Move to next day button */}
        <TouchableOpacity
          style={styles.moveButton}
          onPress={() => moveWorkoutToDay(item, "next")}
        >
          <Ionicons name="arrow-forward" size={18} color="#6B8CFF" />
        </TouchableOpacity>
        
        {/* Remove button */}
        <TouchableOpacity
          style={styles.removeButton}
          onPress={() => removeWorkout(item.id)}
        >
          <Ionicons name="trash-outline" size={20} color="#FF4B4B" />
        </TouchableOpacity>
      </View>
    </View>
  );

  // Render workout item for selection
  const renderSelectableWorkoutItem = ({ item }: { item: Workout }) => (
    <TouchableOpacity
      style={[
        styles.selectableWorkout,
        item.selected && styles.selectedWorkout
      ]}
      onPress={() => toggleWorkoutSelection(item.id)}
      activeOpacity={0.7}
    >
      <View style={styles.selectableWorkoutContent}>
        <Text style={styles.selectableWorkoutName}>{item.name}</Text>
        <View style={styles.selectableWorkoutDetails}>
          <View style={styles.selectableDetailItem}>
            <Ionicons name="time-outline" size={16} color="#666" />
            <Text style={styles.selectableDetailText}>{item.duration} Min</Text>
          </View>
          <View style={styles.selectableDetailItem}>
            <Ionicons name="barbell-outline" size={16} color="#666" />
            <Text style={styles.selectableDetailText}>{item.type}</Text>
          </View>
          <View
            style={[
              styles.selectableDifficultyBadge,
              { backgroundColor: getDifficultyColor(item.difficulty) },
            ]}
          >
            <Text style={styles.selectableDifficultyText}>{item.difficulty}</Text>
          </View>
        </View>
      </View>
      <View style={[styles.checkBox, item.selected && styles.checkBoxSelected]}>
        {item.selected && <Ionicons name="checkmark" size={16} color="#fff" />}
      </View>
    </TouchableOpacity>
  );

  // Render workout in weekly schedule
  const renderWeeklyWorkoutItem = ({ item, dayIndex }: { item: Workout, dayIndex: number }) => (
    <View style={styles.weeklyWorkoutItem}>
      <View style={styles.weeklyWorkoutContent}>
        <Text style={styles.weeklyWorkoutName} numberOfLines={1}>
          {item.name}
        </Text>
        <View style={styles.weeklyWorkoutDetails}>
          <View style={styles.weeklyWorkoutDetail}>
            <Ionicons name="time-outline" size={14} color="#666" />
            <Text style={styles.weeklyWorkoutDetailText}>{item.duration}min</Text>
          </View>
        </View>
      </View>
      
      <TouchableOpacity
        style={styles.weeklyWorkoutRemove}
        onPress={() => removeWorkoutFromDay(dayIndex, item.id)}
      >
        <Ionicons name="close-circle" size={18} color="#FF4B4B" />
      </TouchableOpacity>
    </View>
  );

  // Render day schedule in weekly view
  const renderDaySchedule = (day: WeeklyScheduleDay) => (
    <View style={styles.dayScheduleContainer} key={day.dayIndex}>
      <View style={styles.dayHeader}>
        <Text style={styles.dayName}>{day.dayName}</Text>
        <TouchableOpacity
          style={styles.addDayWorkoutButton}
          onPress={() => openDayWorkoutPicker(day.dayIndex)}
        >
          <Ionicons name="add-circle" size={22} color="#6B8CFF" />
        </TouchableOpacity>
      </View>
      
      <View style={styles.dayWorkoutsList}>
        {day.workouts.length > 0 ? (
          day.workouts.map(workout => (
            <React.Fragment key={workout.id}>
              {renderWeeklyWorkoutItem({ item: workout, dayIndex: day.dayIndex })}
            </React.Fragment>
          ))
        ) : (
          <Text style={styles.noWorkoutsText}>No workouts</Text>
        )}
      </View>
    </View>
  );
  
  // Header component with user info
  const renderHeader = () => {
    return (
      <>
        {/* Animated header for scrolling effect */}
        <Animated.View
          style={[
            styles.headerContainer,
            { height: headerHeight, opacity: headerOpacity },
          ]}
        >
          <LinearGradient
            colors={["#92A3FD", "#9DCEFF"]}
            style={styles.headerGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            <View style={styles.headerContent}>
              <View style={styles.userHeaderInfo}>
                <View style={styles.userAvatarContainer}>
                  {user?.profile_picture_url ? (
                    <Image
                      source={{ uri: user.profile_picture_url }}
                      style={styles.userAvatar}
                    />
                  ) : (
                    <View style={[styles.userAvatar, styles.placeholderAvatar]}>
                      <Text style={styles.userInitial}>
                        {user?.full_name?.charAt(0) || "U"}
                      </Text>
                    </View>
                  )}
                  {user?.subscription_type === "premium" && (
                    <View style={styles.premiumBadge}>
                      <Ionicons name="star" size={10} color="#fff" />
                    </View>
                  )}
                </View>

                <View style={styles.userHeaderDetails}>
                  <Text style={styles.userName}>{user?.full_name || "User"}</Text>
                  <Text style={styles.userEmail}>{user?.email || ""}</Text>
                  
                  <View style={styles.userMetaContainer}>
                    {user?.height && (
                      <View style={styles.metaItem}>
                        <Ionicons name="resize-outline" size={14} color="#fff" />
                        <Text style={styles.metaItemText}>{user.height} cm</Text>
                      </View>
                    )}
                    {user?.weight && (
                      <View style={styles.metaItem}>
                        <Ionicons name="barbell-outline" size={14} color="#fff" />
                        <Text style={styles.metaItemText}>{user.weight} kg</Text>
                      </View>
                    )}
                    {user?.gender && (
                      <View style={styles.metaItem}>
                        <Ionicons 
                          name={user.gender === 'female' ? "female" : "male"} 
                          size={14} 
                          color="#fff" 
                        />
                        <Text style={styles.metaItemText}>
                          {user.gender === 'female' ? 'Weiblich' : 'Männlich'}
                        </Text>
                      </View>
                    )}
                  </View>
                </View>
              </View>

              <View style={styles.subscriptionContainer}>
                <View
                  style={[
                    styles.subscriptionBadge,
                    {
                      backgroundColor:
                        user?.subscription_type === "premium"
                          ? "#FFCB66"
                          : "#9E9E9E",
                    },
                  ]}
                >
                  <Text style={styles.subscriptionText}>
                    {user?.subscription_type === "premium"
                      ? "Premium"
                      : "Basic"}
                  </Text>
                </View>
                {user?.goal && (
                  <View style={styles.goalBadge}>
                    <Ionicons name="flag-outline" size={12} color="#fff" />
                    <Text style={styles.goalText}>{user.goal}</Text>
                  </View>
                )}
              </View>
            </View>
          </LinearGradient>
        </Animated.View>

        {/* Title that appears when scrolling */}
        <Animated.View
          style={[
            styles.headerTitleContainer,
            { opacity: headerTitleOpacity },
          ]}
        >
          <Text style={styles.headerTitle} numberOfLines={1}>
            {user?.full_name || "User"}
          </Text>
          {user?.subscription_type === "premium" && (
            <View style={styles.miniPremiumBadge}>
              <Ionicons name="star" size={10} color="#fff" />
            </View>
          )}
        </Animated.View>
      </>
    );
  };

  // Stats component
  const renderStats = () => {
    return (
      <View style={styles.statsContainer}>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{userStats.totalAssigned}</Text>
          <Text style={styles.statLabel}>Zugewiesen</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{userStats.completedCount}</Text>
          <Text style={styles.statLabel}>Absolviert</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{userStats.streakDays}</Text>
          <Text style={styles.statLabel}>Streak</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{userStats.completionRate}%</Text>
          <Text style={styles.statLabel}>Quote</Text>
        </View>
      </View>
    );
  };

  // Tab bar component
  const renderTabBar = () => {
    const isCompactView = width < 380; // Threshold for compact view
    
    return (
      <View style={styles.tabBar}>
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === "training" && styles.activeTab,
          ]}
          onPress={() => setActiveTab("training")}
        >
          <Ionicons
            name="calendar"
            size={20}
            color={activeTab === "training" ? "#6B8CFF" : "#8F9BB3"}
          />
          {!isCompactView && (
            <Text style={[
              styles.tabText,
              activeTab === "training" && styles.activeTabText
            ]}>
              Training
            </Text>
          )}
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === "schedule" && styles.activeTab,
          ]}
          onPress={() => setActiveTab("schedule")}
        >
          <Ionicons
            name="calendar-number"
            size={20}
            color={activeTab === "schedule" ? "#6B8CFF" : "#8F9BB3"}
          />
          {!isCompactView && (
            <Text style={[
              styles.tabText,
              activeTab === "schedule" && styles.activeTabText
            ]}>
              Weekly
            </Text>
          )}
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === "stats" && styles.activeTab,
          ]}
          onPress={() => setActiveTab("stats")}
        >
          <Ionicons
            name="stats-chart"
            size={20}
            color={activeTab === "stats" ? "#6B8CFF" : "#8F9BB3"}
          />
          {!isCompactView && (
            <Text style={[
              styles.tabText,
              activeTab === "stats" && styles.activeTabText
            ]}>
              Stats
            </Text>
          )}
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.tab,
            activeTab === "history" && styles.activeTab,
          ]}
          onPress={() => setActiveTab("history")}
        >
          <Ionicons
            name="time"
            size={20}
            color={activeTab === "history" ? "#6B8CFF" : "#8F9BB3"}
          />
          {!isCompactView && (
            <Text style={[
              styles.tabText,
              activeTab === "history" && styles.activeTabText
            ]}>
              History
            </Text>
          )}
        </TouchableOpacity>
      </View>
    );
  };

  // Render content based on active tab
  const renderContent = () => {
    if (activeTab === "training") {
      return renderTrainingContent();
    } else if (activeTab === "stats") {
      return renderStatsContent();
    } else if (activeTab === "history") {
      return renderHistoryContent();
    } else {
      return renderScheduleContent();
    }
  };

  // Render training tab content
  const renderTrainingContent = () => {
    return (
      <>
        {/* Calendar for date selection */}
        <View style={styles.calendarContainer}>
          <Calendar
            theme={{
              backgroundColor: '#ffffff',
              calendarBackground: '#ffffff',
              textSectionTitleColor: '#b6c1cd',
              selectedDayBackgroundColor: '#6B8CFF',
              selectedDayTextColor: '#ffffff',
              todayTextColor: '#6B8CFF',
              dayTextColor: '#2d4150',
              textDisabledColor: '#d9e1e8',
              dotColor: '#6B8CFF',
              selectedDotColor: '#ffffff',
              arrowColor: '#6B8CFF',
              monthTextColor: '#2d4150',
              indicatorColor: '#6B8CFF',
              textDayFontFamily: 'System',
              textMonthFontFamily: 'System',
              textDayHeaderFontFamily: 'System',
              textDayFontWeight: '300',
              textMonthFontWeight: 'bold',
              textDayHeaderFontWeight: '300',
              textDayFontSize: 16,
              textMonthFontSize: 16,
              textDayHeaderFontSize: 13
            }}
            markedDates={calendarMarkedDates}
            onDayPress={handleDateSelect}
            enableSwipeMonths={true}
            hideExtraDays={false}
          />
        </View>

        {/* Selected Date Header */}
        <View style={styles.selectedDateHeader}>
          <Ionicons name="calendar" size={20} color="#6B8CFF" />
          <Text style={styles.selectedDateText}>
            {new Date(selectedDate).toLocaleDateString('de-DE', {
              weekday: 'long',
              year: 'numeric',
              month: 'long',
              day: 'numeric'
            })}
          </Text>
        </View>

        {/* Workout section content */}
        {showWorkoutSelector ? (
          // Workout selection view
          <View style={styles.workoutSelectorContainer}>
            <View style={styles.searchBarContainer}>
              <Ionicons name="search" size={20} color="#8F9BB3" />
              <TextInput
                style={styles.searchBarInput}
                placeholder="Workouts durchsuchen..."
                value={searchQuery}
                onChangeText={setSearchQuery}
                placeholderTextColor="#8F9BB3"
              />
              {searchQuery.length > 0 && (
                <TouchableOpacity onPress={() => setSearchQuery('')}>
                  <Ionicons name="close-circle" size={20} color="#8F9BB3" />
                </TouchableOpacity>
              )}
            </View>

            {filteredAvailableWorkouts.length > 0 ? (
              <FlatList
                data={filteredAvailableWorkouts}
                renderItem={renderSelectableWorkoutItem}
                keyExtractor={(item) => item.id.toString()}
                style={styles.workoutList}
                showsVerticalScrollIndicator={false}
              />
            ) : (
              <View style={styles.emptyListContainer}>
                <Ionicons name="fitness-outline" size={48} color="#C5CEE0" />
                <Text style={styles.emptyListText}>
                  Keine passenden Workouts gefunden
                </Text>
              </View>
            )}

            <View style={styles.actionButtonsContainer}>
              <TouchableOpacity 
                style={styles.cancelButton}
                onPress={() => setShowWorkoutSelector(false)}
              >
                <Text style={styles.cancelButtonText}>Abbrechen</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[
                  styles.assignButton,
                  !availableWorkouts.some(w => w.selected) && styles.disabledButton
                ]}
                onPress={assignWorkouts}
                disabled={!availableWorkouts.some(w => w.selected)}
              >
                <Text style={styles.assignButtonText}>Workout zuweisen</Text>
              </TouchableOpacity>
            </View>
          </View>
        ) : (
          // Assigned workouts view
          <View style={styles.workoutsContainer}>
            <View style={styles.sectionHeaderRow}>
              <Text style={styles.sectionTitle}>Workouts für diesen Tag</Text>
              <TouchableOpacity 
                style={styles.addButton}
                onPress={() => setShowWorkoutSelector(true)}
              >
                <Ionicons name="add-circle" size={24} color="#6B8CFF" />
                <Text style={styles.addButtonText}>Hinzufügen</Text>
              </TouchableOpacity>
            </View>

            {userWorkouts.length > 0 ? (
              <FlatList
                data={userWorkouts}
                renderItem={renderAssignedWorkoutItem}
                keyExtractor={(item) => `${item.id}-${selectedDate}`}
                style={styles.workoutList}
                showsVerticalScrollIndicator={false}
              />
            ) : (
              <View style={styles.emptyListContainer}>
                <Ionicons name="calendar-outline" size={48} color="#C5CEE0" />
                <Text style={styles.emptyListText}>
                  Keine Workouts für diesen Tag
                </Text>
                <Text style={styles.emptyListSubText}>
                  Füge Workouts hinzu, um einen Trainingsplan zu erstellen
                </Text>
              </View>
            )}
          </View>
        )}
      </>
    );
  };

  // Render stats tab content
  const renderStatsContent = () => {
    return (
      <View style={styles.statsContent}>
        {/* Progress metrics */}
        <View style={styles.statsCard}>
          <Text style={styles.statsCardTitle}>Trainingsfortschritt</Text>
          <View style={styles.progressChartContainer}>
            {/* Progress chart could be added here */}
            <Text style={styles.progressText}>
              {userStats.completionRate}% abgeschlossen
            </Text>
            <View style={styles.progressBar}>
              <View 
                style={[
                  styles.progressFill, 
                  { width: `${userStats.completionRate}%` }
                ]} 
              />
            </View>
            <Text style={styles.progressSubText}>
              {userStats.completedCount} von {userStats.totalAssigned} Workouts absolviert
            </Text>
          </View>
        </View>

        {/* Weight history */}
        <View style={styles.statsCard}>
          <Text style={styles.statsCardTitle}>Gewichtsverlauf</Text>
          {weightHistory.length > 0 ? (
            <View style={styles.weightChartContainer}>
              {/* Weight chart could be added here */}
              <Text style={styles.currentWeight}>
                {weightHistory[0].weight} kg
              </Text>
              <Text style={styles.weightDate}>
                Stand: {new Date(weightHistory[0].date).toLocaleDateString()}
              </Text>
            </View>
          ) : (
            <View style={styles.emptyStatsContainer}>
              <Ionicons name="barbell-outline" size={40} color="#C5CEE0" />
              <Text style={styles.emptyStatsText}>
                Keine Gewichtsdaten verfügbar
              </Text>
            </View>
          )}
        </View>

        {/* Activity summary */}
        <View style={styles.statsCard}>
          <Text style={styles.statsCardTitle}>Aktivitäten</Text>
          <View style={styles.activitySummary}>
            <View style={styles.activityMetric}>
              <Ionicons name="flame" size={24} color="#FF4B4B" />
              <Text style={styles.activityMetricValue}>{userStats.streakDays}</Text>
              <Text style={styles.activityMetricLabel}>Streak</Text>
            </View>
            <View style={styles.activityMetric}>
              <Ionicons name="calendar-number" size={24} color="#6B8CFF" />
              <Text style={styles.activityMetricValue}>{userStats.totalAssigned}</Text>
              <Text style={styles.activityMetricLabel}>Trainings</Text>
            </View>
            <View style={styles.activityMetric}>
              <Ionicons name="time" size={24} color="#4CAF50" />
              <Text style={styles.activityMetricValue}>
                {userStats.lastActivity ? 
                  new Date(userStats.lastActivity).toLocaleDateString() : 
                  '-'}
              </Text>
              <Text style={styles.activityMetricLabel}>Letztes Training</Text>
            </View>
          </View>
        </View>
      </View>
    );
  };

  // Render history tab content
  const renderHistoryContent = () => {
    return (
      <View style={styles.historyContent}>
        <Text style={styles.historyTitle}>Trainings-Historie</Text>
        {activityHistory.length > 0 ? (
          <FlatList
            data={activityHistory}
            renderItem={renderHistoryItem}
            keyExtractor={(item) => item.id}
            style={styles.historyList}
            showsVerticalScrollIndicator={false}
          />
        ) : (
          <View style={styles.emptyHistoryContainer}>
            <Ionicons name="time" size={48} color="#C5CEE0" />
            <Text style={styles.emptyHistoryText}>
              Keine Trainings-Historie verfügbar
            </Text>
            <Text style={styles.emptyHistorySubText}>
              Hier werden abgeschlossene Trainings angezeigt
            </Text>
          </View>
        )}
      </View>
    );
  };

  // Render schedule tab content
  const renderScheduleContent = () => {
    if (showScheduleWorkoutPicker) {
      const dayName = selectedDayForWorkout >= 0 ? 
        weeklySchedule[selectedDayForWorkout].dayName : '';
      
      return (
        <View style={styles.schedulePickerContainer}>
          <View style={styles.schedulePickerHeader}>
            <Text style={styles.schedulePickerTitle}>
              Select workout for {dayName}
            </Text>
            <TouchableOpacity
              style={styles.closePickerButton}
              onPress={() => setShowScheduleWorkoutPicker(false)}
            >
              <Ionicons name="close" size={24} color="#8F9BB3" />
            </TouchableOpacity>
          </View>
          
          <View style={styles.searchBarContainer}>
            <Ionicons name="search" size={20} color="#8F9BB3" />
            <TextInput
              style={styles.searchBarInput}
              placeholder="Search workouts..."
              value={searchQuery}
              onChangeText={setSearchQuery}
              placeholderTextColor="#8F9BB3"
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity onPress={() => setSearchQuery('')}>
                <Ionicons name="close-circle" size={20} color="#8F9BB3" />
              </TouchableOpacity>
            )}
          </View>

          {allWorkouts.length > 0 ? (
            <FlatList
              data={filteredAvailableWorkouts}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={styles.scheduleWorkoutItem}
                  onPress={() => addWorkoutToDay(item)}
                >
                  <View style={styles.scheduleWorkoutContent}>
                    <Text style={styles.scheduleWorkoutName}>{item.name}</Text>
                    <View style={styles.scheduleWorkoutDetails}>
                      <View style={styles.scheduleWorkoutDetail}>
                        <Ionicons name="time-outline" size={16} color="#666" />
                        <Text style={styles.scheduleWorkoutDetailText}>
                          {item.duration} min
                        </Text>
                      </View>
                      <View
                        style={[
                          styles.scheduleDifficultyBadge,
                          { backgroundColor: getDifficultyColor(item.difficulty) },
                        ]}
                      >
                        <Text style={styles.scheduleDifficultyText}>
                          {item.difficulty}
                        </Text>
                      </View>
                    </View>
                  </View>
                  <Ionicons name="add-circle" size={22} color="#6B8CFF" />
                </TouchableOpacity>
              )}
              keyExtractor={(item) => item.id.toString()}
              style={styles.workoutList}
              showsVerticalScrollIndicator={false}
            />
          ) : (
            <View style={styles.emptyListContainer}>
              <Ionicons name="fitness-outline" size={48} color="#C5CEE0" />
              <Text style={styles.emptyListText}>
                No workouts available
              </Text>
            </View>
          )}
        </View>
      );
    }

    return (
      <View style={styles.scheduleContainer}>
        <View style={styles.scheduleHeader}>
          <Text style={styles.scheduleTitle}>Weekly Schedule</Text>
          <TouchableOpacity
            style={styles.clearAllButton}
            onPress={clearAllWorkouts}
          >
            <Ionicons name="trash-outline" size={20} color="#FF4B4B" />
            <Text style={styles.clearAllButtonText}>Clear All</Text>
          </TouchableOpacity>
        </View>
        
        <ScrollView style={styles.scheduleContent}>
          {weeklySchedule.map(day => renderDaySchedule(day))}
        </ScrollView>
        
        <View style={styles.scheduleActions}>
          <TouchableOpacity
            style={styles.saveScheduleButton}
            onPress={saveWeeklySchedule}
          >
            <Text style={styles.saveScheduleButtonText}>Save</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.applyScheduleButton,
              isApplyingSchedule && styles.disabledButton
            ]}
            onPress={applyScheduleToMonth}
            disabled={isApplyingSchedule}
          >
            <Text style={styles.applyScheduleButtonText}>
              {isApplyingSchedule ? "Applying..." : "Apply to Month"}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#6B8CFF" />
          <Text style={styles.loadingText}>Daten werden geladen...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen
        options={{
          title: "",
          headerTransparent: true,
          headerShadowVisible: false,
          headerBackVisible: false,
          headerLeft: () => null, // Remove default header back button
        }}
      />
      
      {renderHeader()}
      
      {/* Custom back button positioned with proper spacing for notch */}
      <View style={styles.customHeaderButtonContainer}>
        <TouchableOpacity
          style={styles.customBackButton}
          onPress={() => router.back()}
          activeOpacity={0.7}
        >
          <LinearGradient
            colors={["rgba(0,0,0,0.5)", "rgba(0,0,0,0.4)"]}
            style={styles.backButtonGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            <Ionicons name="chevron-back" size={26} color="#ffffff" />
            <Text style={styles.backButtonText}>Zurück</Text>
          </LinearGradient>
        </TouchableOpacity>
      </View>

      <Animated.ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        scrollEventThrottle={16}
        onScroll={Animated.event(
          [{ nativeEvent: { contentOffset: { y: scrollY } } }],
          { useNativeDriver: false }
        )}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        <View style={styles.contentContainer}>
          {renderStats()}
          {renderTabBar()}
          {renderContent()}
        </View>
      </Animated.ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F7F9FC",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: "#8F9BB3",
  },
  scrollContent: {
    paddingTop: HEADER_MAX_HEIGHT,
    paddingBottom: 20,
  },
  contentContainer: {
    padding: 16,
  },
  headerContainer: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    overflow: "hidden",
    zIndex: 10,
  },
  headerGradient: {
    height: "100%",
    width: "100%",
  },
  headerContent: {
    padding: 16,
    paddingTop: Platform.OS === "ios" ? 48 : 16,
  },
  userHeaderInfo: {
    flexDirection: "row",
    alignItems: "center",
  },
  userHeaderDetails: {
    marginLeft: 16,
    flex: 1,
  },
  userMetaContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    marginTop: 8,
    gap: 8,
  },
  metaItem: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  metaItemText: {
    fontSize: 12,
    color: "#fff",
    marginLeft: 4,
  },
  subscriptionContainer: {
    flexDirection: "row",
    marginTop: 12,
    alignItems: "center",
  },
  subscriptionBadge: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 16,
    marginRight: 8,
  },
  subscriptionText: {
    fontSize: 12,
    fontWeight: "500",
    color: "#fff",
  },
  goalBadge: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  goalText: {
    fontSize: 12,
    color: "#fff",
    marginLeft: 4,
  },
  headerTitleContainer: {
    position: "absolute",
    top: Platform.OS === "ios" ? 48 : 16,
    left: 0,
    right: 0,
    alignItems: "center",
    zIndex: 5,
    flexDirection: "row",
    justifyContent: "center",
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#2E3A59",
  },
  miniPremiumBadge: {
    backgroundColor: "#FFCB66",
    borderRadius: 10,
    width: 16,
    height: 16,
    justifyContent: "center",
    alignItems: "center",
    marginLeft: 4,
  },
  backButton: {
    marginLeft: 10,
    marginTop: Platform.OS === 'ios' ? 10 : 0,
    borderRadius: 20,
    overflow: 'hidden', // Important to keep gradient within bounds
    elevation: 3,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 3,
  },
  backButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 14,
    borderRadius: 20,
  },
  backButtonText: {
    color: '#ffffff',
    fontSize: 15,
    fontWeight: '600',
    marginLeft: 4,
  },
  userAvatarContainer: {
    position: "relative",
  },
  userAvatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
  },
  placeholderAvatar: {
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    justifyContent: "center",
    alignItems: "center",
  },
  userInitial: {
    fontSize: 32,
    fontWeight: "bold",
    color: "#fff",
  },
  premiumBadge: {
    position: "absolute",
    top: 0,
    right: 0,
    backgroundColor: "#FFCB66",
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 2,
    borderColor: "#FFFFFF",
  },
  userName: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#fff",
    marginBottom: 4,
  },
  userEmail: {
    fontSize: 14,
    color: "rgba(255, 255, 255, 0.8)",
    marginBottom: 8,
  },
  statsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    backgroundColor: "#fff",
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  statItem: {
    alignItems: "center",
    flex: 1,
  },
  statValue: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#6B8CFF",
  },
  statLabel: {
    fontSize: 12,
    color: "#8F9BB3",
    marginTop: 4,
  },
  tabBar: {
    flexDirection: "row",
    backgroundColor: "#fff",
    borderRadius: 16,
    marginBottom: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "center",
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: "#6B8CFF",
  },
  tabText: {
    fontSize: 14,
    fontWeight: "500",
    color: "#8F9BB3",
    marginLeft: 4,
  },
  activeTabText: {
    color: "#6B8CFF",
  },
  calendarContainer: {
    marginBottom: 16,
    borderRadius: 12,
    overflow: 'hidden',
    backgroundColor: '#fff',
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  selectedDateHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    paddingHorizontal: 8,
    paddingVertical: 8,
    backgroundColor: '#fff',
    borderRadius: 8,
  },
  selectedDateText: {
    marginLeft: 8,
    fontSize: 16,
    color: "#2E3A59",
    fontWeight: '500',
  },
  workoutsContainer: {
    flex: 1,
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionHeaderRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: "#2E3A59",
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  addButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6B8CFF',
    marginLeft: 4,
  },
  workoutList: {
    flex: 1,
  },
  emptyListContainer: {
    padding: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyListText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#8F9BB3',
    marginTop: 16,
  },
  emptyListSubText: {
    fontSize: 14,
    color: '#8F9BB3',
    textAlign: 'center',
    marginTop: 8,
  },
  workoutSelectorContainer: {
    flex: 1,
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  searchBarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F7F9FC',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 10,
    marginBottom: 16,
  },
  searchBarInput: {
    flex: 1,
    fontSize: 16,
    marginLeft: 8,
    color: '#2E3A59',
  },
  actionButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  cancelButton: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: "#F7F9FC",
    borderRadius: 12,
  },
  cancelButtonText: {
    fontSize: 14,
    fontWeight: "500",
    color: "#8F9BB3",
  },
  assignButton: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: "#6B8CFF",
    borderRadius: 12,
  },
  assignButtonText: {
    fontSize: 14,
    fontWeight: "500",
    color: "#fff",
  },
  disabledButton: {
    backgroundColor: '#C5CEE0',
  },
  selectableWorkout: {
    flexDirection: "row",
    alignItems: "center",
    padding: 12,
    borderRadius: 12,
    backgroundColor: "#F7F9FC",
    marginBottom: 8,
  },
  selectedWorkout: {
    backgroundColor: "#E4E9F2",
  },
  selectableWorkoutContent: {
    flex: 1,
  },
  selectableWorkoutName: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#2E3A59",
  },
  selectableWorkoutDetails: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 4,
  },
  selectableDetailItem: {
    flexDirection: "row",
    alignItems: "center",
    marginRight: 8,
  },
  selectableDetailText: {
    fontSize: 14,
    color: "#8F9BB3",
    marginLeft: 4,
  },
  selectableDifficultyBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  selectableDifficultyText: {
    fontSize: 12,
    fontWeight: "500",
    color: "#fff",
  },
  checkBox: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: "#8F9BB3",
    justifyContent: "center",
    alignItems: "center",
  },
  checkBoxSelected: {
    backgroundColor: "#6B8CFF",
    borderColor: "#6B8CFF",
  },
  assignedWorkout: {
    flexDirection: "row",
    alignItems: "center",
    padding: 12,
    borderRadius: 12,
    backgroundColor: "#F7F9FC",
    marginBottom: 8,
  },
  completionCheckbox: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#8F9BB3',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  completionCheckboxChecked: {
    backgroundColor: '#4CAF50',
    borderColor: '#4CAF50',
  },
  assignedWorkoutContent: {
    flex: 1,
  },
  assignedWorkoutName: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#2E3A59",
  },
  completedWorkoutText: {
    color: "#8F9BB3",
    textDecorationLine: 'line-through',
  },
  assignedWorkoutDetails: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 4,
  },
  assignedDetailItem: {
    flexDirection: "row",
    alignItems: "center",
    marginRight: 8,
  },
  assignedDetailText: {
    fontSize: 14,
    color: "#8F9BB3",
    marginLeft: 4,
  },
  assignedDifficultyBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  assignedDifficultyText: {
    fontSize: 12,
    fontWeight: "500",
    color: "#fff",
  },
  removeButton: {
    padding: 8,
  },
  // Stats tab styles
  statsContent: {
    flex: 1,
  },
  statsCard: {
    backgroundColor: "#fff",
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  statsCardTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#2E3A59",
    marginBottom: 16,
  },
  progressChartContainer: {
    alignItems: 'center',
  },
  progressText: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#6B8CFF",
    marginBottom: 8,
  },
  progressBar: {
    width: '100%',
    height: 8,
    backgroundColor: '#F1F4FA',
    borderRadius: 4,
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#6B8CFF',
    borderRadius: 4,
  },
  progressSubText: {
    fontSize: 14,
    color: '#8F9BB3',
  },
  weightChartContainer: {
    alignItems: 'center',
  },
  currentWeight: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#6B8CFF",
  },
  weightDate: {
    fontSize: 14,
    color: '#8F9BB3',
    marginTop: 4,
  },
  emptyStatsContainer: {
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center', 
  },
  emptyStatsText: {
    fontSize: 14,
    color: '#8F9BB3',
    marginTop: 8,
  },
  activitySummary: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  activityMetric: {
    alignItems: 'center',
  },
  activityMetricValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2E3A59',
    marginTop: 8,
  },
  activityMetricLabel: {
    fontSize: 12,
    color: '#8F9BB3',
    marginTop: 4,
  },
  // History tab styles
  historyContent: {
    flex: 1,
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  historyItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 12,
    backgroundColor: "#F7F9FC",
    marginBottom: 8,
  },
  historyDate: {
    marginRight: 12,
  },
  historyDateText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: "#2E3A59",
  },
  historyTimeText: {
    fontSize: 12,
    color: "#8F9BB3",
  },
  historyContent: {
    flex: 1,
  },
  historyTitle: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#2E3A59",
  },
  historyStatus: {
    fontSize: 14,
    color: "#8F9BB3",
    marginTop: 4,
  },
  emptyHistoryContainer: {
    padding: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyHistoryText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#8F9BB3',
    marginTop: 16,
  },
  emptyHistorySubText: {
    fontSize: 14,
    color: '#8F9BB3',
    textAlign: 'center',
    marginTop: 8,
  },
  historyList: {
    flex: 1,
  },
  customHeaderButtonContainer: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? 60 : 20, // Account for iOS notch
    left: 16,
    zIndex: 1000,
    width: 100,
  },
  customBackButton: {
    borderRadius: 24,
    overflow: 'hidden',
    elevation: 5,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
  },
  // Weekly schedule styles
  scheduleContainer: {
    flex: 1,
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  scheduleHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  scheduleTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: "#2E3A59",
  },
  clearAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  clearAllButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FF4B4B',
    marginLeft: 4,
  },
  scheduleContent: {
    flex: 1,
  },
  dayScheduleContainer: {
    marginBottom: 16,
  },
  dayHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  dayName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: "#2E3A59",
  },
  addDayWorkoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dayWorkoutsList: {
    backgroundColor: '#F7F9FC',
    borderRadius: 12,
    padding: 8,
  },
  noWorkoutsText: {
    fontSize: 14,
    color: '#8F9BB3',
    textAlign: 'center',
    padding: 8,
  },
  weeklyWorkoutItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    borderRadius: 8,
    backgroundColor: '#fff',
    marginBottom: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  weeklyWorkoutContent: {
    flex: 1,
  },
  weeklyWorkoutName: {
    fontSize: 14,
    fontWeight: 'bold',
    color: "#2E3A59",
  },
  weeklyWorkoutDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  weeklyWorkoutDetail: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 8,
  },
  weeklyWorkoutDetailText: {
    fontSize: 12,
    color: '#8F9BB3',
    marginLeft: 4,
  },
  weeklyWorkoutRemove: {
    padding: 4,
  },
  scheduleActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  saveScheduleButton: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: "#6B8CFF",
    borderRadius: 12,
  },
  saveScheduleButtonText: {
    fontSize: 14,
    fontWeight: "500",
    color: "#fff",
  },
  applyScheduleButton: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: "#4CAF50",
    borderRadius: 12,
  },
  applyScheduleButtonText: {
    fontSize: 14,
    fontWeight: "500",
    color: "#fff",
  },
  schedulePickerContainer: {
    flex: 1,
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  schedulePickerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  schedulePickerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: "#2E3A59",
  },
  closePickerButton: {
    padding: 4,
  },
  scheduleWorkoutItem: {
    flexDirection: "row",
    alignItems: "center",
    padding: 12,
    borderRadius: 12,
    backgroundColor: "#F7F9FC",
    marginBottom: 8,
  },
  scheduleWorkoutContent: {
    flex: 1,
  },
  scheduleWorkoutName: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#2E3A59",
  },
  scheduleWorkoutDetails: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 4,
  },
  scheduleWorkoutDetail: {
    flexDirection: "row",
    alignItems: "center",
    marginRight: 8,
  },
  scheduleWorkoutDetailText: {
    fontSize: 14,
    color: "#8F9BB3",
    marginLeft: 4,
  },
  scheduleDifficultyBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  scheduleDifficultyText: {
    fontSize: 12,
    fontWeight: "500",
    color: "#fff",
  },
});

export default UserDetailPage;
import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Animated,
  Dimensions,
  ImageBackground,
  TextInput,
  Platform,
  StatusBar,
  LayoutAnimation,
  UIManager
} from 'react-native';
import { Stack, router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { supabase } from '../../lib/supabase';

const { width, height } = Dimensions.get('window');

// Enable LayoutAnimation for Android
if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}

interface WellnessMetric {
  id: number;
  name: string;
  icon: string;
  value: number;
  unit: string;
  color: string[];
}

interface WellnessActivity {
  id: number;
  title: string;
  description: string;
  duration: number;
  image: string;
  categoryId: number;
}

interface WellnessCategory {
  id: number;
  name: string;
  icon: string;
  color: string[];
}

export default function WellnessScreen() {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [metrics, setMetrics] = useState<WellnessMetric[]>([
    { id: 1, name: 'Schlaf', icon: 'moon-outline', value: 7.5, unit: 'Std', color: ['#9DCEFF', '#92A3FD'] },
    { id: 2, name: 'Wasser', icon: 'water-outline', value: 2.1, unit: 'L', color: ['#C58BF2', '#EEA4CE'] },
    { id: 3, name: 'Schritte', icon: 'footsteps-outline', value: 6243, unit: '', color: ['#92A3FD', '#9DCEFF'] },
    { id: 4, name: 'Stress', icon: 'pulse-outline', value: 3.5, unit: '/10', color: ['#EEA4CE', '#C58BF2'] },
  ]);
  
  const [categories, setCategories] = useState<WellnessCategory[]>([
    { id: 1, name: 'Meditation', icon: 'leaf-outline', color: ['#92A3FD', '#9DCEFF'] },
    { id: 2, name: 'Schlaf', icon: 'moon-outline', color: ['#C58BF2', '#EEA4CE'] },
    { id: 3, name: 'Atmung', icon: 'fitness-outline', color: ['#EEA4CE', '#C58BF2'] },
    { id: 4, name: 'Stretching', icon: 'body-outline', color: ['#9DCEFF', '#92A3FD'] },
  ]);
  
  const [activities, setActivities] = useState<WellnessActivity[]>([
    { id: 1, title: 'Morgenmeditation', description: 'Beginne deinen Tag mit einer ruhigen Meditation', duration: 10, image: 'https://images.unsplash.com/photo-1545389336-cf090694435e?q=80&w=500', categoryId: 1 },
    { id: 2, title: 'Tiefes Atmen', description: 'Beruhige deinen Geist mit tiefen Atemübungen', duration: 5, image: 'https://images.unsplash.com/photo-1506126613408-eca07ce68773?q=80&w=500', categoryId: 3 },
    { id: 3, title: 'Schlafroutine', description: 'Verbessere deinen Schlaf mit dieser Routine', duration: 15, image: 'https://images.unsplash.com/photo-1511295742362-93749ae39365?q=80&w=500', categoryId: 2 },
    { id: 4, title: 'Ganzkörperdehnung', description: 'Sanfte Dehnübungen für den ganzen Körper', duration: 12, image: 'https://images.unsplash.com/photo-1552196563-55cd4e45efb3?q=80&w=500', categoryId: 4 },
  ]);
  
  const [visibleItems, setVisibleItems] = useState<{[key: string]: boolean}>({});
  const [mood, setMood] = useState<number | null>(null);
  const scrollY = useRef(new Animated.Value(0)).current;
  const headerHeight = scrollY.interpolate({
    inputRange: [0, 100],
    outputRange: [200, 70],
    extrapolate: 'clamp',
  });
  
  const headerOpacity = scrollY.interpolate({
    inputRange: [0, 60, 90],
    outputRange: [1, 0.3, 0],
    extrapolate: 'clamp',
  });
  
  const titleOpacity = scrollY.interpolate({
    inputRange: [0, 30, 60],
    outputRange: [0, 0.7, 1],
    extrapolate: 'clamp',
  });

  // Animation values
  const tabIndicator = useRef(new Animated.Value(0)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const chartHeight = useRef([...Array(7)].map(() => new Animated.Value(0))).current;

  // Set up staggered animations for items to appear
  useEffect(() => {
    const timeout = setTimeout(() => {
      LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
      setVisibleItems({
        moodSelector: true,
        metrics: true,
        categories: true,
        activities: true,
        trackers: true,
        insights: true
      });
      
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true
      }).start();
    }, 100);
    
    return () => clearTimeout(timeout);
  }, [activeTab]);
  
  // Run chart animations when insights tab is selected
  useEffect(() => {
    if (activeTab === 'insights') {
      const data = [2.4, 1.8, 2.9, 2.0, 2.6, 1.6, 2.3];
      
      data.forEach((value, index) => {
        Animated.sequence([
          Animated.delay(300 + (index * 100)),
          Animated.timing(chartHeight[index], {
            toValue: (value / 3) * 120,
            duration: 500,
            useNativeDriver: false
          })
        ]).start();
      });
    }
  }, [activeTab, visibleItems.insights]);

  useEffect(() => {
    const position = activeTab === 'dashboard' ? 0 : activeTab === 'tracker' ? 1 : 2;
    Animated.timing(tabIndicator, {
      toValue: position,
      duration: 300,
      useNativeDriver: false
    }).start();
  }, [activeTab]);

  const handleMoodSelect = (value: number) => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.spring);
    setMood(value);
  };

  const renderMoodSelector = () => {
    const moods = [
      { value: 1, icon: '☹️', label: 'Schlecht' },
      { value: 2, icon: '😕', label: 'Mäßig' },
      { value: 3, icon: '😐', label: 'Okay' },
      { value: 4, icon: '🙂', label: 'Gut' },
      { value: 5, icon: '😄', label: 'Großartig' },
    ];

    if (!visibleItems.moodSelector) return null;

    return (
      <Animated.View 
        style={[styles.moodContainer, { opacity: fadeAnim }]}
      >
        <Text style={styles.moodTitle}>Wie fühlst du dich heute?</Text>
        <View style={styles.moodOptions}>
          {moods.map((item) => (
            <TouchableOpacity
              key={item.value}
              style={[
                styles.moodOption,
                mood === item.value && styles.moodOptionSelected
              ]}
              onPress={() => handleMoodSelect(item.value)}
            >
              <Animated.View style={{
                transform: [{ scale: mood === item.value ? 1.1 : 1 }]
              }}>
                <Text style={styles.moodEmoji}>{item.icon}</Text>
                <Text style={[
                  styles.moodLabel,
                  mood === item.value && styles.moodLabelSelected
                ]}>
                  {item.label}
                </Text>
              </Animated.View>
            </TouchableOpacity>
          ))}
        </View>
      </Animated.View>
    );
  };

  const renderMetricItem = (metric: WellnessMetric, index: number) => {
    return (
      <Animated.View
        key={metric.id}
        style={[
          styles.metricCard,
          { 
            opacity: fadeAnim,
            transform: [{ 
              translateY: fadeAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [20, 0]
              }) 
            }]
          }
        ]}
      >
        <LinearGradient
          colors={metric.color}
          style={styles.metricGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          <Ionicons name={metric.icon as any} size={24} color="#fff" />
          <Text style={styles.metricValue}>
            {metric.value}{metric.unit}
          </Text>
          <Text style={styles.metricName}>{metric.name}</Text>
        </LinearGradient>
      </Animated.View>
    );
  };

  const renderActivityItem = (activity: WellnessActivity, index: number) => {
    return (
      <Animated.View
        key={activity.id}
        style={[
          styles.activityCard,
          { 
            opacity: fadeAnim,
            transform: [{ 
              translateX: fadeAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [index % 2 === 0 ? -20 : 20, 0]
              }) 
            }]
          }
        ]}
      >
        <TouchableOpacity 
          activeOpacity={0.9} 
          onPress={() => router.push(`/meditation/${activity.id}`)}
          style={{flex: 1}}
        >
          <ImageBackground
            source={{ uri: activity.image }}
            style={styles.activityImage}
            imageStyle={styles.activityImageStyle}
          >
            <BlurView intensity={30} style={styles.activityOverlay}>
              <View style={styles.activityContent}>
                <View>
                  <Text style={styles.activityTitle}>{activity.title}</Text>
                  <Text style={styles.activityDescription}>{activity.description}</Text>
                </View>
                <View style={styles.activityFooter}>
                  <View style={styles.activityDuration}>
                    <Ionicons name="time-outline" size={16} color="#fff" />
                    <Text style={styles.activityDurationText}>{activity.duration} Min</Text>
                  </View>
                  <TouchableOpacity 
                    style={styles.startButton}
                    onPress={() => router.push(`/meditation/${activity.id}`)}
                  >
                    <Text style={styles.startButtonText}>Starten</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </BlurView>
          </ImageBackground>
        </TouchableOpacity>
      </Animated.View>
    );
  };

  const renderDashboard = () => {
    return (
      <ScrollView 
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        scrollEventThrottle={16}
        onScroll={Animated.event(
          [{ nativeEvent: { contentOffset: { y: scrollY } } }],
          { useNativeDriver: false }
        )}
      >
        {renderMoodSelector()}
        
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Deine Übersicht</Text>
          <TouchableOpacity style={styles.seeAllButton}>
            <Text style={styles.seeAllText}>Alle anzeigen</Text>
          </TouchableOpacity>
        </View>
        
        {visibleItems.metrics && (
          <View style={styles.metricsContainer}>
            {metrics.map((metric, index) => renderMetricItem(metric, index))}
          </View>
        )}
        
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Kategorien</Text>
        </View>
        
        {visibleItems.categories && (
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.categoriesContainer}
          >
            {categories.map((category, index) => (
              <Animated.View
                key={category.id}
                style={[
                  styles.categoryCard,
                  { 
                    opacity: fadeAnim,
                    transform: [{ 
                      scale: fadeAnim.interpolate({
                        inputRange: [0, 1],
                        outputRange: [0.9, 1]
                      }) 
                    }]
                  }
                ]}
              >
                <LinearGradient
                  colors={category.color}
                  style={styles.categoryGradient}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                >
                  <Ionicons name={category.icon as any} size={28} color="#fff" />
                  <Text style={styles.categoryName}>{category.name}</Text>
                </LinearGradient>
              </Animated.View>
            ))}
          </ScrollView>
        )}
        
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Empfohlene Aktivitäten</Text>
          <TouchableOpacity style={styles.seeAllButton}>
            <Text style={styles.seeAllText}>Alle anzeigen</Text>
          </TouchableOpacity>
        </View>
        
        {visibleItems.activities && activities.map((activity, index) => 
          renderActivityItem(activity, index)
        )}
        
        <View style={{ height: 50 }} />
      </ScrollView>
    );
  };

  const renderTracker = () => {
    if (!visibleItems.trackers) {
      return (
        <View style={styles.loaderContainer}>
          <Animated.View style={{ opacity: fadeAnim }}>
            <Ionicons name="water-outline" size={40} color="#92A3FD" />
            <Text style={styles.loadingText}>Lädt Tracker...</Text>
          </Animated.View>
        </View>
      );
    }

    return (
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={[styles.scrollContent, { alignItems: 'center', paddingTop: 20 }]}
      >
        <Animated.View 
          style={[
            styles.trackerCard,
            { 
              opacity: fadeAnim,
              transform: [{ 
                scale: fadeAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0.9, 1]
                }) 
              }]
            }
          ]}
        >
          <LinearGradient
            colors={['#92A3FD', '#9DCEFF']}
            style={styles.trackerGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            <Text style={styles.trackerTitle}>Wassertracker</Text>
            <View style={styles.waterTracker}>
              {[1, 2, 3, 4, 5, 6, 7, 8].map((item) => (
                <Animated.View
                  key={item}
                  style={[
                    styles.waterGlass, 
                    item <= 5 ? styles.waterGlassFilled : {},
                    { 
                      opacity: fadeAnim,
                      transform: [{ 
                        translateY: fadeAnim.interpolate({
                          inputRange: [0, 1],
                          outputRange: [20, 0]
                        }) 
                      }]
                    }
                  ]}
                >
                  <Ionicons 
                    name="water-outline" 
                    size={24} 
                    color={item <= 5 ? "#fff" : "rgba(255,255,255,0.6)"} 
                  />
                </Animated.View>
              ))}
            </View>
            <Text style={styles.waterTrackerText}>5 von 8 Gläsern (2,1L / 3,0L)</Text>
            <TouchableOpacity style={styles.waterButton}>
              <Ionicons name="add-outline" size={24} color="#fff" />
              <Text style={styles.waterButtonText}>Glas hinzufügen</Text>
            </TouchableOpacity>
          </LinearGradient>
        </Animated.View>
        
        <Animated.View 
          style={[
            styles.trackerCard,
            { 
              opacity: fadeAnim,
              transform: [{ 
                scale: fadeAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0.9, 1]
                }) 
              }]
            }
          ]}
        >
          <LinearGradient
            colors={['#C58BF2', '#EEA4CE']}
            style={styles.trackerGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            <Text style={styles.trackerTitle}>Schlafprotokoll</Text>
            <View style={styles.sleepTracker}>
              <View style={styles.sleepInput}>
                <Text style={styles.sleepLabel}>Schlafenszeit</Text>
                <View style={styles.timeInput}>
                  <TextInput 
                    style={styles.timeInputText} 
                    value="23:30" 
                    keyboardType="number-pad"
                  />
                  <Ionicons name="time-outline" size={22} color="#fff" />
                </View>
              </View>
              <View style={styles.sleepInput}>
                <Text style={styles.sleepLabel}>Aufwachzeit</Text>
                <View style={styles.timeInput}>
                  <TextInput 
                    style={styles.timeInputText} 
                    value="07:00" 
                    keyboardType="number-pad"
                  />
                  <Ionicons name="time-outline" size={22} color="#fff" />
                </View>
              </View>
            </View>
            <Text style={styles.sleepQualityTitle}>Schlafqualität</Text>
            <View style={styles.sleepQuality}>
              {[1, 2, 3, 4, 5].map((rating) => (
                <TouchableOpacity key={rating} style={styles.starContainer}>
                  <Ionicons 
                    name={rating <= 4 ? "star" : "star-outline"} 
                    size={28} 
                    color="#fff" 
                  />
                </TouchableOpacity>
              ))}
            </View>
            <TouchableOpacity style={styles.sleepButton}>
              <Text style={styles.sleepButtonText}>Speichern</Text>
            </TouchableOpacity>
          </LinearGradient>
        </Animated.View>
        
        <View style={{ height: 100 }} />
      </ScrollView>
    );
  };

  const renderInsights = () => {
    if (!visibleItems.insights) {
      return (
        <View style={styles.loaderContainer}>
          <Animated.View style={{ opacity: fadeAnim }}>
            <Ionicons name="analytics-outline" size={40} color="#92A3FD" />
            <Text style={styles.loadingText}>Lädt Insights...</Text>
          </Animated.View>
        </View>
      );
    }

    return (
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={[styles.scrollContent, { paddingTop: 20 }]}
      >
        <Animated.View
          style={[
            styles.insightsHeader,
            { 
              opacity: fadeAnim,
              transform: [{ 
                translateY: fadeAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: [20, 0]
                }) 
              }]
            }
          ]}
        >
          <Text style={styles.insightsTitle}>Wöchentliche Übersicht</Text>
          <Text style={styles.insightsSubtitle}>13. Mai - 19. Mai</Text>
        </Animated.View>
        
        <Animated.View
          style={[
            styles.insightsCard,
            { 
              opacity: fadeAnim,
              transform: [{ 
                scale: fadeAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0.95, 1]
                }) 
              }]
            }
          ]}
        >
          <View style={styles.insightsCardHeader}>
            <View>
              <Text style={styles.insightsCardTitle}>Wasseraufnahme</Text>
              <Text style={styles.insightsCardSubtitle}>Durchschnittlich 2,3L / Tag</Text>
            </View>
            <View style={styles.trendBadge}>
              <Ionicons name="trending-up-outline" size={16} color="#fff" />
              <Text style={styles.trendText}>+12%</Text>
            </View>
          </View>
          
          <View style={styles.chartContainer}>
            {[2.4, 1.8, 2.9, 2.0, 2.6, 1.6, 2.3].map((value, index) => (
              <View key={index} style={styles.chartColumn}>
                <Animated.View 
                  style={[
                    styles.chartBar, 
                    { height: chartHeight[index] }
                  ]}
                >
                  <LinearGradient
                    colors={['#92A3FD', '#9DCEFF']}
                    style={styles.chartBarGradient}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 0, y: 1 }}
                  />
                </Animated.View>
                <Text style={styles.chartLabel}>{['M', 'D', 'M', 'D', 'F', 'S', 'S'][index]}</Text>
                <Text style={styles.chartValue}>{value}L</Text>
              </View>
            ))}
          </View>
        </Animated.View>
        
        <Animated.View
          style={[
            styles.insightsCard,
            { 
              opacity: fadeAnim,
              transform: [{ 
                scale: fadeAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0.95, 1]
                }) 
              }]
            }
          ]}
        >
          <View style={styles.insightsCardHeader}>
            <View>
              <Text style={styles.insightsCardTitle}>Stimmungstracker</Text>
              <Text style={styles.insightsCardSubtitle}>Letzte 7 Tage</Text>
            </View>
          </View>
          
          <View style={styles.moodChart}>
            {[3, 4, 5, 3, 4, 2, 4].map((moodValue, index) => (
              <Animated.View
                key={index}
                style={[
                  styles.moodChartItem,
                  { opacity: fadeAnim }
                ]}
              >
                <Text style={styles.moodChartEmoji}>{
                  moodValue === 1 ? '☹️' : 
                  moodValue === 2 ? '😕' : 
                  moodValue === 3 ? '😐' : 
                  moodValue === 4 ? '🙂' : '😄'
                }</Text>
                <Text style={styles.chartLabel}>{['M', 'D', 'M', 'D', 'F', 'S', 'S'][index]}</Text>
              </Animated.View>
            ))}
          </View>
          
          <View style={styles.moodSummary}>
            <Text style={styles.moodSummaryText}>
              Deine Stimmung war überwiegend <Text style={styles.highlightText}>gut</Text> in dieser Woche.
            </Text>
          </View>
        </Animated.View>
        
        <View style={{ height: 100 }} />
      </ScrollView>
    );
  };

  const setActiveTabWithAnimation = (tab: string) => {
    // Fade out current content
    Animated.timing(fadeAnim, {
      toValue: 0,
      duration: 150,
      useNativeDriver: true
    }).start(() => {
      // Change tab
      setActiveTab(tab);
      // Reset visibilities
      setVisibleItems({});
      
      // Schedule layout animations and fade in for the new content
      setTimeout(() => {
        LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
        setVisibleItems({
          moodSelector: true,
          metrics: true,
          categories: true,
          activities: true,
          trackers: true,
          insights: true
        });
        
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true
        }).start();
      }, 50);
    });
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" />

      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />

      <Animated.View style={[styles.header, { height: headerHeight }]}>
        <LinearGradient
          colors={['#92A3FD', '#9DCEFF']}
          style={styles.headerGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          <Animated.View style={[styles.headerContent, { opacity: headerOpacity }]}>
            <Text style={styles.greeting}>Hallo, Anna!</Text>
            <Text style={styles.headerText}>Wie geht es dir heute?</Text>
          </Animated.View>
          
          <Animated.Text style={[styles.headerTitle, { opacity: titleOpacity }]}>
            Wellness
          </Animated.Text>
          
          <View style={styles.tabsContainer}>
            <TouchableOpacity
              style={styles.tab}
              onPress={() => setActiveTabWithAnimation('dashboard')}
            >
              <Text style={[
                styles.tabText,
                activeTab === 'dashboard' && styles.activeTabText
              ]}>
                Dashboard
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={styles.tab}
              onPress={() => setActiveTabWithAnimation('tracker')}
            >
              <Text style={[
                styles.tabText,
                activeTab === 'tracker' && styles.activeTabText
              ]}>
                Tracker
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={styles.tab}
              onPress={() => setActiveTabWithAnimation('insights')}
            >
              <Text style={[
                styles.tabText,
                activeTab === 'insights' && styles.activeTabText
              ]}>
                Insights
              </Text>
            </TouchableOpacity>
            
            <Animated.View
              style={[
                styles.tabIndicator,
                {
                  left: tabIndicator.interpolate({
                    inputRange: [0, 1, 2],
                    outputRange: ['0%', '33.33%', '66.66%'],
                  }),
                },
              ]}
            />
          </View>
        </LinearGradient>
      </Animated.View>

      {activeTab === 'dashboard' && renderDashboard()}
      {activeTab === 'tracker' && renderTracker()}
      {activeTab === 'insights' && renderInsights()}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    width: '100%',
    overflow: 'hidden',
  },
  headerGradient: {
    flex: 1,
    paddingTop: Platform.OS === 'ios' ? 50 : 40,
    paddingHorizontal: 20,
  },
  headerContent: {
    marginTop: 10,
  },
  headerTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#fff',
    position: 'absolute',
    top: Platform.OS === 'ios' ? 55 : 45,
    left: 20,
  },
  greeting: {
    fontSize: 26,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 6,
  },
  headerText: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    marginBottom: 20,
  },
  tabsContainer: {
    flexDirection: 'row',
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 50,
  },
  tab: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  tabText: {
    fontSize: 15,
    fontWeight: '500',
    color: 'rgba(255, 255, 255, 0.7)',
  },
  activeTabText: {
    color: '#fff',
    fontWeight: '600',
  },
  tabIndicator: {
    position: 'absolute',
    bottom: 0,
    width: '33.33%',
    height: 3,
    backgroundColor: '#fff',
    borderTopLeftRadius: 3,
    borderTopRightRadius: 3,
  },
  scrollView: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  scrollContent: {
    paddingHorizontal: 16,
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 50,
  },
  loadingText: {
    textAlign: 'center',
    fontSize: 16,
    color: '#92A3FD',
    marginTop: 10,
  },
  moodContainer: {
    backgroundColor: '#fff',
    borderRadius: 20,
    padding: 20,
    marginTop: 20,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  moodTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 15,
    textAlign: 'center',
  },
  moodOptions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  moodOption: {
    alignItems: 'center',
    padding: 10,
    borderRadius: 12,
  },
  moodOptionSelected: {
    backgroundColor: 'rgba(146, 163, 253, 0.1)',
  },
  moodEmoji: {
    fontSize: 32,
    marginBottom: 6,
  },
  moodLabel: {
    fontSize: 12,
    color: '#666',
    fontWeight: '500',
  },
  moodLabelSelected: {
    color: '#92A3FD',
    fontWeight: '600',
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 25,
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  seeAllButton: {
    padding: 5,
  },
  seeAllText: {
    color: '#92A3FD',
    fontWeight: '500',
    fontSize: 14,
  },
  metricsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  metricCard: {
    width: '48%',
    borderRadius: 20,
    overflow: 'hidden',
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 2,
  },
  metricGradient: {
    padding: 20,
    height: 120,
    alignItems: 'center',
    justifyContent: 'center',
  },
  metricValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginTop: 8,
    marginBottom: 4,
  },
  metricName: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.9)',
    fontWeight: '500',
  },
  categoriesContainer: {
    paddingVertical: 10,
    marginBottom: 10,
  },
  categoryCard: {
    marginRight: 15,
    borderRadius: 20,
    overflow: 'hidden',
    width: 120,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 2,
  },
  categoryGradient: {
    padding: 20,
    height: 120,
    alignItems: 'center',
    justifyContent: 'center',
  },
  categoryName: {
    fontSize: 14,
    fontWeight: '500',
    color: '#fff',
    marginTop: 10,
  },
  activityCard: {
    borderRadius: 20,
    overflow: 'hidden',
    marginBottom: 15,
    height: 160,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 2,
  },
  activityImage: {
    width: '100%',
    height: '100%',
  },
  activityImageStyle: {
    borderRadius: 20,
  },
  activityOverlay: {
    flex: 1,
    padding: 20,
    justifyContent: 'flex-end',
    borderRadius: 20,
  },
  activityContent: {
    justifyContent: 'space-between',
    flex: 1,
  },
  activityTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 6,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 3,
  },
  activityDescription: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.9)',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 3,
  },
  activityFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 20,
  },
  activityDuration: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  activityDurationText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 6,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  startButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.5)',
  },
  startButtonText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 14,
  },
  trackerCard: {
    width: '100%',
    marginBottom: 20,
    borderRadius: 24,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  trackerGradient: {
    padding: 20,
  },
  trackerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 20,
  },
  waterTracker: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  waterGlass: {
    width: '23%',
    height: 60,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10,
  },
  waterGlassFilled: {
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.5)',
  },
  waterTrackerText: {
    color: '#fff',
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
  },
  waterButton: {
    flexDirection: 'row',
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    paddingVertical: 12,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.5)',
  },
  waterButtonText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 16,
    marginLeft: 8,
  },
  sleepTracker: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  sleepInput: {
    width: '48%',
  },
  sleepLabel: {
    color: 'rgba(255, 255, 255, 0.9)',
    fontSize: 14,
    marginBottom: 8,
  },
  timeInput: {
    flexDirection: 'row',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 10,
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  timeInputText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
  },
  sleepQualityTitle: {
    color: 'rgba(255, 255, 255, 0.9)',
    fontSize: 14,
    marginBottom: 8,
  },
  sleepQuality: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 0,
  },
  starContainer: {
    padding: 5,
  },
  sleepButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    paddingVertical: 12,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 20,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.5)',
  },
  sleepButtonText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 16,
  },
  insightsHeader: {
    marginBottom: 20,
  },
  insightsTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#333',
  },
  insightsSubtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  insightsCard: {
    backgroundColor: '#fff',
    borderRadius: 20,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  insightsCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 20,
  },
  insightsCardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  insightsCardSubtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  trendBadge: {
    flexDirection: 'row',
    backgroundColor: '#92A3FD',
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 6,
    alignItems: 'center',
  },
  trendText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 12,
    marginLeft: 4,
  },
  chartContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    height: 170,
  },
  chartColumn: {
    alignItems: 'center',
    width: 30,
    height: 170,
    justifyContent: 'flex-end',
  },
  chartBar: {
    width: '100%',
    borderRadius: 8,
    overflow: 'hidden',
  },
  chartBarGradient: {
    width: '100%',
    height: '100%',
  },
  chartLabel: {
    marginTop: 8,
    fontSize: 12,
    color: '#666',
    fontWeight: '500',
  },
  chartValue: {
    marginTop: 4,
    fontSize: 10,
    color: '#999',
  },
  moodChart: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  moodChartItem: {
    alignItems: 'center',
  },
  moodChartEmoji: {
    fontSize: 24,
    marginBottom: 8,
  },
  moodSummary: {
    backgroundColor: 'rgba(146, 163, 253, 0.1)',
    borderRadius: 12,
    padding: 15,
  },
  moodSummaryText: {
    color: '#555',
    fontSize: 15,
    textAlign: 'center',
  },
  highlightText: {
    color: '#92A3FD',
    fontWeight: '600',
  },
});


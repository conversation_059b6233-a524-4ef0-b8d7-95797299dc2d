import React, { useState, useRef, useEffect } from 'react';
import { 
  Pressable, 
  StyleSheet, 
  View, 
  GestureResponderEvent, 
  Platform, 
  Text, 
  Dimensions, 
  Animated,
  useWindowDimensions
} from 'react-native';
import Ionicons from '@expo/vector-icons/Ionicons';
import { useSettings } from '../context/SettingsContext';

type AddButtonProps = {
  onPress?: (event?: GestureResponderEvent) => void;
  focused: boolean;
};

/**
 * Enhanced AddButton component with responsive menu
 * Fixed to ensure menu items always stay within screen boundaries
 */
const AddButtonMenu: React.FC<AddButtonProps> = ({ onPress, focused }) => {
  const [isOpen, setIsOpen] = useState(false);
  const { triggerHaptic } = useSettings();
  const menuOpenAnim = useRef(new Animated.Value(0)).current;
  const buttonScaleAnim = useRef(new Animated.Value(1)).current;
  const backdropOpacity = useRef(new Animated.Value(0)).current;
  
  // Get accurate screen dimensions using hook instead of static import
  const { width: screenWidth } = useWindowDimensions();
  
  // Individual animations for each menu option
  const leftOptionAnim = useRef(new Animated.Value(0)).current;
  const centerOptionAnim = useRef(new Animated.Value(0)).current;
  const rightOptionAnim = useRef(new Animated.Value(0)).current;
  
  // Menu options data with clear naming
  const menuOptions = [
    { name: "Workout", icon: "fitness", color: "#FF686B", animation: leftOptionAnim },
    { name: "Nutrition", icon: "nutrition", color: "#FF8E6E", animation: centerOptionAnim },
    { name: "Health", icon: "heart", color: "#FF758F", animation: rightOptionAnim }
  ];

  // Handle button press with improved animation sequence
  const handlePress = (e?: GestureResponderEvent) => {
    triggerHaptic();
    setIsOpen(prev => !prev);
    
    // Button press feedback animation
    Animated.sequence([
      Animated.timing(buttonScaleAnim, {
        toValue: 0.9,
        duration: 100,
        useNativeDriver: true
      }),
      Animated.spring(buttonScaleAnim, {
        toValue: 1,
        friction: 5,
        tension: 300,
        useNativeDriver: true
      })
    ]).start();
  };

  // Handle menu option selection
  const handleOptionPress = (option: string) => {
    triggerHaptic();
    console.log(`${option} option selected`);
    
    // Close menu after selection with a slight delay
    setTimeout(() => {
      setIsOpen(false);
    }, 100);
  };

  // Control animations when menu state changes
  useEffect(() => {
    // Prepare animation sequence
    const animationSequence = [];
    
    if (isOpen) {
      // Open animations
      animationSequence.push(
        // Backdrop fade in
        Animated.timing(backdropOpacity, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true
        }),
        
        // Menu opening animation
        Animated.timing(menuOpenAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true
        })
      );
      
      // Staggered option animations
      animationSequence.push(
        Animated.stagger(80, [
          Animated.spring(leftOptionAnim, {
            toValue: 1,
            friction: 6,
            tension: 300,
            useNativeDriver: true
          }),
          Animated.spring(centerOptionAnim, {
            toValue: 1,
            friction: 6,
            tension: 300,
            useNativeDriver: true
          }),
          Animated.spring(rightOptionAnim, {
            toValue: 1,
            friction: 6,
            tension: 300,
            useNativeDriver: true
          })
        ])
      );
    } else {
      // Close animations in reverse order
      animationSequence.push(
        // Options disappear first
        Animated.parallel([
          Animated.timing(leftOptionAnim, {
            toValue: 0,
            duration: 150,
            useNativeDriver: true
          }),
          Animated.timing(centerOptionAnim, {
            toValue: 0,
            duration: 150,
            useNativeDriver: true
          }),
          Animated.timing(rightOptionAnim, {
            toValue: 0,
            duration: 150,
            useNativeDriver: true
          })
        ]),
        
        // Then close the menu and fade out backdrop
        Animated.parallel([
          Animated.timing(menuOpenAnim, {
            toValue: 0,
            duration: 200,
            useNativeDriver: true
          }),
          Animated.timing(backdropOpacity, {
            toValue: 0,
            duration: 150,
            useNativeDriver: true
          })
        ])
      );
    }
    
    // Run animations in sequence
    Animated.sequence(animationSequence).start();
  }, [isOpen]);
  
  // Constants for sizing and spacing
  const BUTTON_SIZE = 60;
  const MENU_ITEM_SIZE = 50;
  const MENU_ITEM_SPACING = 15;
  const MENU_WIDTH = 220; // Fixed width that accommodates all items
  
  // FIXED: Improved positioning calculations to ensure items stay within screen bounds
  
  // Calculate safe horizontal spread - smaller on narrow screens
  // This ensures the most edge items never go outside the screen
  const tabBarWidth = screenWidth - 40; // Accounting for 20px padding on each side
  const centerPosition = tabBarWidth / 2;
  
  // Max spread that will fit within screen (with additional safety margin)
  const SAFETY_MARGIN = 20;
  const maxSpread = Math.min(
    (screenWidth / 2) - MENU_ITEM_SIZE - SAFETY_MARGIN, 
    90 // Maximum horizontal spread (reduced from 120 to be safer)
  );
  
  // Calculate positions for menu options
  const horizontalPositions = [-maxSpread, 0, maxSpread];
  
  // Get position transform for a menu option based on index and animation value
  const getMenuItemTransform = (animation: Animated.Value, index: number) => {
    const verticalDistance = index === 1 ? -90 : -70; // Middle item higher
    const horizontalOffset = horizontalPositions[index];
    
    return {
      opacity: animation,
      transform: [
        { scale: animation },
        // Vertical movement (up from button)
        { 
          translateY: animation.interpolate({
            inputRange: [0, 1],
            outputRange: [0, verticalDistance]
          }) 
        },
        // Horizontal movement (side to side)
        { 
          translateX: animation.interpolate({
            inputRange: [0, 1],
            outputRange: [0, horizontalOffset]
          }) 
        }
      ]
    };
  };

  return (
    <View style={styles.container}>
      {/* Backdrop overlay */}
      {isOpen && (
        <Animated.View 
          style={[
            styles.backdrop,
            { opacity: backdropOpacity }
          ]}
          pointerEvents={isOpen ? "auto" : "none"}
          onTouchEnd={() => setIsOpen(false)}
        />
      )}
      
      {/* Menu container - now using absolute positioning from center */}
      <View style={styles.menuContainer}>
        {menuOptions.map((option, index) => (
          <Animated.View 
            key={option.name}
            style={[
              styles.menuItemContainer,
              getMenuItemTransform(option.animation, index)
            ]}
          >
            {/* Option button */}
            <Pressable
              onPress={() => handleOptionPress(option.name)}
              style={({ pressed }) => [
                styles.menuButton,
                { 
                  backgroundColor: option.color,
                  transform: [{ scale: pressed ? 0.92 : 1 }],
                  shadowOpacity: pressed ? 0.1 : 0.25,
                }
              ]}
              android_ripple={{ color: 'rgba(255,255,255,0.3)', borderless: true }}
            >
              <Ionicons 
                name={`${option.icon}${index === 1 ? '' : '-outline'}`} 
                size={22} 
                color="#fff" 
              />
            </Pressable>
            
            {/* Option label */}
            <Animated.View style={styles.menuLabel}>
              <Text style={styles.menuLabelText}>
                {option.name}
              </Text>
            </Animated.View>
          </Animated.View>
        ))}
      </View>

      {/* Main add button */}
      <Animated.View style={[
        styles.buttonShadow,
        { transform: [{ scale: buttonScaleAnim }] }
      ]}>
        <Pressable
          onPress={handlePress}
          style={({ pressed }) => [
            styles.addButton,
            {
              backgroundColor: isOpen ? '#ff5277' : '#ff758f',
              transform: [{ scale: pressed ? 0.95 : 1 }],
              shadowOpacity: pressed ? 0.15 : 0.25,
              shadowRadius: pressed ? 3 : 6,
              elevation: pressed ? 4 : 8,
            }
          ]}
        >
          <Animated.View style={{
            transform: [{ 
              rotate: menuOpenAnim.interpolate({
                inputRange: [0, 1],
                outputRange: ['0deg', '45deg']
              }) 
            }]
          }}>
            <Ionicons name="add" size={32} color="#fff" />
          </Animated.View>
        </Pressable>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'flex-end',
    height: 100,
    width: '100%',
    position: 'relative',
  },
  backdrop: {
    position: 'absolute',
    top: -1000,
    left: -1000,
    right: -1000,
    bottom: -100,
    backgroundColor: 'rgba(0,0,0,0.3)',
    zIndex: 50
  },
  menuContainer: {
    position: 'absolute',
    bottom: 75,
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    height: 150,
    zIndex: 100,
    // No left/right positioning - center alignment is handled by parent View
  },
  menuItemContainer: {
    position: 'absolute',
    alignItems: 'center',
    justifyContent: 'center',
    // All items start from center position and animate out
  },
  menuButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 3,
    borderColor: 'rgba(255,255,255,0.25)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.25,
    shadowRadius: 5,
    elevation: 5,
  },
  menuLabel: {
    backgroundColor: 'rgba(255,255,255,0.95)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginTop: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3
  },
  menuLabelText: {
    color: '#333',
    fontSize: 12,
    fontWeight: '600'
  },
  buttonShadow: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.2,
    shadowRadius: 6,
    elevation: 8
  },
  addButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
    borderWidth: 4,
    borderColor: 'rgba(255,255,255,0.25)',
  }
});

export default AddButtonMenu;

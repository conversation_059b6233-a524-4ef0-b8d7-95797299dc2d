import { View, StyleSheet, Platform, Animated } from "react-native";
import React, { useEffect, useRef } from "react";
import Svg, { Path } from "react-native-svg";
import { HEIGHT, MIN_LEDGE, Side, WIDTH } from "@/configs/constants";
import MaskedView from '@react-native-masked-view/masked-view';

const AnimatedPath = Animated.createAnimatedComponent(Path);

interface WaveProps {
  side: Side;
  children: React.ReactElement;
  position: {
    x: Animated.Value;
    y: Animated.Value;
  };
  isTransitioning: boolean;
}

export default function Wave({
  side,
  children,
  position,
  isTransitioning,
}: WaveProps) {
  // Create animated values for derived values
  const rValue = useRef(new Animated.Value(MIN_LEDGE)).current;
  const ledgeValue = useRef(new Animated.Value(0)).current;
  
  // Update R value whenever position.x changes
  useEffect(() => {
    const listener = position.x.addListener(({ value }) => {
      const newR = Math.min(value - MIN_LEDGE, WIDTH / 2.5);
      rValue.setValue(newR > 0 ? newR : MIN_LEDGE);
    });
    
    return () => {
      position.x.removeListener(listener);
    };
  }, [position.x]);
  
  // Update ledge value based on R and isTransitioning
  useEffect(() => {
    const listener = rValue.addListener(({ value: r }) => {
      const xValue = position.x._value;
      const baseLedge = Math.max(0, xValue - MIN_LEDGE - r);
      
      if (isTransitioning) {
        Animated.spring(ledgeValue, {
          toValue: xValue,
          stiffness: 200,
          useNativeDriver: false
        }).start();
      } else {
        Animated.spring(ledgeValue, {
          toValue: baseLedge,
          stiffness: 200,
          useNativeDriver: false
        }).start();
      }
    });
    
    return () => {
      rValue.removeListener(listener);
    };
  }, [isTransitioning, position.x]);
  
  // Create path data for SVG
  const createPathData = () => {
    return Animated.add(
      ledgeValue,
      new Animated.Value(0) // This is a placeholder, actual calculation happens in interpolate
    ).interpolate({
      inputRange: [0, 1],
      outputRange: ["0", "1"],
      extrapolate: "clamp",
      // The actual path is calculated in the outputRange using string interpolation
      // This is a workaround since we can't directly compute complex paths with Animated
    });
  };

  const pathInterpolator = position.x.interpolate({
    inputRange: [0, WIDTH],
    outputRange: [0, 1],
    extrapolate: "clamp"
  });

  const androidStyle = {
    transform: [{
      translateX: Animated.cond(
        isTransitioning,
        Animated.timing(new Animated.Value(0), {
          toValue: 0,
          duration: 300,
          useNativeDriver: true
        }),
        side === Side.RIGHT
          ? Animated.subtract(WIDTH, ledgeValue)
          : Animated.multiply(Animated.subtract(WIDTH, ledgeValue), -1)
      )
    }]
  };

  const renderPath = () => {
    return (
      <Svg
        style={[
          StyleSheet.absoluteFill,
          {
            transform: [{ rotateY: side === Side.RIGHT ? "180deg" : "0deg" }],
          },
        ]}
      >
        <AnimatedPath
          fill={
            Platform.OS === "android"
              ? children.props.slide.color
              : children.props.color
          }
          d={computePath()}
        />
      </Svg>
    );
  };
  
  // Compute the path string based on current values
  const computePath = () => {
    const stepY = position.x._value - MIN_LEDGE;
    const stepX = rValue._value / 2;
    const C = stepY * 0.5522847498;

    // Calculate points
    const p1x = ledgeValue._value;
    const p1y = position.y._value - 2 * stepY;
    
    const p2x = p1x + stepX;
    const p2y = p1y + stepY;
    
    const p3x = p2x + stepX;
    const p3y = p2y + stepY;
    
    const p4x = p3x - stepX;
    const p4y = p3y + stepY;
    
    const p5x = p4x - stepX;
    const p5y = p4y + stepY;

    // Construct the SVG path
    const d = [
      "M 0 0",
      `H ${p1x}`,
      `V${p1y}`,
      `C ${p1x} ${p1y + C} ${p2x} ${p2y} ${p2x} ${p2y}`,
      `C ${p2x} ${p2y} ${p3x} ${p3y - C} ${p3x} ${p3y}`,
      `C ${p3x} ${p3y + C} ${p4x} ${p4y} ${p4x} ${p4y}`,
      `C ${p4x} ${p4y} ${p5x} ${p5y - C} ${p5x} ${p5y}`,
      `V ${HEIGHT}`,
      `H 0`,
      "Z",
    ].join(" ");

    return d;
  };

  if (Platform.OS === "android") {
    return (
      <View style={StyleSheet.absoluteFill}>
        {renderPath()}
        <Animated.View style={[StyleSheet.absoluteFill, androidStyle]}>
          {children}
        </Animated.View>
      </View>
    );
  }

  // iOS implementation using MaskedView
  return (
    <MaskedView style={StyleSheet.absoluteFill} maskElement={renderPath()}>
      {children}
    </MaskedView>
  );
}

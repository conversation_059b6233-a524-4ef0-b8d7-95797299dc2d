import React, { ReactNode, useRef, useState } from 'react';
import { View, StyleSheet, Dimensions, Animated, PanResponder } from 'react-native';

const { width } = Dimensions.get('window');

interface SimpleSliderProps {
  children: ReactNode;
  index: number;
  setIndex: (index: number) => void;
  prev?: ReactNode;
  next?: ReactNode;
}

export default function SimpleSlider({ children, index, setIndex, prev, next }: SimpleSliderProps) {
  const [complete, setComplete] = useState(true);
  const pan = useRef(new Animated.Value(0)).current;

  const panResponder = useRef(
    PanResponder.create({
      onMoveShouldSetPanResponder: () => complete,
      onPanResponderGrant: () => {
        setComplete(false);
      },
      onPanResponderMove: Animated.event([null, { dx: pan }], {
        useNativeDriver: false,
      }),
      onPanResponderRelease: (_, gestureState) => {
        setComplete(true);

        // Define the velocity and distance thresholds
        const velocityThreshold = 0.3;
        const distanceThreshold = width / 3;
        
        if (
          (Math.abs(gestureState.vx) > velocityThreshold || 
           Math.abs(gestureState.dx) > distanceThreshold) &&
          gestureState.dx < 0 && 
          next
        ) {
          // Swipe left - go to next slide
          Animated.timing(pan, {
            toValue: -width,
            duration: 250,
            useNativeDriver: false,
          }).start(() => {
            pan.setValue(0);
            setIndex(index + 1);
          });
        } else if (
          (Math.abs(gestureState.vx) > velocityThreshold || 
           Math.abs(gestureState.dx) > distanceThreshold) &&
          gestureState.dx > 0 && 
          index > 0
        ) {
          // Swipe right - go to previous slide
          Animated.timing(pan, {
            toValue: width,
            duration: 250,
            useNativeDriver: false,
          }).start(() => {
            pan.setValue(0);
            setIndex(index - 1);
          });
        } else {
          // Not enough velocity/distance - bounce back
          Animated.spring(pan, {
            toValue: 0,
            useNativeDriver: false,
          }).start();
        }
      },
    })
  ).current;

  return (
    <View style={styles.container} {...panResponder.panHandlers}>
      <Animated.View
        style={[
          styles.sliderContent,
          {
            transform: [{ translateX: pan }],
          },
        ]}
      >
        <View style={styles.slide}>{children}</View>
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    overflow: 'hidden',
  },
  sliderContent: {
    flex: 1,
    flexDirection: 'row',
    width: width,
  },
  slide: {
    width: width,
  },
});
